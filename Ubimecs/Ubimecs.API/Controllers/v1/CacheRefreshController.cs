using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
using Ubimecs.Application.Contracts.Cache;

namespace Ubimecs.API.Controllers.v1
{
    /// <summary>
    /// Controller for managing cache refresh operations
    /// </summary>
    [Route("api/v1/cache/refresh")]
    [ApiController]
    [Authorize] // Protect endpoints with authorization
    public class CacheRefreshController : ControllerBase
    {
        private readonly ICacheService _cacheService;
        private readonly ILogger<CacheRefreshController> _logger;

        public CacheRefreshController(ICacheService cacheService, ILogger<CacheRefreshController> logger)
        {
            _cacheService = cacheService;
            _logger = logger;
        }

        /// <summary>
        /// Refreshes the check-in parameters cache
        /// Linked to CHK_RetrieveParametersRQ SOAP service
        /// </summary>
        /// <returns>Success or error response</returns>
        [HttpPost("checkin-parameters")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> RefreshCheckInParameters()
        {
            try
            {
                _logger.LogInformation("Cache refresh request received for check-in parameters");
                
                await _cacheService.RefreshCheckInTimeResControlAsync();
                
                _logger.LogInformation("Check-in parameters cache refreshed successfully");
                
                return Ok(new
                {
                    success = true,
                    message = "Check-in parameters cache refreshed successfully",
                    timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to refresh check-in parameters cache");
                
                return StatusCode(StatusCodes.Status500InternalServerError, new
                {
                    success = false,
                    message = "Failed to refresh check-in parameters cache",
                    error = ex.Message,
                    timestamp = DateTime.UtcNow
                });
            }
        }

        /// <summary>
        /// Refreshes the airport list cache
        /// Linked to RetrieveOandDPairsRQ SOAP service
        /// </summary>
        /// <returns>Success or error response</returns>
        [HttpPost("airport-list")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> RefreshAirportList()
        {
            try
            {
                _logger.LogInformation("Cache refresh request received for airport list");
                
                await _cacheService.RefreshAirportListAsync();
                
                _logger.LogInformation("Airport list cache refreshed successfully");
                
                return Ok(new
                {
                    success = true,
                    message = "Airport list cache refreshed successfully",
                    timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to refresh airport list cache");
                
                return StatusCode(StatusCodes.Status500InternalServerError, new
                {
                    success = false,
                    message = "Failed to refresh airport list cache",
                    error = ex.Message,
                    timestamp = DateTime.UtcNow
                });
            }
        }

        /// <summary>
        /// Refreshes all cache data (check-in parameters and airport list)
        /// </summary>
        /// <returns>Success or error response</returns>
        [HttpPost("all")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> RefreshAllCache()
        {
            try
            {
                _logger.LogInformation("Cache refresh request received for all cache data");
                
                // Execute both refresh operations sequentially
                await _cacheService.RefreshCheckInTimeResControlAsync();
                await _cacheService.RefreshAirportListAsync();
                
                _logger.LogInformation("All cache data refreshed successfully");
                
                return Ok(new
                {
                    success = true,
                    message = "All cache data refreshed successfully",
                    refreshedCaches = new[] { "check-in parameters", "airport list" },
                    timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to refresh all cache data");
                
                return StatusCode(StatusCodes.Status500InternalServerError, new
                {
                    success = false,
                    message = "Failed to refresh all cache data",
                    error = ex.Message,
                    timestamp = DateTime.UtcNow
                });
            }
        }
    }
}
