using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Ubimecs.Application.Features.Session;
using Ubimecs.Infrastructure.Models.Request;
using Ubimecs.Infrastructure.Models.Response;

namespace Ubimecs.API.Controllers.v1
{
    [Route("api/v1/[controller]/[action]/{id}")]
    [ApiController]
    public class SessionController : ControllerBase
    {
        [HttpPost]
        public ServiceResponse GetSession(MainRequest request)
        {
            return new SessionGetService(request).Work();
        }
    }
}
