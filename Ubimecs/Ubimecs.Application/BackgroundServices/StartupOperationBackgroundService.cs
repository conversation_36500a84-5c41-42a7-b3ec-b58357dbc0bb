using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Ubimecs.Application.Features.Cache;
using Ubimecs.Application.Providers;
using Ubimecs.IBS.Requesters;
using Ubimecs.Infrastructure.Caching;
using Ubimecs.Infrastructure.Database.DatabaseContext;
using Ubimecs.Infrastructure.Models.Common;

namespace Ubimecs.Application.BackgroundServices
{
    public class StartupOperationBackgroundService : BackgroundService
    {
        private readonly IServiceScopeFactory _serviceScopeFactory;
        public StartupOperationBackgroundService(IServiceScopeFactory serviceScopeFactory)
        {
            _serviceScopeFactory = serviceScopeFactory;
        }
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            using (var scope = _serviceScopeFactory.CreateScope())
            {
                var _torcDbContext = scope.ServiceProvider.GetRequiredService<TorcDatabaseDbContext>();
                var _userDbContext = scope.ServiceProvider.GetRequiredService<UserDbContext>();
                var _ubimecsDbContext = scope.ServiceProvider.GetRequiredService<UbimecsDbContext>();


                var entityDataCacher = new EntityDataCacher(_ubimecsDbContext, _userDbContext,_torcDbContext);
                entityDataCacher.GetConfigsList();

                SessionCache session = new SessionCache("", LanguageEnum.Turkish);
                var provider = ProviderFactory.GetProvider(GeneralConstants.UBIMECS_PROVIDER_TYPE, session,"Startup");


                await Task.Run(() => new DataCacher(provider, _torcDbContext, _userDbContext, _ubimecsDbContext).CacheAll());
            }            
        }
    }
}