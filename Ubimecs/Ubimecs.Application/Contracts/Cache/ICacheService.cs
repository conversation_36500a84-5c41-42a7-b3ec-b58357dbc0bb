using System.Threading.Tasks;

namespace Ubimecs.Application.Contracts.Cache
{
    /// <summary>
    /// Service interface for managing cache refresh operations
    /// </summary>
    public interface ICacheService
    {
        /// <summary>
        /// Refreshes the check-in time restriction control cache data
        /// This method is linked to CHK_RetrieveParametersRQ SOAP service
        /// </summary>
        /// <returns>A task representing the asynchronous operation</returns>
        Task RefreshCheckInTimeResControlAsync();

        /// <summary>
        /// Refreshes the airport list cache data
        /// This method is linked to RetrieveOandDPairsRQ SOAP service
        /// </summary>
        /// <returns>A task representing the asynchronous operation</returns>
        Task RefreshAirportListAsync();
    }
}
