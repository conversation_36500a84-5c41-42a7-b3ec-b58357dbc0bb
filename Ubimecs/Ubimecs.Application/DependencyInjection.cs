using Microsoft.Extensions.DependencyInjection;
using Ubimecs.Application.Contracts.Auth;
using Ubimecs.Application.Contracts.Cache;
using Ubimecs.Application.Contracts.File;
using Ubimecs.Application.Contracts.Utility;
using Ubimecs.Application.Features.Auth;
using Ubimecs.Application.Features.Cache;
using Ubimecs.Application.Features.File;
using Ubimecs.Application.Features.Utility;

namespace Ubimecs.Application
{
    public static class DependencyInjection
    {
        public static IServiceCollection AddApplication(this IServiceCollection services)
        {
            services.AddScoped<IUtilityService, UtilityService>();
            services.AddScoped<IAuthService, AuthService>();
            services.AddScoped<IExcelService, ExcelService>();
            services.AddScoped<ICacheService, CacheService>();

            return services;
        }
    }
}
