using Ubimecs.Application.Common;
using Ubimecs.Infrastructure.Models.Common;
using Ubimecs.Infrastructure.Models.Request;
using Ubimecs.Infrastructure.Models.Request.Agency;
using Ubimecs.Infrastructure.Models.Response.Agency;

namespace Ubimecs.Application.Features.Agency;

public class AgencyGetGroupBookingsService : BaseService<GetGroupBookingsRequest, GetGroupBookingsResponse>
{
    public AgencyGetGroupBookingsService(MainRequest request) : base(request)
    {
    }

    internal override void ValidationControl()
    {
        if (string.IsNullOrEmpty(Session.AgencyInfo?.AgencyCode))
        {
            throw new TmobException("Agency information is missing");
        }
    }

    internal override void FlowOperations()
    {

    }

    internal override void InternalWork()
    {
        ResponseData = ServiceProvider.GetGroupBookings(RequestData);
    }
} 