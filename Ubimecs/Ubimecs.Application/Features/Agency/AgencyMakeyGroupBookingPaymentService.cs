using Ubimecs.Application.Common;
using Ubimecs.Infrastructure.Models.Common;
using Ubimecs.Infrastructure.Models.Request;
using Ubimecs.Infrastructure.Models.Request.Agency;
using Ubimecs.Infrastructure.Models.Response;

namespace Ubimecs.Application.Features.Agency;

public class AgencyMakeGroupBookingPaymentService : BaseService<MakeGroupBookingPaymentRequest, GetPnrInfoResponse>
{
    public AgencyMakeGroupBookingPaymentService(MainRequest request) : base(request)
    {
    }

    internal override void ValidationControl()
    {
        if (string.IsNullOrEmpty(Session.AgencyInfo?.AgencyCode))
        {
            throw new TmobException("Agency information is missing");
        }
    }

    internal override void FlowOperations()
    {

    }

    internal override void InternalWork()
    {
        ResponseData = ServiceProvider.MakeGroupBookingPayment(RequestData);
    }
}