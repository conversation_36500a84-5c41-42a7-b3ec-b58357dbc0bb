using Ubimecs.Application.Common;
using Ubimecs.Infrastructure.CMS.Contracts;
using Ubimecs.Infrastructure.Models.Configuration;
using Ubimecs.Infrastructure.Models.Request;
using Ubimecs.Infrastructure.Models.Request.Agency;
using Ubimecs.Infrastructure.Models.Response.Agency;
using Ubimecs.Infrastructure.Models.Response.Pagination;

namespace Ubimecs.Application.Features.Agency
{
    public class ListTravelAgentsService : BaseService<ListTravelAgentsRequest, PaginatedResponse<TravelAgentDetail>>
    {
        private readonly PaginationSettings _paginationSettings; 
        private readonly IAgencyService _agencyService;

        public ListTravelAgentsService(MainRequest request, PaginationSettings paginationSettings, IAgencyService agencyService) : base(request)
        {
            _paginationSettings = paginationSettings;
            _agencyService = agencyService;
        }

        internal override void ValidationControl()
        {

        }

        internal override void InternalWork()
        {
            
        }

        internal override void FlowOperations()
        {

        }
        
        internal override async Task InternalWorkAsync()
        {
            ResponseData = await ServiceProvider.ListTravelAgents(_agencyService,RequestData, _paginationSettings);
        }
    }

}
