using Ubimecs.Application.Common;
using Ubimecs.Application.Features.Cache;
using Ubimecs.Infrastructure.Database.DatabaseContext;
using Ubimecs.Infrastructure.FileService.Contracts;
using Ubimecs.Infrastructure.Models.Common;
using Ubimecs.Infrastructure.Models.Request;
using Ubimecs.Infrastructure.Models.Request.Agency;
using Ubimecs.Infrastructure.Models.Response.Agency;

namespace Ubimecs.Application.Features.Agency
{
    public class UpdateAgencyProfileService : BaseService<UpdateAgencyProfileRequest, UpdateAgencyProfileResponse>
    {
        private readonly IFileStorageService _fileStorageService;
        private readonly UbimecsDbContext _dbContext;

        public UpdateAgencyProfileService(MainRequest request, IFileStorageService fileStorageService,UbimecsDbContext dbContext) : base(request)
        {
            _fileStorageService = fileStorageService;
            _dbContext = dbContext;
        }

        internal override void ValidationControl()
        {

        }

        internal override void FlowOperations() { }

        internal override void InternalWork()
        {
            ResponseData = ServiceProvider.UpdateAgencyProfile(RequestData,_fileStorageService,_dbContext);
            
        }
    }
}
