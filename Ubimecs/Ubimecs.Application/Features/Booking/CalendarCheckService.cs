using Ubimecs.Application.Common;
using Ubimecs.Infrastructure.Models.Common;
using Ubimecs.Infrastructure.Models.Flow;
using Ubimecs.Infrastructure.Models.Request;
using Ubimecs.Infrastructure.Models.Response;

namespace Ubimecs.Application.Features.Booking
{
    public class CalendarCheckService : BaseService<CheckCalendarRequest, CalendarCheckResponse>
    {
        public CalendarCheckService(MainRequest request) : base(request)
        {

        }

        internal override void ValidationControl()
        {

        }

        internal override void FlowOperations()
        {
            if (Session.CurrentFlow.Type != FlowType.Rebooking)
            {
                if (Session.CurrentFlow?.Type != FlowType.Booking)
                {
                    Session.StartNewFlow(FlowType.Booking);
                }
                else
                {
                    Session.ResetCurrentFlow();
                }
            }
            else
            {
                if (RequestData.IsBooking)
                {
                    Session.StartNewFlow(FlowType.Booking);
                }
            }
        }

        internal override void InternalWork()
        {
            var response = ServiceProvider.GetCalendarAvailability(GetFlightsRequest.GetCalendarCheckRequest(RequestData.DepartureAirport, RequestData.ArrivalAirport));

            if (RequestData.PassengerTypes != null)
            {
                ResponseData.AvailableDays = response.Where(w => w.Value >= RequestData.PassengerTypes.Where(s => s.Key != PassengerTypeEnum.INF.ToString()).Sum(s => s.Value)).Select(s => s.Key).ToList();
            }
            else
            {
                ResponseData.AvailableDays = response.Where(w => w.Value >= 0).Select(s => s.Key).ToList();
            }

            if (!RequestData.IsBooking)
            {
                if (Session.CurrentFlow.Type == FlowType.Rebooking)
                {
                    if (!string.IsNullOrEmpty(RequestData.TmobId))
                    {
                        var flights = Session.CurrentFlow.PNR.Flights.Where(t => t.State != OfferRebookingStateEnum.Added).ToList();
                        var flight = flights.FirstOrDefault(f => f.TmobId == RequestData.TmobId && f.State != OfferRebookingStateEnum.Added);
                        var index = flights.IndexOf(flight);

                        if (index == 0)
                        {
                            if (flights.Count >= 2)
                            {
                                var secondFlightDeparture = flights[1].Segments.First().DepartureDate;

                                ResponseData.AvailableDays.RemoveAll(r => r >= secondFlightDeparture.Date);

                            }
                        }
                        else if (index == flights.Count - 1)
                        {
                            var secondToLastFlightDeparture = flights[index - 1].Segments.First().DepartureDate;

                            ResponseData.AvailableDays.RemoveAll(r => r <= secondToLastFlightDeparture);
                        }
                        else
                        {
                            if (flights.Count >= 3)
                            {
                                var beforeFlightDeparture = flights[index - 1].Segments.First().DepartureDate;
                                var afterFlightDeparture = flights[index + 1].Segments.First().DepartureDate;

                                ResponseData.AvailableDays.RemoveAll(r => r <= beforeFlightDeparture.Date || r >= afterFlightDeparture.Date);
                            }
                        }
                    }
                    else
                    {
                        var flights = Session.CurrentFlow.PNR.Flights;
                        var lastFlight = flights.LastOrDefault();

                        var lastFlightDeparture = lastFlight.Segments.First().DepartureDate;

                        ResponseData.AvailableDays.RemoveAll(r => r <= lastFlightDeparture.Date);
                    }
                }
            }
        }
    }
}
