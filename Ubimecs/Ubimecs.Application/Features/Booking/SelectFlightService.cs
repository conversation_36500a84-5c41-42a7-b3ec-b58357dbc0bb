using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Ubimecs.Application.Common;
using Ubimecs.Infrastructure.Models.Common;
using Ubimecs.Infrastructure.Models.Request;
using Ubimecs.Infrastructure.Models.Response;

namespace Ubimecs.Application.Features.Booking
{
    public class SelectFlightService : BaseService<FlightSelectRequest, FlightSelectResponse>
    {
        public SelectFlightService(MainRequest request) : base(request, true)
        {

        }
        internal override void ValidationControl()
        {
            var passengers = Session.CurrentFlow.PNR.Passengers;
            var infantCount = passengers.Count(x=> x.PassengerType == PassengerTypeEnum.INF);
            var adultCount = passengers.Count(x => x.PassengerType == PassengerTypeEnum.ADT);
            if (infantCount > adultCount)
            {
                throw new TmobException("Infant count can not be bigger than adults.");
            }

        }

        internal override void FlowOperations()
        {
        }

        internal override void InternalWork()
        {

            ResponseData = ServiceProvider.BookingSelectFlight(RequestData);
        }
    }
}
