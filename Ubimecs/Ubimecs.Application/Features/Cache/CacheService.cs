using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Ubimecs.Application.Contracts.Cache;
using Ubimecs.Application.Providers;
using Ubimecs.Infrastructure.Caching;
using Ubimecs.Infrastructure.Caching.Models;
using Ubimecs.Infrastructure.Database.DatabaseContext;
using Ubimecs.Infrastructure.Logging;
using Ubimecs.Infrastructure.Models.Common;
using Ubimecs.Infrastructure.Models.DTO;
using Ubimecs.Infrastructure.Models.Request;
using Ubimecs.Infrastructure.Providers;

namespace Ubimecs.Application.Features.Cache
{
    /// <summary>
    /// Service for managing cache refresh operations
    /// </summary>
    public class CacheService : ICacheService
    {
        private readonly IServiceScopeFactory _serviceScopeFactory;
        private readonly ILogger<CacheService> _logger;

        public CacheService(IServiceScopeFactory serviceScopeFactory, ILogger<CacheService> logger)
        {
            _serviceScopeFactory = serviceScopeFactory;
            _logger = logger;
        }

        /// <summary>
        /// Refreshes the check-in time restriction control cache data asynchronously
        /// </summary>
        public async Task RefreshCheckInTimeResControlAsync()
        {
            try
            {
                _logger.LogInformation("Starting refresh of CheckInTimeResControl cache");

                await Task.Run(() =>
                {
                    using var scope = _serviceScopeFactory.CreateScope();
                    var serviceProvider = CreateServiceProvider(scope);
                    
                    var getCheckInIFlyResControl = serviceProvider.CheckInIFlyResControl(
                        CheckInIFlyResControlRequest.GetIFlyResControlRequest());
                    
                    CacheManager.Instance.Set<Dictionary<string, string>>(
                        CacheDataKeys.CheckInTimeResControl, 
                        getCheckInIFlyResControl.TimeResValues);

                    CacheManager.Instance.Set<List<CheckInTimeResControlDTO>>(
                        CacheDataKeys.CheckInTimeResControl_V2, 
                        getCheckInIFlyResControl.TimeResValues_V2);
                });

                _logger.LogInformation("Successfully refreshed CheckInTimeResControl cache");
                Logger.Instance.FileLog("CACHE_SERVICE", "CHECKIN_TIME_RES_CONTROL", "REFRESH COMPLETED SUCCESSFULLY");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error refreshing CheckInTimeResControl cache");
                Logger.Instance.ErrorLog("CACHE_SERVICE", "CHECKIN_TIME_RES_CONTROL", ex);
                throw;
            }
        }

        /// <summary>
        /// Refreshes the airport list cache data asynchronously
        /// </summary>
        public async Task RefreshAirportListAsync()
        {
            try
            {
                _logger.LogInformation("Starting refresh of AirportList cache");

                await Task.Run(() =>
                {
                    using var scope = _serviceScopeFactory.CreateScope();
                    var serviceProvider = CreateServiceProvider(scope);
                    
                    var airportResponse = serviceProvider.GetAirportList();

                    // Process airport data with localized names
                    foreach (var item in airportResponse.Airports)
                    {
                        foreach (var language in Enum.GetValues(typeof(LanguageEnum)).Cast<LanguageEnum>())
                        {
                            var CountryList = CachedData.GetCountries((int)language);
                            var CityList = CachedData.GetCities((int)language);

                            item.CountryNames[(int)language] = CountryList.ContainsKey(item.CountryCode) 
                                ? CountryList[item.CountryCode] 
                                : item.Country;
                            
                            item.CityNames[(int)language] = CityList.ContainsKey(item.AirportCode) 
                                ? CityList[item.AirportCode] 
                                : item.CityName;

                            var airportCodeNameDic = airportResponse.Airports.ToDictionary(k => k.AirportCode, v => v.CityName);

                            item.DepartureList = item.DepartureList
                                .OrderBy(o => airportCodeNameDic.ContainsKey(o) ? airportCodeNameDic[o] : o)
                                .ToList();
                        }
                    }

                    CacheManager.Instance.Set(CacheDataKeys.AirPorts, airportResponse);
                });

                _logger.LogInformation("Successfully refreshed AirportList cache");
                Logger.Instance.FileLog("CACHE_SERVICE", "AIRPORT_LIST", "REFRESH COMPLETED SUCCESSFULLY");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error refreshing AirportList cache");
                Logger.Instance.ErrorLog("CACHE_SERVICE", "AIRPORT_LIST", ex);
                throw;
            }
        }

        /// <summary>
        /// Creates a service provider instance with the required dependencies
        /// </summary>
        private IUbimecsServiceProvider CreateServiceProvider(IServiceScope scope)
        {
            var torcDbContext = scope.ServiceProvider.GetRequiredService<TorcDatabaseDbContext>();
            var userDbContext = scope.ServiceProvider.GetRequiredService<UserDbContext>();
            var ubimecsDbContext = scope.ServiceProvider.GetRequiredService<UbimecsDbContext>();

            SessionCache session = new SessionCache("", LanguageEnum.Turkish);
            return ProviderFactory.GetProvider(GeneralConstants.UBIMECS_PROVIDER_TYPE, session, "CacheService");
        }
    }
}
