using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Ubimecs.Infrastructure.Caching.Models;
using Ubimecs.Infrastructure.Caching;
using Ubimecs.Infrastructure.Database.DatabaseContext;
using Ubimecs.Domain.Entities.UbimecsEntities;
using Ubimecs.Infrastructure.Models.Common;
using Ubimecs.Infrastructure.Models.DTO;
using Ubimecs.Domain.Entities.UserEntities;
using Ubimecs.Domain.Entities.TorcEntities.ProcedureEntities;

namespace Ubimecs.Application.Features.Cache
{
    public class EntityDataCacher
    {
        private readonly UbimecsDbContext _ubimecsDbContext;
        private readonly UserDbContext _userDbContext;  
        private readonly TorcDatabaseDbContext _torcDbContext;
        public EntityDataCacher(UbimecsDbContext ubimecsDbContext, UserDbContext userDbContext, TorcDatabaseDbContext torcDbContext)
        {
            _ubimecsDbContext = ubimecsDbContext;
            _userDbContext = userDbContext;
            _torcDbContext = torcDbContext;
        }
        public void GetCurrencyCodes()
        {
            Dictionary<string, string> currencyCodeListForCountry = new Dictionary<string, string>();


            var CurrencyTypes = _ubimecsDbContext.CurrencyType.ToList();

            foreach (var item in CurrencyTypes)
            {
                if (item != null && item.CurrencyCode != null && item.CountryCode != null)
                {
                    if (!currencyCodeListForCountry.ContainsKey(item.CurrencyCode))
                        currencyCodeListForCountry.Add(item.CurrencyCode, item.CountryCode);
                }
            }

            CacheManager.Instance.Set<Dictionary<string, string>>(CacheDataKeys.CurrencyCodes, currencyCodeListForCountry);
        }

        public void GetConfigsList()
        {
            Dictionary<string, string> configList = new Dictionary<string, string>();


            var _settingsList = _ubimecsDbContext.Configs.ToList();

            foreach (var item in _settingsList)
            {
                if (item != null && item.Name != null && item.Value != null)
                {
                    if (!configList.ContainsKey(item.Name))
                        configList.Add(item.Name, item.Value);
                }
            }

            CacheManager.Instance.Set(CacheDataKeys.Configs, configList);
        }
        public void GetLanguageList()
        {
            var LanguageList = _torcDbContext.Language.Where(x => x.IsClientActive == true).ToList();

            CacheManager.Instance.Set(CacheDataKeys.LanguageList, LanguageList.Select(s => new LanguageDTO
            {
                Id = s.LId,
                CultureName = s.CultureSettingKey.ToUpper(),
                Name = s.Name
            }).ToList());

        }
        public void GetPageSettingsList()
        {
            List<PageSetting> pageSettingsList = new List<PageSetting>();

            try
            {
                pageSettingsList = _ubimecsDbContext.PageSettings.ToList();
            }
            catch (Exception)
            {
                pageSettingsList = new List<PageSetting>();
            }


            CacheManager.Instance.Set<List<PageSetting>>(CacheDataKeys.PageSettings, pageSettingsList);
        }
        public void GetCountryList()
        {
            List<Country> countryList = new List<Country>();

            var _countryList = _userDbContext.Country.ToList();

            foreach (var item in _countryList)
            {
                if (item != null && item.Name != null && item.TwoLetterIsoCode != null && item.ThreeLetterIsoCode != null)
                {
                    if (!countryList.Any(t => t.Name == item.Name))
                    {
                        countryList.Add(item);
                    }
                }
            }

            CacheManager.Instance.Set<List<Country>>(CacheDataKeys.Countries, countryList);
        }
        public void GetServiceCategories()
        {
            Dictionary<string, ServiceCategoryEnum> ssrCategoryList = new Dictionary<string, ServiceCategoryEnum>();

            var serviceList = _ubimecsDbContext.IbsService.ToList();

            foreach (var item in serviceList)
            {

                if (!ssrCategoryList.ContainsKey(item.Name))
                    ssrCategoryList.Add(item.Name, (ServiceCategoryEnum)item.CategoryId);

            }

            CacheManager.Instance.Set(CacheDataKeys.ServiceCodeCategories, ssrCategoryList);
        }
        public void GetSecurePayment()
        {
            Dictionary<string, object> securePayment = new Dictionary<string, object>();

            //Eğer ki SecurePayment yapılacak ise onun ile alakalı olan bilgileri bu alanda veritabanından çekilecek...

            CacheManager.Instance.Set<Dictionary<string, object>>(CacheDataKeys.SecurePayment, securePayment);
        }
        public void GetOrderChannels()
        {
            List<OrderChannel> orderChannels;

            orderChannels = _ubimecsDbContext.OrderChannel.ToList();

            CacheManager.Instance.Set<List<OrderChannel>>(CacheDataKeys.OrderChannel, orderChannels);
        }
        public void GetPassengerTypes()
        {
            List<PassengerType> passangerType;

            passangerType = _ubimecsDbContext.PassengerType.ToList();

            CacheManager.Instance.Set<List<PassengerType>>(CacheDataKeys.PassengerType, passangerType);
        }
        public void GetPlcCountries()
        {
            List<PlcCountry> plcCountryList;

            plcCountryList = _ubimecsDbContext.PlcCountries.ToList();

            CacheManager.Instance.Set<List<PlcCountry>>(CacheDataKeys.PlcCountries, plcCountryList);
        }
        public void GetBINList()
        {
            List<BINList> binList;

            binList = _ubimecsDbContext.BINList.ToList();

            CacheManager.Instance.Set<List<BINList>>(CacheDataKeys.BINList, binList);
        }
        public void GetAppText()
        {
            List<AppText> appTextList;

            try
            {
                appTextList = _ubimecsDbContext.AppText.ToList();
            }
            catch (Exception)
            {
                appTextList = new List<AppText>();
            }
            CacheManager.Instance.Set<List<AppText>>(CacheDataKeys.AppText, appTextList);

        }

        public void GetCmsValues()
        {
            List<uSP_SelectAllClientTexts_Result> cmsKeyList;
            int lastVersionNumber = 0;

            try
            {
                cmsKeyList = _torcDbContext.uSP_SelectAllClientTexts().ToList();
            }
            catch (Exception ex)
            {
                cmsKeyList = new List<uSP_SelectAllClientTexts_Result>();
            }

            try
            {
                lastVersionNumber = (int)cmsKeyList.OrderByDescending(t => t.ClientKeyVersionNumber)?.FirstOrDefault()?.ClientKeyVersionNumber;
            }
            catch (Exception)
            {
            }

            if (lastVersionNumber != 0)
            {
                CacheManager.Instance.Set(CacheDataKeys.LastCMSVersion, lastVersionNumber);
            }

            var data = cmsKeyList.GroupBy(g => g.LanguageID.GetValueOrDefault()).ToDictionary(k => k.Key, v => v.GroupBy(g => g.Key).ToDictionary(kk => kk.Key, kv => kv.First()));

            CacheManager.Instance.Set(CacheDataKeys.CmsKeys, data);

            foreach (var lang in data)
            {
                foreach (var key in data[lang.Key])
                {
                    CacheManager.Instance.Set(CacheDataKeys.GetCMsKeyName(key.Key, lang.Key), data[lang.Key][key.Key]);
                }
            }

        }

        public void GetCmsValuesUpdateControl()
        {
            List<uSP_SelectAllClientTexts_Result> cmsKeyList;
            try
            {
                cmsKeyList = _torcDbContext.uSP_SelectAllClientTexts().ToList();
                var lastVersionNumber = cmsKeyList.OrderByDescending(t => t.ClientKeyVersionNumber).First().ClientKeyVersionNumber;
                if (CachedData.LastCMSVersion != lastVersionNumber)
                {
                    GetCmsValues();
                }
            }
            catch (Exception)
            {
            }

        }

        public void GetThirdPartyErrorCodes()
        {
            List<ThirdPartyErrorCode> errorCodes;

            try
            {
                errorCodes = _ubimecsDbContext.ThirdPartyErrorCodes.ToList();
            }
            catch (Exception)
            {
                errorCodes = new List<ThirdPartyErrorCode>();
            }
            CacheManager.Instance.Set(CacheDataKeys.ThirdPartyErrors, errorCodes.GroupBy(g => g.Language ?? 0).ToDictionary(k => k.Key, v => v.ToList()));

        }

        public void GetErrorCodes()
        {
            List<ErrorCode> errorCodes;

            try
            {
                errorCodes = _ubimecsDbContext.ErrorCodes.ToList();
            }
            catch (Exception)
            {
                errorCodes = new List<ErrorCode>();
            }
            CacheManager.Instance.Set(CacheDataKeys.ErrorCodes,errorCodes);

        }
        public void GetTresholds()
        {
            List<Treshold> tresholds;
            try
            {
                tresholds = _ubimecsDbContext.Treshold.ToList();
            }
            catch (Exception)
            {
                tresholds = new List<Treshold>();
            }
            CacheManager.Instance.Set(CacheDataKeys.Tresholds, tresholds);
        } 
        
        public void GetChannelConfigurations()
        {
            List<ChannelConfiguration> channelConfigurations;
            try
            {
                channelConfigurations = _ubimecsDbContext.ChannelConfigurations.ToList();
            }
            catch (Exception)
            {
                channelConfigurations = new List<ChannelConfiguration>();
            }
            CacheManager.Instance.Set(CacheDataKeys.ChannelConfigurations, channelConfigurations);
        }
    }
}
