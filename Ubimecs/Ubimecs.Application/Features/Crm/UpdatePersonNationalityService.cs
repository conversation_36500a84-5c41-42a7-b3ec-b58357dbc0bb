using Ubimecs.Application.Common;
using Ubimecs.Infrastructure.CRM.Contracts;
using Ubimecs.Infrastructure.CRM.Models.Next4Biz.Requests.Dto;
using Ubimecs.Infrastructure.CRM.Models.Next4Biz.Responses;
using Ubimecs.Infrastructure.Models.Request;

namespace Ubimecs.Application.Features.Crm;

public class UpdatePersonNationalityService: BaseService<UpdatePersonNationalityRequestDto,UpdatePersonNationalityResponse>
{
    private readonly ICrmService _crmService;

    public UpdatePersonNationalityService(MainRequest request, ICrmService crmService) : base(request)
    {
        _crmService = crmService;
    }

    internal override void FlowOperations()
    {
    }

    internal override void ValidationControl()
    {
        
    }

    internal override void InternalWork()
    {
        
    }

    internal override async Task InternalWorkAsync()
    {
        ResponseData = await ServiceProvider.UpdatePersonNationality(_crmService, RequestData);
    }
}