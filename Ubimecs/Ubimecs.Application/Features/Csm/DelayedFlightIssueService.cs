using Ubimecs.Application.Common;
using Ubimecs.Infrastructure.CSM.Contracts;
using Ubimecs.Infrastructure.CSM.Models.Next4Biz.Requests;
using Ubimecs.Infrastructure.CSM.Models.Next4Biz.Responses;
using Ubimecs.Infrastructure.Models.Request;

namespace Ubimecs.Application.Features.Csm;

public class DelayedFlightIssueService: BaseService<DelayedFlightIssueRequest,CreateIssueResponse>
{
    private readonly ICsmService _csmService;

    public DelayedFlightIssueService(MainRequest request, ICsmService csmService) : base(request)
    {
        _csmService = csmService;
    }

    internal override void FlowOperations()
    {
        
    }

    internal override void ValidationControl()
    {
        
    }

    internal override void InternalWork()
    {
        ResponseData = ServiceProvider.CreateDelayedFlightIssue(_csmService,RequestData);
    }
}