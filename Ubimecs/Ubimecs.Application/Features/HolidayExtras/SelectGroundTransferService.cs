using Ubimecs.Application.Common;
using Ubimecs.Infrastructure.Models.DTO.GroundTransfer;
using Ubimecs.Infrastructure.Models.Request;

namespace Ubimecs.Application.Features.HolidayExtras;

public class SelectGroundTransferService: BaseService<SelectGroundTransferRequest,List<SessionGroundTransferDTO>>
{
    public SelectGroundTransferService(MainRequest request): base(request)
    {
        
    }
    
    internal override void ValidationControl()
    {
        
    }

    internal override void FlowOperations()
    {
        Session.CurrentFlow.PNR.GroundTransfers.RemoveAll(x => x.SegmentTmobId == RequestData.SegmentTmobId);
        
        Session.CurrentFlow.PNR.GroundTransfers.Add(new SessionGroundTransferDTO
        {
            Code = RequestData.Code,
            SegmentTmobId = RequestData.SegmentTmobId
        });
    }

    internal override void InternalWork()
    {
        ResponseData = Session.CurrentFlow.PNR.GroundTransfers.ToList();
    }
}