using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Ubimecs.Application.Common;
using Ubimecs.Infrastructure.Models.Request;
using Ubimecs.Infrastructure.Models.Response;

namespace Ubimecs.Application.Features.Loyalty
{
    public class EditUserService : BaseService<EditMemberRequest, RegisterResponse>
    {
        public EditUserService(MainRequest request) : base(request)
        {
        }

        internal override void FlowOperations()
        {
        }

        internal override void InternalWork()
        {
            ResponseData = ServiceProvider.EditUser(RequestData);
        }

        internal override void ValidationControl()
        {
        }
    }
}
