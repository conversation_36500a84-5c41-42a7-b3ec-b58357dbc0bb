using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Ubimecs.Application.Common;
using Ubimecs.Infrastructure.Models.DTO.Passenger;
using Ubimecs.Infrastructure.Models.Request;
using Ubimecs.Infrastructure.Models.Response;

namespace Ubimecs.Application.Features.Rebooking
{
    public class AddGroupProfileService : BaseService<AddGroupProfileRequest, List<PassengerInformationDTO>>
    {
        public AddGroupProfileService(MainRequest request) : base(request)
        {
        }

        internal override void FlowOperations()
        {

        }

        internal override void InternalWork()
        {
            RequestData.GroupProfiles.ForEach((groupProfile) =>
            {
                string givenName = groupProfile.Name.ToUpperInvariant();
                string surname = groupProfile.Surname.ToUpperInvariant();
                var passenger = Session.CurrentFlow.PNR.GroupPassengers.FirstOrDefault(x => x.GivenName == givenName &&
                                                                                            x.Surname == surname &&
                                                                                            x.ETicketNumber == groupProfile.ETKT);

                GetPnrInfoResponse response = null;
                try
                {
                    response = ServiceProvider.SearchGuest(new SearchGuestRequest { FirstName = givenName, LastName = surname, PnrNumber = groupProfile.ETKT }, false);
                    //TODO: farklı pnr lı gelme durumu konusulacak! (S.Y)
                }
                catch (Exception ex)
                {

                    response = null;
                }
                finally
                {
                    if (response != null)
                    {
                        if (passenger == null)
                        {
                            response.PassengerList = response.PassengerList ?? new List<PassengerInformationDTO>();
                            var passengerInfo = response.PassengerList.FirstOrDefault(x => x.GivenName == givenName && x.Surname == surname && x.ETicketNumber == groupProfile.ETKT);
                            if (passengerInfo != null)
                            {
                                Session.CurrentFlow.PNR.GroupPassengers.Add(passengerInfo);
                            }
                        }
                    }
                    else
                    {
                        if (passenger != null)
                        {
                            Session.CurrentFlow.PNR.GroupPassengers.Remove(passenger);
                        }
                    }
                }

            });

            ResponseData = Session.CurrentFlow.PNR.GroupPassengers.ToList();
        }

        internal override void ValidationControl()
        {

        }
    }
}
