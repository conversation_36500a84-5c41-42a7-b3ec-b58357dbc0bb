using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Ubimecs.Application.Common;
using Ubimecs.Infrastructure.Models.Request;
using Ubimecs.Infrastructure.Models.Response;

namespace Ubimecs.Application.Features.Rebooking
{
    public class RebookingChangeCurrencyService : BaseService<BookingChangeCurrencyRequest, ChangeCurrencyResponse>
    {
        public RebookingChangeCurrencyService(MainRequest request) : base(request)
        {
        }

        internal override void FlowOperations()
        {
        }

        internal override void InternalWork()
        {
            Session.CurrentFlow.PNR.Currency = RequestData.Currency;
            ResponseData = ServiceProvider.RebookingChangeCurrency();
        }

        internal override void ValidationControl()
        {
        }
    }
}
