using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Ubimecs.Application.Common;
using Ubimecs.Infrastructure.Models.DTO.Ife;
using Ubimecs.Infrastructure.Models.Request;
using Ubimecs.Infrastructure.Models.Response;

namespace Ubimecs.Application.Features.Rebooking
{
    public class RebookingSelectIfeService : BaseService<SelectIfeRequest, BasePriceResponse>
    {
        public RebookingSelectIfeService(MainRequest request) : base(request)
        {
        }

        internal override void FlowOperations()
        {
            //Buradaki TmobId Segmente air olan TmobId'yi temsil etmektedir.
            var ifes = Session.CurrentFlow.PNR.IfeServices.FirstOrDefault(f => f.TmobId == RequestData.TmobId);
            if (ifes == null)
            {
                var newIfe = new PassengerFlightIfe();
                if (RequestData.PassengerIds != null && RequestData.PassengerIds.Count > 0)
                {
                    newIfe.TmobId = RequestData.TmobId;
                    foreach (var passenger in RequestData.PassengerIds)
                    {
                        var newPassInfo = new PassengerFlightIfe.PassengerInfo { PassengerId = passenger };
                        if (newIfe.Ifes == null)
                        {
                            var newIfes = new List<PassengerFlightIfe.PassengerInfo>();
                            newIfe.Ifes = newIfes;
                        }
                        newIfe.Ifes.Add(newPassInfo);
                    }
                    Session.CurrentFlow.PNR.IfeServices.Add(newIfe);
                }
            }
            else
            {
                var currentPasengers = ifes.Ifes.Select(t => t.PassengerId).ToList();
                var deletingPassengersIfes = ifes.Ifes.Where(t => !RequestData.PassengerIds.Contains(t.PassengerId)).ToList();
                var addingPassengersIfes = RequestData.PassengerIds.Where(t => !currentPasengers.Contains(t)).ToList();
                if (deletingPassengersIfes != null && deletingPassengersIfes.Count > 0)
                {
                    foreach (var passenger in deletingPassengersIfes)
                    {
                        ifes.Ifes.Remove(passenger);
                    }
                }
                if (addingPassengersIfes != null && addingPassengersIfes.Count > 0)
                {
                    foreach (var passengerId in addingPassengersIfes)
                    {
                        ifes.Ifes.Add(new PassengerFlightIfe.PassengerInfo
                        {
                            PassengerId = passengerId
                        });
                    }
                }
            }
        }

        internal override void InternalWork()
        {
            ResponseData = ServiceProvider.RebookingSelectIfe(RequestData);
        }

        internal override void ValidationControl()
        {
        }
    }
}
