using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Ubimecs.Application.Common;
using Ubimecs.Infrastructure.Models.DTO.Meal;
using Ubimecs.Infrastructure.Models.Request;
using Ubimecs.Infrastructure.Models.Response;

namespace Ubimecs.Application.Features.Rebooking
{
    public class RebookingSelectMealService : BaseService<BookingSelectMealRequest, BasePriceResponse>
    {
        public RebookingSelectMealService(MainRequest request) : base(request)
        {

        }

        internal override void ValidationControl()
        {
        }

        internal override void FlowOperations()
        {
        }

        internal override void InternalWork()
        {
            var meal = Session.CurrentFlow.PNR.Meals.FirstOrDefault(f => f.SegmentTmobId == RequestData.SegmentTmobId && f.PassengerId == RequestData.PassengerId);
            var mealResponse = Session.CurrentFlow.ServiceMeals.Meals.FirstOrDefault(m =>
                m.Code == RequestData.ServiceId);
            if (meal == null && RequestData.ServiceId != null)
            {
                meal = new PassengerFlightMeal
                {
                    PassengerId = RequestData.PassengerId,
                    SegmentTmobId = RequestData.SegmentTmobId,
                    ServiceId = RequestData.ServiceId,
                    IsBundleIncluded = mealResponse?.IsBundleIncluded ?? false,
                    BundleCode = mealResponse?.BundleCode,
                    BundleCategoryCode = mealResponse?.BundleCategoryCode,
                };
                Session.CurrentFlow.PNR.Meals.Add(meal);
            }
            else
            {
                if (RequestData.ServiceId != null)
                {
                    meal.ServiceId = RequestData.ServiceId;
                    meal.IsBundleIncluded = mealResponse?.IsBundleIncluded ?? false;
                    meal.BundleCode = mealResponse?.BundleCode;
                    meal.BundleCategoryCode = mealResponse?.BundleCategoryCode;
                }
                else
                {
                    Session.CurrentFlow.PNR.Meals.Remove(meal);
                }
            }

            ResponseData = ServiceProvider.RebookingSelectMeal(RequestData);
        }
    }
}
