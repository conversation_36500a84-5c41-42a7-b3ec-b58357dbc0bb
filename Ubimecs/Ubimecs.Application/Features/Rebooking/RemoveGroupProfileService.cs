using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Ubimecs.Application.Common;
using Ubimecs.Infrastructure.Models.DTO.Passenger;
using Ubimecs.Infrastructure.Models.Request;

namespace Ubimecs.Application.Features.Rebooking
{
    public class RemoveGroupProfileService : BaseService<RemoveGroupProfileRequest, List<PassengerInformationDTO>>
    {
        public RemoveGroupProfileService(MainRequest request) : base(request)
        {
        }

        internal override void FlowOperations()
        {

        }

        internal override void InternalWork()
        {
            var passenger = Session.CurrentFlow.PNR.GroupPassengers.FirstOrDefault(x => x.GivenName == RequestData.Name &&
                                                                                        x.Surname == RequestData.Surname &&
                                                                                        x.ETicketNumber == RequestData.ETKT
                                                                                        );
            if (passenger != null)
            {
                Session.CurrentFlow.PNR.GroupPassengers.Remove(passenger);
            }

            ResponseData = Session.CurrentFlow.PNR.GroupPassengers.ToList();
        }

        internal override void ValidationControl()
        {

        }
    }
}
