using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Ubimecs.Application.Common;
using Ubimecs.Infrastructure.Models.Flow;
using Ubimecs.Infrastructure.Models.Request;
using Ubimecs.Infrastructure.Models.Response;

namespace Ubimecs.Application.Features.Rebooking
{
    public class SearchGuestService : BaseService<SearchGuestRequest, GetPnrInfoResponse>
    {
        public SearchGuestService(MainRequest request) : base(request)
        {
        }

        internal override void FlowOperations()
        {
            if (RequestData.PnrNumber != null)
            {
                Session.StartNewFlow(FlowType.Rebooking);
                Session.CurrentFlow.RebookingType = RebookingTypeEnum.AddExtras;
            }
            else if (!string.IsNullOrEmpty(RequestData.TourOperatorNumber))
            {
                Session.StartNewFlow(FlowType.Rebooking);
                Session.CurrentFlow.RebookingType = RebookingTypeEnum.AddExtras;
            }
        }

        internal override void InternalWork()
        {
            ResponseData = ServiceProvider.SearchGuest(RequestData, !string.IsNullOrEmpty(RequestData.PnrNumber));
        }

        internal override void ValidationControl()
        {

            //if (!string.IsNullOrEmpty(RequestData.PnrNumber) && RequestData.PnrNumber.Trim().Length != 6)
            //{
            //    throw new TmobException(CachedData.GetCMS(Session.Language, "pnr_retrieval_wrong"));
            //}
        }
    }
}
