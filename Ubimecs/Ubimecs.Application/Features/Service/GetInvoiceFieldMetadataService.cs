using Ubimecs.Application.Common;
using Ubimecs.Infrastructure.Models.Common;
using Ubimecs.Infrastructure.Models.Request;
using Ubimecs.Infrastructure.Models.Response;

namespace Ubimecs.Application.Features.Service
{
    public class GetInvoiceFieldMetadataService : BaseService<GetInvoiceFieldMetadataRequest, GetInvoiceFieldMetadataResponse>
    {
        public GetInvoiceFieldMetadataService(MainRequest request) : base(request)
        {
        }

        internal override void FlowOperations()
        {
        }

        internal override void InternalWork()
        {
            ResponseData = ServiceProvider.GetInvoiceFieldMetadata(RequestData);
        }

        internal override void ValidationControl()
        {
            if (string.IsNullOrEmpty(RequestData.CountryOfResidence))
            {
                throw new TmobException("CountryOfResidence is required");
            }
        }
    }
}
