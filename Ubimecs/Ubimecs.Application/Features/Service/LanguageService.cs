using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Ubimecs.Application.Common;
using Ubimecs.Infrastructure.Caching;
using Ubimecs.Infrastructure.Models.DTO;
using Ubimecs.Infrastructure.Models.Request;

namespace Ubimecs.Application.Features.Service
{
    public class LanguageService : BaseService<LanguageRequest, List<LanguageDTO>>
    {
        public LanguageService(MainRequest request) : base(request, false, true)
        {
        }

        internal override void ValidationControl()
        {

        }

        internal override void FlowOperations()
        {
        }

        internal override void InternalWork()
        {
            ResponseData = CachedData.LanguageList;
        }
    }
}
