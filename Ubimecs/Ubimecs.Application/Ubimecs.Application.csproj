<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="SSH.NET" Version="2024.1.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Ubimecs.IBS\Ubimecs.IBS.csproj" />
    <ProjectReference Include="..\Ubimecs.Infrastructure.CMS\Ubimecs.Infrastructure.CMS.csproj" />
    <ProjectReference Include="..\Ubimecs.Infrastructure.CRM\Ubimecs.Infrastructure.CRM.csproj" />
    <ProjectReference Include="..\Ubimecs.Infrastructure.CSM\Ubimecs.Infrastructure.CSM.csproj" />
    <ProjectReference Include="..\Ubimecs.Infrastructure.FileService\Ubimecs.Infrastructure.FileService.csproj" />
    <ProjectReference Include="..\Ubimecs.Infrastructure.HolidayExtras\Ubimecs.Infrastructure.HolidayExtras.csproj" />
    <ProjectReference Include="..\Ubimecs.Infrastructure.Mailing\Ubimecs.Infrastructure.Mailing.csproj" />
    <ProjectReference Include="..\Ubimecs.Infrastructure\Ubimecs.Infrastructure.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Features\Captcha\" />
  </ItemGroup>

</Project>
