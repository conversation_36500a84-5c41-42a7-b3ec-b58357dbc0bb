//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace GiftVoucherPortServiceReference
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.ServiceContractAttribute(Namespace="http://www.ibsplc.com/wsdl", ConfigurationName="GiftVoucherPortServiceReference.GiftVoucherPort")]
    public interface GiftVoucherPort
    {
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:#createGiftVoucher", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<GiftVoucherPortServiceReference.createGiftVoucherResponse> createGiftVoucherAsync(GiftVoucherPortServiceReference.createGiftVoucherRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:#issueGiftVoucher", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<GiftVoucherPortServiceReference.issueGiftVoucherResponse> issueGiftVoucherAsync(GiftVoucherPortServiceReference.issueGiftVoucherRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:#listGiftVouchers", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<GiftVoucherPortServiceReference.listGiftVouchersResponse> listGiftVouchersAsync(GiftVoucherPortServiceReference.listGiftVouchersRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:#loginVPP", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<GiftVoucherPortServiceReference.loginVPPResponse> loginVPPAsync(GiftVoucherPortServiceReference.loginVPPRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:#retrieveGCWithProfileAlias", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<GiftVoucherPortServiceReference.retrieveGCWithProfileAliasResponse> retrieveGCWithProfileAliasAsync(GiftVoucherPortServiceReference.retrieveGCWithProfileAliasRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:#retrieveGiftCertificateDetails", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<GiftVoucherPortServiceReference.retrieveGiftCertificateDetailsResponse> retrieveGiftCertificateDetailsAsync(GiftVoucherPortServiceReference.retrieveGiftCertificateDetailsRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:#retrieveVPPDetails", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<GiftVoucherPortServiceReference.retrieveVPPDetailsResponse> retrieveVPPDetailsAsync(GiftVoucherPortServiceReference.retrieveVPPDetailsRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:#voidVoucher", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<GiftVoucherPortServiceReference.voidVoucherResponse> voidVoucherAsync(GiftVoucherPortServiceReference.voidVoucherRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:#getTaxForGiftVoucher", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<GiftVoucherPortServiceReference.getTaxForGiftVoucherResponse> getTaxForGiftVoucherAsync(GiftVoucherPortServiceReference.getTaxForGiftVoucherRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:#sendGCEmail", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<GiftVoucherPortServiceReference.sendGCEmailResponse> sendGCEmailAsync(GiftVoucherPortServiceReference.sendGCEmailRequest request);
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class CreateGiftVoucherRQ
    {
        
        private string airlineCodeField;
        
        private BookingChannelKeyType bookingChannelField;
        
        private VoucherDeliveryMode deliveryModeField;
        
        private string pickupLocationField;
        
        private sendTo sendToField;
        
        private string giftCertificateTypeCodeField;
        
        private string giftVoucherCurrencyCodeField;
        
        private GiftVoucherDenominationDetails[] denominationDetailsField;
        
        private GuestPaymentInfoType[] guestPaymentInfoField;
        
        private GVPurchaserInformation purchaserInformationField;
        
        private GVRecipientInformation recipientInformationField;
        
        private string personalMessageField;
        
        private string companyNameField;
        
        private string taxOfficeField;
        
        private string languageField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AirlineCode
        {
            get
            {
                return this.airlineCodeField;
            }
            set
            {
                this.airlineCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public BookingChannelKeyType BookingChannel
        {
            get
            {
                return this.bookingChannelField;
            }
            set
            {
                this.bookingChannelField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public VoucherDeliveryMode deliveryMode
        {
            get
            {
                return this.deliveryModeField;
            }
            set
            {
                this.deliveryModeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string pickupLocation
        {
            get
            {
                return this.pickupLocationField;
            }
            set
            {
                this.pickupLocationField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public sendTo sendTo
        {
            get
            {
                return this.sendToField;
            }
            set
            {
                this.sendToField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string GiftCertificateTypeCode
        {
            get
            {
                return this.giftCertificateTypeCodeField;
            }
            set
            {
                this.giftCertificateTypeCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string giftVoucherCurrencyCode
        {
            get
            {
                return this.giftVoucherCurrencyCodeField;
            }
            set
            {
                this.giftVoucherCurrencyCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("DenominationDetails", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public GiftVoucherDenominationDetails[] DenominationDetails
        {
            get
            {
                return this.denominationDetailsField;
            }
            set
            {
                this.denominationDetailsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("GuestPaymentInfo", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public GuestPaymentInfoType[] GuestPaymentInfo
        {
            get
            {
                return this.guestPaymentInfoField;
            }
            set
            {
                this.guestPaymentInfoField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public GVPurchaserInformation purchaserInformation
        {
            get
            {
                return this.purchaserInformationField;
            }
            set
            {
                this.purchaserInformationField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=10)]
        public GVRecipientInformation recipientInformation
        {
            get
            {
                return this.recipientInformationField;
            }
            set
            {
                this.recipientInformationField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=11)]
        public string personalMessage
        {
            get
            {
                return this.personalMessageField;
            }
            set
            {
                this.personalMessageField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=12)]
        public string companyName
        {
            get
            {
                return this.companyNameField;
            }
            set
            {
                this.companyNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=13)]
        public string taxOffice
        {
            get
            {
                return this.taxOfficeField;
            }
            set
            {
                this.taxOfficeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=14)]
        public string language
        {
            get
            {
                return this.languageField;
            }
            set
            {
                this.languageField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class BookingChannelKeyType
    {
        
        private string channelTypeField;
        
        private string channelField;
        
        private string localeField;
        
        private string sessionIdField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string ChannelType
        {
            get
            {
                return this.channelTypeField;
            }
            set
            {
                this.channelTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string Channel
        {
            get
            {
                return this.channelField;
            }
            set
            {
                this.channelField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string Locale
        {
            get
            {
                return this.localeField;
            }
            set
            {
                this.localeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string SessionId
        {
            get
            {
                return this.sessionIdField;
            }
            set
            {
                this.sessionIdField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class DenominationType
    {
        
        private string currencyCodeField;
        
        private double amountField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string CurrencyCode
        {
            get
            {
                return this.currencyCodeField;
            }
            set
            {
                this.currencyCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public double Amount
        {
            get
            {
                return this.amountField;
            }
            set
            {
                this.amountField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class ContactInformation
    {
        
        private string agencyNameField;
        
        private string agencyRegionalNameField;
        
        private string managerFirstNameField;
        
        private string managerLastNameField;
        
        private string isdPhoneCodeField;
        
        private string phoneNumberField;
        
        private string countryCodeField;
        
        private string countryNameField;
        
        private string address1Field;
        
        private string address2Field;
        
        private string address3Field;
        
        private string cityField;
        
        private string provinceCodeField;
        
        private string provinceField;
        
        private string postalCodeField;
        
        private string faxNumberField;
        
        private string emailIDField;
        
        private string altEmailIDField;
        
        private string commentsField;
        
        private string extnNumberField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AgencyName
        {
            get
            {
                return this.agencyNameField;
            }
            set
            {
                this.agencyNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string AgencyRegionalName
        {
            get
            {
                return this.agencyRegionalNameField;
            }
            set
            {
                this.agencyRegionalNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string ManagerFirstName
        {
            get
            {
                return this.managerFirstNameField;
            }
            set
            {
                this.managerFirstNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string ManagerLastName
        {
            get
            {
                return this.managerLastNameField;
            }
            set
            {
                this.managerLastNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string IsdPhoneCode
        {
            get
            {
                return this.isdPhoneCodeField;
            }
            set
            {
                this.isdPhoneCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string PhoneNumber
        {
            get
            {
                return this.phoneNumberField;
            }
            set
            {
                this.phoneNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string CountryCode
        {
            get
            {
                return this.countryCodeField;
            }
            set
            {
                this.countryCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string CountryName
        {
            get
            {
                return this.countryNameField;
            }
            set
            {
                this.countryNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public string Address1
        {
            get
            {
                return this.address1Field;
            }
            set
            {
                this.address1Field = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public string Address2
        {
            get
            {
                return this.address2Field;
            }
            set
            {
                this.address2Field = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=10)]
        public string Address3
        {
            get
            {
                return this.address3Field;
            }
            set
            {
                this.address3Field = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=11)]
        public string City
        {
            get
            {
                return this.cityField;
            }
            set
            {
                this.cityField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=12)]
        public string ProvinceCode
        {
            get
            {
                return this.provinceCodeField;
            }
            set
            {
                this.provinceCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=13)]
        public string Province
        {
            get
            {
                return this.provinceField;
            }
            set
            {
                this.provinceField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=14)]
        public string PostalCode
        {
            get
            {
                return this.postalCodeField;
            }
            set
            {
                this.postalCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=15)]
        public string FaxNumber
        {
            get
            {
                return this.faxNumberField;
            }
            set
            {
                this.faxNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=16)]
        public string EmailID
        {
            get
            {
                return this.emailIDField;
            }
            set
            {
                this.emailIDField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=17)]
        public string AltEmailID
        {
            get
            {
                return this.altEmailIDField;
            }
            set
            {
                this.altEmailIDField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=18)]
        public string Comments
        {
            get
            {
                return this.commentsField;
            }
            set
            {
                this.commentsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=19)]
        public string ExtnNumber
        {
            get
            {
                return this.extnNumberField;
            }
            set
            {
                this.extnNumberField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class VPPDetails
    {
        
        private string airlineCodeField;
        
        private string vPPIDField;
        
        private string companyNameField;
        
        private string salesManagerIDField;
        
        private CommissionableAt commissionableAtField;
        
        private string agencyCodeField;
        
        private DateOnlyType startDateField;
        
        private DateOnlyType endDateField;
        
        private ContactInformation contactTypeField;
        
        private VPPStatus vPPStatusField;
        
        private string singleGVIndField;
        
        private string bulkGVIndField;
        
        private string agencyNameField;
        
        private string agencyRegionalNameField;
        
        private DenominationType[] denominationTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AirlineCode
        {
            get
            {
                return this.airlineCodeField;
            }
            set
            {
                this.airlineCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string VPPID
        {
            get
            {
                return this.vPPIDField;
            }
            set
            {
                this.vPPIDField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string CompanyName
        {
            get
            {
                return this.companyNameField;
            }
            set
            {
                this.companyNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string SalesManagerID
        {
            get
            {
                return this.salesManagerIDField;
            }
            set
            {
                this.salesManagerIDField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public CommissionableAt CommissionableAt
        {
            get
            {
                return this.commissionableAtField;
            }
            set
            {
                this.commissionableAtField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string AgencyCode
        {
            get
            {
                return this.agencyCodeField;
            }
            set
            {
                this.agencyCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public DateOnlyType StartDate
        {
            get
            {
                return this.startDateField;
            }
            set
            {
                this.startDateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public DateOnlyType EndDate
        {
            get
            {
                return this.endDateField;
            }
            set
            {
                this.endDateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public ContactInformation ContactType
        {
            get
            {
                return this.contactTypeField;
            }
            set
            {
                this.contactTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public VPPStatus VPPStatus
        {
            get
            {
                return this.vPPStatusField;
            }
            set
            {
                this.vPPStatusField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=10)]
        public string SingleGVInd
        {
            get
            {
                return this.singleGVIndField;
            }
            set
            {
                this.singleGVIndField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=11)]
        public string BulkGVInd
        {
            get
            {
                return this.bulkGVIndField;
            }
            set
            {
                this.bulkGVIndField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=12)]
        public string AgencyName
        {
            get
            {
                return this.agencyNameField;
            }
            set
            {
                this.agencyNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=13)]
        public string AgencyRegionalName
        {
            get
            {
                return this.agencyRegionalNameField;
            }
            set
            {
                this.agencyRegionalNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("DenominationType", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=14)]
        public DenominationType[] DenominationType
        {
            get
            {
                return this.denominationTypeField;
            }
            set
            {
                this.denominationTypeField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public enum CommissionableAt
    {
        
        /// <remarks/>
        CREATION,
        
        /// <remarks/>
        USAGE,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class DateOnlyType
    {
        
        private System.DateTime dateField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, DataType="date", Order=0)]
        public System.DateTime Date
        {
            get
            {
                return this.dateField;
            }
            set
            {
                this.dateField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public enum VPPStatus
    {
        
        /// <remarks/>
        ACTIVE,
        
        /// <remarks/>
        INACTIVE,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class RecipientDetails
    {
        
        private string titleField;
        
        private string firstNameField;
        
        private string lastNameField;
        
        private string organizationField;
        
        private string addressOneField;
        
        private string addressTwoField;
        
        private string streetField;
        
        private string townField;
        
        private string stateField;
        
        private string countryField;
        
        private string zipCodeField;
        
        private string homePhoneField;
        
        private string workPhoneField;
        
        private string mobilePhoneField;
        
        private string faxField;
        
        private string isdHomePhoneCodeField;
        
        private string isdWorkPhoneCodeField;
        
        private string isdMobilePhoneCodeField;
        
        private string emailField;
        
        private RecipientType recipientTypeField;
        
        private bool recipientTypeFieldSpecified;
        
        private string recipientProfileIdField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string title
        {
            get
            {
                return this.titleField;
            }
            set
            {
                this.titleField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string firstName
        {
            get
            {
                return this.firstNameField;
            }
            set
            {
                this.firstNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string lastName
        {
            get
            {
                return this.lastNameField;
            }
            set
            {
                this.lastNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string organization
        {
            get
            {
                return this.organizationField;
            }
            set
            {
                this.organizationField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string addressOne
        {
            get
            {
                return this.addressOneField;
            }
            set
            {
                this.addressOneField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string addressTwo
        {
            get
            {
                return this.addressTwoField;
            }
            set
            {
                this.addressTwoField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string street
        {
            get
            {
                return this.streetField;
            }
            set
            {
                this.streetField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string town
        {
            get
            {
                return this.townField;
            }
            set
            {
                this.townField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public string state
        {
            get
            {
                return this.stateField;
            }
            set
            {
                this.stateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public string country
        {
            get
            {
                return this.countryField;
            }
            set
            {
                this.countryField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=10)]
        public string zipCode
        {
            get
            {
                return this.zipCodeField;
            }
            set
            {
                this.zipCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=11)]
        public string homePhone
        {
            get
            {
                return this.homePhoneField;
            }
            set
            {
                this.homePhoneField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=12)]
        public string workPhone
        {
            get
            {
                return this.workPhoneField;
            }
            set
            {
                this.workPhoneField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=13)]
        public string mobilePhone
        {
            get
            {
                return this.mobilePhoneField;
            }
            set
            {
                this.mobilePhoneField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=14)]
        public string fax
        {
            get
            {
                return this.faxField;
            }
            set
            {
                this.faxField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=15)]
        public string isdHomePhoneCode
        {
            get
            {
                return this.isdHomePhoneCodeField;
            }
            set
            {
                this.isdHomePhoneCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=16)]
        public string isdWorkPhoneCode
        {
            get
            {
                return this.isdWorkPhoneCodeField;
            }
            set
            {
                this.isdWorkPhoneCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=17)]
        public string isdMobilePhoneCode
        {
            get
            {
                return this.isdMobilePhoneCodeField;
            }
            set
            {
                this.isdMobilePhoneCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=18)]
        public string email
        {
            get
            {
                return this.emailField;
            }
            set
            {
                this.emailField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=19)]
        public RecipientType recipientType
        {
            get
            {
                return this.recipientTypeField;
            }
            set
            {
                this.recipientTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool recipientTypeSpecified
        {
            get
            {
                return this.recipientTypeFieldSpecified;
            }
            set
            {
                this.recipientTypeFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=20)]
        public string recipientProfileId
        {
            get
            {
                return this.recipientProfileIdField;
            }
            set
            {
                this.recipientProfileIdField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public enum RecipientType
    {
        
        /// <remarks/>
        INDIVIDUAL,
        
        /// <remarks/>
        CORPORATE,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class PurchaserDetails
    {
        
        private string titleField;
        
        private string firstNameField;
        
        private string lastNameField;
        
        private string organizationField;
        
        private string addressOneField;
        
        private string addressTwoField;
        
        private string streetField;
        
        private string townField;
        
        private string stateField;
        
        private string countryField;
        
        private string zipCodeField;
        
        private string homePhoneField;
        
        private string workPhoneField;
        
        private string mobilePhoneField;
        
        private string faxField;
        
        private string isdHomePhoneCodeField;
        
        private string isdWorkPhoneCodeField;
        
        private string isdMobilePhoneCodeField;
        
        private string emailField;
        
        private string loyaltyNumberField;
        
        private string loyaltyBonusCodeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string title
        {
            get
            {
                return this.titleField;
            }
            set
            {
                this.titleField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string firstName
        {
            get
            {
                return this.firstNameField;
            }
            set
            {
                this.firstNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string lastName
        {
            get
            {
                return this.lastNameField;
            }
            set
            {
                this.lastNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string organization
        {
            get
            {
                return this.organizationField;
            }
            set
            {
                this.organizationField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string addressOne
        {
            get
            {
                return this.addressOneField;
            }
            set
            {
                this.addressOneField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string addressTwo
        {
            get
            {
                return this.addressTwoField;
            }
            set
            {
                this.addressTwoField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string street
        {
            get
            {
                return this.streetField;
            }
            set
            {
                this.streetField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string town
        {
            get
            {
                return this.townField;
            }
            set
            {
                this.townField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public string state
        {
            get
            {
                return this.stateField;
            }
            set
            {
                this.stateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public string country
        {
            get
            {
                return this.countryField;
            }
            set
            {
                this.countryField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=10)]
        public string zipCode
        {
            get
            {
                return this.zipCodeField;
            }
            set
            {
                this.zipCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=11)]
        public string homePhone
        {
            get
            {
                return this.homePhoneField;
            }
            set
            {
                this.homePhoneField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=12)]
        public string workPhone
        {
            get
            {
                return this.workPhoneField;
            }
            set
            {
                this.workPhoneField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=13)]
        public string mobilePhone
        {
            get
            {
                return this.mobilePhoneField;
            }
            set
            {
                this.mobilePhoneField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=14)]
        public string fax
        {
            get
            {
                return this.faxField;
            }
            set
            {
                this.faxField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=15)]
        public string isdHomePhoneCode
        {
            get
            {
                return this.isdHomePhoneCodeField;
            }
            set
            {
                this.isdHomePhoneCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=16)]
        public string isdWorkPhoneCode
        {
            get
            {
                return this.isdWorkPhoneCodeField;
            }
            set
            {
                this.isdWorkPhoneCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=17)]
        public string isdMobilePhoneCode
        {
            get
            {
                return this.isdMobilePhoneCodeField;
            }
            set
            {
                this.isdMobilePhoneCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=18)]
        public string email
        {
            get
            {
                return this.emailField;
            }
            set
            {
                this.emailField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=19)]
        public string loyaltyNumber
        {
            get
            {
                return this.loyaltyNumberField;
            }
            set
            {
                this.loyaltyNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=20)]
        public string loyaltyBonusCode
        {
            get
            {
                return this.loyaltyBonusCodeField;
            }
            set
            {
                this.loyaltyBonusCodeField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class RecipientDetailsType
    {
        
        private string firstNameField;
        
        private string lastNameField;
        
        private string organizationField;
        
        private string addressOneField;
        
        private string countryField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string FirstName
        {
            get
            {
                return this.firstNameField;
            }
            set
            {
                this.firstNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string LastName
        {
            get
            {
                return this.lastNameField;
            }
            set
            {
                this.lastNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string Organization
        {
            get
            {
                return this.organizationField;
            }
            set
            {
                this.organizationField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string AddressOne
        {
            get
            {
                return this.addressOneField;
            }
            set
            {
                this.addressOneField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string Country
        {
            get
            {
                return this.countryField;
            }
            set
            {
                this.countryField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class PurchaserDetailsType
    {
        
        private string firstNameField;
        
        private string lastNameField;
        
        private string organizationField;
        
        private string addressOneField;
        
        private string countryField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string FirstName
        {
            get
            {
                return this.firstNameField;
            }
            set
            {
                this.firstNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string LastName
        {
            get
            {
                return this.lastNameField;
            }
            set
            {
                this.lastNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string Organization
        {
            get
            {
                return this.organizationField;
            }
            set
            {
                this.organizationField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string AddressOne
        {
            get
            {
                return this.addressOneField;
            }
            set
            {
                this.addressOneField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string Country
        {
            get
            {
                return this.countryField;
            }
            set
            {
                this.countryField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class RetrieveGCWithProfileAlias
    {
        
        private string giftVoucherTypeField;
        
        private string giftCertificateNumberField;
        
        private string bulkOrderNumberField;
        
        private string giftCertificateCurrencyCodeField;
        
        private double originalAmountField;
        
        private bool originalAmountFieldSpecified;
        
        private double giftCertificateBalanceAmountField;
        
        private bool giftCertificateBalanceAmountFieldSpecified;
        
        private string giftCertificateStatusField;
        
        private System.DateTime giftCertificateExpiryDateField;
        
        private string ownerOfCreationField;
        
        private PurchaserDetailsType purchaserDetailsField;
        
        private RecipientDetailsType recepientDetailsField;
        
        private ErrorType errorTypeField;
        
        private string convertToCurrencyField;
        
        private double convertedAmountField;
        
        private string giftCertificateTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string GiftVoucherType
        {
            get
            {
                return this.giftVoucherTypeField;
            }
            set
            {
                this.giftVoucherTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string GiftCertificateNumber
        {
            get
            {
                return this.giftCertificateNumberField;
            }
            set
            {
                this.giftCertificateNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string BulkOrderNumber
        {
            get
            {
                return this.bulkOrderNumberField;
            }
            set
            {
                this.bulkOrderNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string GiftCertificateCurrencyCode
        {
            get
            {
                return this.giftCertificateCurrencyCodeField;
            }
            set
            {
                this.giftCertificateCurrencyCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public double OriginalAmount
        {
            get
            {
                return this.originalAmountField;
            }
            set
            {
                this.originalAmountField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool OriginalAmountSpecified
        {
            get
            {
                return this.originalAmountFieldSpecified;
            }
            set
            {
                this.originalAmountFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public double GiftCertificateBalanceAmount
        {
            get
            {
                return this.giftCertificateBalanceAmountField;
            }
            set
            {
                this.giftCertificateBalanceAmountField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool GiftCertificateBalanceAmountSpecified
        {
            get
            {
                return this.giftCertificateBalanceAmountFieldSpecified;
            }
            set
            {
                this.giftCertificateBalanceAmountFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string GiftCertificateStatus
        {
            get
            {
                return this.giftCertificateStatusField;
            }
            set
            {
                this.giftCertificateStatusField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, DataType="date", Order=7)]
        public System.DateTime GiftCertificateExpiryDate
        {
            get
            {
                return this.giftCertificateExpiryDateField;
            }
            set
            {
                this.giftCertificateExpiryDateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public string OwnerOfCreation
        {
            get
            {
                return this.ownerOfCreationField;
            }
            set
            {
                this.ownerOfCreationField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public PurchaserDetailsType PurchaserDetails
        {
            get
            {
                return this.purchaserDetailsField;
            }
            set
            {
                this.purchaserDetailsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=10)]
        public RecipientDetailsType RecepientDetails
        {
            get
            {
                return this.recepientDetailsField;
            }
            set
            {
                this.recepientDetailsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=11)]
        public ErrorType ErrorType
        {
            get
            {
                return this.errorTypeField;
            }
            set
            {
                this.errorTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=12)]
        public string ConvertToCurrency
        {
            get
            {
                return this.convertToCurrencyField;
            }
            set
            {
                this.convertToCurrencyField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=13)]
        public double ConvertedAmount
        {
            get
            {
                return this.convertedAmountField;
            }
            set
            {
                this.convertedAmountField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=14)]
        public string GiftCertificateType
        {
            get
            {
                return this.giftCertificateTypeField;
            }
            set
            {
                this.giftCertificateTypeField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class ErrorType
    {
        
        private string errorCodeField;
        
        private string errorValueField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string errorCode
        {
            get
            {
                return this.errorCodeField;
            }
            set
            {
                this.errorCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string errorValue
        {
            get
            {
                return this.errorValueField;
            }
            set
            {
                this.errorValueField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class ListGiftVoucherDetails
    {
        
        private string giftVoucherNumberField;
        
        private string bulkOrderNumberField;
        
        private string giftVoucherTypeCodeField;
        
        private string vPPIdField;
        
        private string ownerOfCreationField;
        
        private string giftVoucherCurrencyCodeField;
        
        private string giftVoucherBalanceAmountField;
        
        private string giftVoucherStatusField;
        
        private System.DateTime giftVoucherExpiryDateField;
        
        private bool isPrevBtnDisableField;
        
        private bool isNextBtnDisableField;
        
        private int totalPagesField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string GiftVoucherNumber
        {
            get
            {
                return this.giftVoucherNumberField;
            }
            set
            {
                this.giftVoucherNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string BulkOrderNumber
        {
            get
            {
                return this.bulkOrderNumberField;
            }
            set
            {
                this.bulkOrderNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string GiftVoucherTypeCode
        {
            get
            {
                return this.giftVoucherTypeCodeField;
            }
            set
            {
                this.giftVoucherTypeCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string VPPId
        {
            get
            {
                return this.vPPIdField;
            }
            set
            {
                this.vPPIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string OwnerOfCreation
        {
            get
            {
                return this.ownerOfCreationField;
            }
            set
            {
                this.ownerOfCreationField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string GiftVoucherCurrencyCode
        {
            get
            {
                return this.giftVoucherCurrencyCodeField;
            }
            set
            {
                this.giftVoucherCurrencyCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string GiftVoucherBalanceAmount
        {
            get
            {
                return this.giftVoucherBalanceAmountField;
            }
            set
            {
                this.giftVoucherBalanceAmountField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string GiftVoucherStatus
        {
            get
            {
                return this.giftVoucherStatusField;
            }
            set
            {
                this.giftVoucherStatusField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, DataType="date", Order=8)]
        public System.DateTime GiftVoucherExpiryDate
        {
            get
            {
                return this.giftVoucherExpiryDateField;
            }
            set
            {
                this.giftVoucherExpiryDateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public bool isPrevBtnDisable
        {
            get
            {
                return this.isPrevBtnDisableField;
            }
            set
            {
                this.isPrevBtnDisableField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=10)]
        public bool isNextBtnDisable
        {
            get
            {
                return this.isNextBtnDisableField;
            }
            set
            {
                this.isNextBtnDisableField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=11)]
        public int totalPages
        {
            get
            {
                return this.totalPagesField;
            }
            set
            {
                this.totalPagesField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class CartDetailsType
    {
        
        private string cartNameField;
        
        private double cartAmountField;
        
        private bool cartAmountFieldSpecified;
        
        private int cartQtyField;
        
        private bool cartQtyFieldSpecified;
        
        private int cartOrderField;
        
        private bool cartOrderFieldSpecified;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string CartName
        {
            get
            {
                return this.cartNameField;
            }
            set
            {
                this.cartNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public double CartAmount
        {
            get
            {
                return this.cartAmountField;
            }
            set
            {
                this.cartAmountField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool CartAmountSpecified
        {
            get
            {
                return this.cartAmountFieldSpecified;
            }
            set
            {
                this.cartAmountFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public int CartQty
        {
            get
            {
                return this.cartQtyField;
            }
            set
            {
                this.cartQtyField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool CartQtySpecified
        {
            get
            {
                return this.cartQtyFieldSpecified;
            }
            set
            {
                this.cartQtyFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public int CartOrder
        {
            get
            {
                return this.cartOrderField;
            }
            set
            {
                this.cartOrderField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool CartOrderSpecified
        {
            get
            {
                return this.cartOrderFieldSpecified;
            }
            set
            {
                this.cartOrderFieldSpecified = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class AdditionalCreditCardInfo
    {
        
        private string registrationNumberField;
        
        private string installmentPeriodField;
        
        private AuthenticationType authenticationTypeField;
        
        private bool authenticationTypeFieldSpecified;
        
        private string cardPasswordField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string RegistrationNumber
        {
            get
            {
                return this.registrationNumberField;
            }
            set
            {
                this.registrationNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string InstallmentPeriod
        {
            get
            {
                return this.installmentPeriodField;
            }
            set
            {
                this.installmentPeriodField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public AuthenticationType AuthenticationType
        {
            get
            {
                return this.authenticationTypeField;
            }
            set
            {
                this.authenticationTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool AuthenticationTypeSpecified
        {
            get
            {
                return this.authenticationTypeFieldSpecified;
            }
            set
            {
                this.authenticationTypeFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string CardPassword
        {
            get
            {
                return this.cardPasswordField;
            }
            set
            {
                this.cardPasswordField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public enum AuthenticationType
    {
        
        /// <remarks/>
        JJ,
        
        /// <remarks/>
        BB,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class AdditionalCCDetails
    {
        
        private string registrationNumberField;
        
        private string authTypeField;
        
        private int installmentPeriodField;
        
        private bool installmentPeriodFieldSpecified;
        
        private string cardPasswordField;
        
        private int goodsCountField;
        
        private bool goodsCountFieldSpecified;
        
        private string goodsNameField;
        
        private string customerUserIDField;
        
        private string threeDNotificationURLField;
        
        private string cSSURLField;
        
        private string paymentPageTemplateField;
        
        private string transactionKeyField;
        
        private string posMessageField;
        
        private string timeStampField;
        
        private string signatureField;
        
        private string bankIdField;
        
        private string bankNameField;
        
        private string cardBinField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string RegistrationNumber
        {
            get
            {
                return this.registrationNumberField;
            }
            set
            {
                this.registrationNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string AuthType
        {
            get
            {
                return this.authTypeField;
            }
            set
            {
                this.authTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public int InstallmentPeriod
        {
            get
            {
                return this.installmentPeriodField;
            }
            set
            {
                this.installmentPeriodField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool InstallmentPeriodSpecified
        {
            get
            {
                return this.installmentPeriodFieldSpecified;
            }
            set
            {
                this.installmentPeriodFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string CardPassword
        {
            get
            {
                return this.cardPasswordField;
            }
            set
            {
                this.cardPasswordField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public int GoodsCount
        {
            get
            {
                return this.goodsCountField;
            }
            set
            {
                this.goodsCountField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool GoodsCountSpecified
        {
            get
            {
                return this.goodsCountFieldSpecified;
            }
            set
            {
                this.goodsCountFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string GoodsName
        {
            get
            {
                return this.goodsNameField;
            }
            set
            {
                this.goodsNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string CustomerUserID
        {
            get
            {
                return this.customerUserIDField;
            }
            set
            {
                this.customerUserIDField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string ThreeDNotificationURL
        {
            get
            {
                return this.threeDNotificationURLField;
            }
            set
            {
                this.threeDNotificationURLField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public string CSSURL
        {
            get
            {
                return this.cSSURLField;
            }
            set
            {
                this.cSSURLField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public string PaymentPageTemplate
        {
            get
            {
                return this.paymentPageTemplateField;
            }
            set
            {
                this.paymentPageTemplateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=10)]
        public string TransactionKey
        {
            get
            {
                return this.transactionKeyField;
            }
            set
            {
                this.transactionKeyField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=11)]
        public string PosMessage
        {
            get
            {
                return this.posMessageField;
            }
            set
            {
                this.posMessageField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=12)]
        public string TimeStamp
        {
            get
            {
                return this.timeStampField;
            }
            set
            {
                this.timeStampField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=13)]
        public string Signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=14)]
        public string BankId
        {
            get
            {
                return this.bankIdField;
            }
            set
            {
                this.bankIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=15)]
        public string BankName
        {
            get
            {
                return this.bankNameField;
            }
            set
            {
                this.bankNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=16)]
        public string CardBin
        {
            get
            {
                return this.cardBinField;
            }
            set
            {
                this.cardBinField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class ThreeDSecurityDtls
    {
        
        private string paReqField;
        
        private string paResField;
        
        private string acsURLField;
        
        private string returnURLField;
        
        private string httpAcceptField;
        
        private string httpUserAgentField;
        
        private string xidField;
        
        private string mdstatusField;
        
        private string mderrormessageField;
        
        private string txnstatusField;
        
        private string eciField;
        
        private string cavvField;
        
        private string mdField;
        
        private string authcodeField;
        
        private string responseField;
        
        private string errmsgField;
        
        private string hostmsgField;
        
        private string procreturncodeField;
        
        private string hostrefnumField;
        
        private string transidField;
        
        private string maskedPanField;
        
        private string rndField;
        
        private string hashField;
        
        private string hashparamsField;
        
        private string midField;
        
        private string hashparamsvalField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string paReq
        {
            get
            {
                return this.paReqField;
            }
            set
            {
                this.paReqField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string paRes
        {
            get
            {
                return this.paResField;
            }
            set
            {
                this.paResField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string acsURL
        {
            get
            {
                return this.acsURLField;
            }
            set
            {
                this.acsURLField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string returnURL
        {
            get
            {
                return this.returnURLField;
            }
            set
            {
                this.returnURLField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string httpAccept
        {
            get
            {
                return this.httpAcceptField;
            }
            set
            {
                this.httpAcceptField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string httpUserAgent
        {
            get
            {
                return this.httpUserAgentField;
            }
            set
            {
                this.httpUserAgentField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string xid
        {
            get
            {
                return this.xidField;
            }
            set
            {
                this.xidField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string mdstatus
        {
            get
            {
                return this.mdstatusField;
            }
            set
            {
                this.mdstatusField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public string mderrormessage
        {
            get
            {
                return this.mderrormessageField;
            }
            set
            {
                this.mderrormessageField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public string txnstatus
        {
            get
            {
                return this.txnstatusField;
            }
            set
            {
                this.txnstatusField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=10)]
        public string eci
        {
            get
            {
                return this.eciField;
            }
            set
            {
                this.eciField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=11)]
        public string cavv
        {
            get
            {
                return this.cavvField;
            }
            set
            {
                this.cavvField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=12)]
        public string md
        {
            get
            {
                return this.mdField;
            }
            set
            {
                this.mdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=13)]
        public string authcode
        {
            get
            {
                return this.authcodeField;
            }
            set
            {
                this.authcodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=14)]
        public string response
        {
            get
            {
                return this.responseField;
            }
            set
            {
                this.responseField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=15)]
        public string errmsg
        {
            get
            {
                return this.errmsgField;
            }
            set
            {
                this.errmsgField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=16)]
        public string hostmsg
        {
            get
            {
                return this.hostmsgField;
            }
            set
            {
                this.hostmsgField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=17)]
        public string procreturncode
        {
            get
            {
                return this.procreturncodeField;
            }
            set
            {
                this.procreturncodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=18)]
        public string hostrefnum
        {
            get
            {
                return this.hostrefnumField;
            }
            set
            {
                this.hostrefnumField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=19)]
        public string transid
        {
            get
            {
                return this.transidField;
            }
            set
            {
                this.transidField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=20)]
        public string maskedPan
        {
            get
            {
                return this.maskedPanField;
            }
            set
            {
                this.maskedPanField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=21)]
        public string rnd
        {
            get
            {
                return this.rndField;
            }
            set
            {
                this.rndField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=22)]
        public string hash
        {
            get
            {
                return this.hashField;
            }
            set
            {
                this.hashField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=23)]
        public string hashparams
        {
            get
            {
                return this.hashparamsField;
            }
            set
            {
                this.hashparamsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=24)]
        public string mid
        {
            get
            {
                return this.midField;
            }
            set
            {
                this.midField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=25)]
        public string hashparamsval
        {
            get
            {
                return this.hashparamsvalField;
            }
            set
            {
                this.hashparamsvalField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class PaymentElementType
    {
        
        private long elementIdField;
        
        private string elementTypeField;
        
        private string elementSubTypeField;
        
        private double amountField;
        
        private string currencyField;
        
        private double exchangeRateField;
        
        private bool exchangeRateFieldSpecified;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public long ElementId
        {
            get
            {
                return this.elementIdField;
            }
            set
            {
                this.elementIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string ElementType
        {
            get
            {
                return this.elementTypeField;
            }
            set
            {
                this.elementTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string ElementSubType
        {
            get
            {
                return this.elementSubTypeField;
            }
            set
            {
                this.elementSubTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public double Amount
        {
            get
            {
                return this.amountField;
            }
            set
            {
                this.amountField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string Currency
        {
            get
            {
                return this.currencyField;
            }
            set
            {
                this.currencyField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public double ExchangeRate
        {
            get
            {
                return this.exchangeRateField;
            }
            set
            {
                this.exchangeRateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool ExchangeRateSpecified
        {
            get
            {
                return this.exchangeRateFieldSpecified;
            }
            set
            {
                this.exchangeRateFieldSpecified = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class BillingAddress
    {
        
        private string addressOneField;
        
        private string addressTwoField;
        
        private string cityNameField;
        
        private string countryNameField;
        
        private string provinceField;
        
        private string zipCodeField;
        
        private string phoneNumberField;
        
        private string phoneNumberCountryCodeField;
        
        private System.DateTime subscriptionDateField;
        
        private string subscriptionDateTimeZoneField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string addressOne
        {
            get
            {
                return this.addressOneField;
            }
            set
            {
                this.addressOneField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string addressTwo
        {
            get
            {
                return this.addressTwoField;
            }
            set
            {
                this.addressTwoField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string cityName
        {
            get
            {
                return this.cityNameField;
            }
            set
            {
                this.cityNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string countryName
        {
            get
            {
                return this.countryNameField;
            }
            set
            {
                this.countryNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string province
        {
            get
            {
                return this.provinceField;
            }
            set
            {
                this.provinceField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string zipCode
        {
            get
            {
                return this.zipCodeField;
            }
            set
            {
                this.zipCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string phoneNumber
        {
            get
            {
                return this.phoneNumberField;
            }
            set
            {
                this.phoneNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string phoneNumberCountryCode
        {
            get
            {
                return this.phoneNumberCountryCodeField;
            }
            set
            {
                this.phoneNumberCountryCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public System.DateTime subscriptionDate
        {
            get
            {
                return this.subscriptionDateField;
            }
            set
            {
                this.subscriptionDateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public string subscriptionDateTimeZone
        {
            get
            {
                return this.subscriptionDateTimeZoneField;
            }
            set
            {
                this.subscriptionDateTimeZoneField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class DccDetailsType
    {
        
        private bool isDCCOfferedField;
        
        private bool hasUserAcceptedField;
        
        private string dccMerchantIDField;
        
        private double dccAmountField;
        
        private string dccCurrencyField;
        
        private double dccExchangeRateField;
        
        private string statusField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public bool isDCCOffered
        {
            get
            {
                return this.isDCCOfferedField;
            }
            set
            {
                this.isDCCOfferedField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public bool hasUserAccepted
        {
            get
            {
                return this.hasUserAcceptedField;
            }
            set
            {
                this.hasUserAcceptedField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string dccMerchantID
        {
            get
            {
                return this.dccMerchantIDField;
            }
            set
            {
                this.dccMerchantIDField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public double dccAmount
        {
            get
            {
                return this.dccAmountField;
            }
            set
            {
                this.dccAmountField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string dccCurrency
        {
            get
            {
                return this.dccCurrencyField;
            }
            set
            {
                this.dccCurrencyField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public double dccExchangeRate
        {
            get
            {
                return this.dccExchangeRateField;
            }
            set
            {
                this.dccExchangeRateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string status
        {
            get
            {
                return this.statusField;
            }
            set
            {
                this.statusField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class PaymentDetailsType
    {
        
        private FOPType formOfPaymentCodeField;
        
        private PaymentCode paymentCodeField;
        
        private bool paymentCodeFieldSpecified;
        
        private string paymentSystemCodeField;
        
        private double paymentAmountField;
        
        private double exchangeRateField;
        
        private bool exchangeRateFieldSpecified;
        
        private WriteOffCodeType writeOffCodeField;
        
        private bool writeOffCodeFieldSpecified;
        
        private string writeOffReasonField;
        
        private CardType cardTypeField;
        
        private bool cardTypeFieldSpecified;
        
        private string paymentCardTypeField;
        
        private string paymentTypeNumberField;
        
        private string authenticationTxnIdField;
        
        private string membershipNumberField;
        
        private string expirationMonthField;
        
        private string expirationYearField;
        
        private DccDetailsType dccDetailsTypeField;
        
        private string cvv2NumberField;
        
        private string agentIDField;
        
        private string passwordField;
        
        private string validationRequiredField;
        
        private string cardHolderNameField;
        
        private string authorisationCodeField;
        
        private string authorisationDateField;
        
        private string paymentCurrencyField;
        
        private string paymentIdField;
        
        private string paymentStatusField;
        
        private System.DateTime transactionTimeField;
        
        private bool transactionTimeFieldSpecified;
        
        private string transactionTimeZoneField;
        
        private string ipAddressField;
        
        private string emailIDField;
        
        private string affiliateIDField;
        
        private BillingAddress billingAddressField;
        
        private string trackOneField;
        
        private string trackTwoField;
        
        private long oandDIdField;
        
        private bool oandDIdFieldSpecified;
        
        private string paymentDescriptionField;
        
        private PaymentElementType[] paymentElementTypeField;
        
        private string errorCodeField;
        
        private string errorDescriptionField;
        
        private string transactionIDField;
        
        private ThreeDSecurityDtls threeDSecurityDetailsField;
        
        private string convenienceStoreCodeField;
        
        private string convenienceStoreNameField;
        
        private string convenienceStoreTypeField;
        
        private string customerFirstNameField;
        
        private string customerLastNameField;
        
        private string customerPhoneNumberField;
        
        private string recieptNumberField;
        
        private string receiptPrintURLField;
        
        private string usableCVSCompanycodeField;
        
        private string maskedCreditcardNumberField;
        
        private AdditionalCCDetails additionalCCDetailsField;
        
        private CashReceiptType cashReceiptTypeField;
        
        private bool cashReceiptTypeFieldSpecified;
        
        private AdditionalCreditCardInfo additionalCreditCardInfoField;
        
        private string originalAuthDateField;
        
        private string originalAuthCodeField;
        
        private string customerIdField;
        
        private string procReturnCodeField;
        
        private string paymentMethodField;
        
        private string transactionKeyField;
        
        private string requestURLField;
        
        private string redirectionURLField;
        
        private string gatewayNameField;
        
        private string localeCountryCodeField;
        
        private string landingPageField;
        
        private bool isPreAuthorizedField;
        
        private bool isPreAuthorizedFieldSpecified;
        
        private CartDetailsType[] cartDetailsTypeField;
        
        private string successURLField;
        
        private string failureURLField;
        
        private string accountHolderNameField;
        
        private string ibanField;
        
        private string bicField;
        
        private string mobSuccessURLField;
        
        private string mobCancelURLField;
        
        private string mobSourceField;
        
        private string mobDeviceTypeField;
        
        private string mobOSField;
        
        private string mobBrowserField;
        
        private PNRType pnrTypeField;
        
        private bool pnrTypeFieldSpecified;
        
        private string customerProfileIdField;
        
        private string agencyCodeField;
        
        private CustomFieldType[] customFieldListField;
        
        private string serviceInitiatorField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public FOPType FormOfPaymentCode
        {
            get
            {
                return this.formOfPaymentCodeField;
            }
            set
            {
                this.formOfPaymentCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public PaymentCode PaymentCode
        {
            get
            {
                return this.paymentCodeField;
            }
            set
            {
                this.paymentCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool PaymentCodeSpecified
        {
            get
            {
                return this.paymentCodeFieldSpecified;
            }
            set
            {
                this.paymentCodeFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string PaymentSystemCode
        {
            get
            {
                return this.paymentSystemCodeField;
            }
            set
            {
                this.paymentSystemCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public double PaymentAmount
        {
            get
            {
                return this.paymentAmountField;
            }
            set
            {
                this.paymentAmountField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public double ExchangeRate
        {
            get
            {
                return this.exchangeRateField;
            }
            set
            {
                this.exchangeRateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool ExchangeRateSpecified
        {
            get
            {
                return this.exchangeRateFieldSpecified;
            }
            set
            {
                this.exchangeRateFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public WriteOffCodeType WriteOffCode
        {
            get
            {
                return this.writeOffCodeField;
            }
            set
            {
                this.writeOffCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool WriteOffCodeSpecified
        {
            get
            {
                return this.writeOffCodeFieldSpecified;
            }
            set
            {
                this.writeOffCodeFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string WriteOffReason
        {
            get
            {
                return this.writeOffReasonField;
            }
            set
            {
                this.writeOffReasonField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public CardType CardType
        {
            get
            {
                return this.cardTypeField;
            }
            set
            {
                this.cardTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool CardTypeSpecified
        {
            get
            {
                return this.cardTypeFieldSpecified;
            }
            set
            {
                this.cardTypeFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public string PaymentCardType
        {
            get
            {
                return this.paymentCardTypeField;
            }
            set
            {
                this.paymentCardTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public string paymentTypeNumber
        {
            get
            {
                return this.paymentTypeNumberField;
            }
            set
            {
                this.paymentTypeNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=10)]
        public string authenticationTxnId
        {
            get
            {
                return this.authenticationTxnIdField;
            }
            set
            {
                this.authenticationTxnIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=11)]
        public string membershipNumber
        {
            get
            {
                return this.membershipNumberField;
            }
            set
            {
                this.membershipNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=12)]
        public string ExpirationMonth
        {
            get
            {
                return this.expirationMonthField;
            }
            set
            {
                this.expirationMonthField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=13)]
        public string ExpirationYear
        {
            get
            {
                return this.expirationYearField;
            }
            set
            {
                this.expirationYearField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=14)]
        public DccDetailsType DccDetailsType
        {
            get
            {
                return this.dccDetailsTypeField;
            }
            set
            {
                this.dccDetailsTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=15)]
        public string cvv2Number
        {
            get
            {
                return this.cvv2NumberField;
            }
            set
            {
                this.cvv2NumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=16)]
        public string agentID
        {
            get
            {
                return this.agentIDField;
            }
            set
            {
                this.agentIDField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=17)]
        public string password
        {
            get
            {
                return this.passwordField;
            }
            set
            {
                this.passwordField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=18)]
        public string validationRequired
        {
            get
            {
                return this.validationRequiredField;
            }
            set
            {
                this.validationRequiredField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=19)]
        public string CardHolderName
        {
            get
            {
                return this.cardHolderNameField;
            }
            set
            {
                this.cardHolderNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=20)]
        public string AuthorisationCode
        {
            get
            {
                return this.authorisationCodeField;
            }
            set
            {
                this.authorisationCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=21)]
        public string AuthorisationDate
        {
            get
            {
                return this.authorisationDateField;
            }
            set
            {
                this.authorisationDateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=22)]
        public string PaymentCurrency
        {
            get
            {
                return this.paymentCurrencyField;
            }
            set
            {
                this.paymentCurrencyField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=23)]
        public string PaymentId
        {
            get
            {
                return this.paymentIdField;
            }
            set
            {
                this.paymentIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=24)]
        public string PaymentStatus
        {
            get
            {
                return this.paymentStatusField;
            }
            set
            {
                this.paymentStatusField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=25)]
        public System.DateTime TransactionTime
        {
            get
            {
                return this.transactionTimeField;
            }
            set
            {
                this.transactionTimeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool TransactionTimeSpecified
        {
            get
            {
                return this.transactionTimeFieldSpecified;
            }
            set
            {
                this.transactionTimeFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=26)]
        public string TransactionTimeZone
        {
            get
            {
                return this.transactionTimeZoneField;
            }
            set
            {
                this.transactionTimeZoneField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=27)]
        public string IpAddress
        {
            get
            {
                return this.ipAddressField;
            }
            set
            {
                this.ipAddressField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=28)]
        public string EmailID
        {
            get
            {
                return this.emailIDField;
            }
            set
            {
                this.emailIDField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=29)]
        public string AffiliateID
        {
            get
            {
                return this.affiliateIDField;
            }
            set
            {
                this.affiliateIDField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=30)]
        public BillingAddress BillingAddress
        {
            get
            {
                return this.billingAddressField;
            }
            set
            {
                this.billingAddressField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=31)]
        public string TrackOne
        {
            get
            {
                return this.trackOneField;
            }
            set
            {
                this.trackOneField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=32)]
        public string TrackTwo
        {
            get
            {
                return this.trackTwoField;
            }
            set
            {
                this.trackTwoField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=33)]
        public long OandDId
        {
            get
            {
                return this.oandDIdField;
            }
            set
            {
                this.oandDIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool OandDIdSpecified
        {
            get
            {
                return this.oandDIdFieldSpecified;
            }
            set
            {
                this.oandDIdFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=34)]
        public string PaymentDescription
        {
            get
            {
                return this.paymentDescriptionField;
            }
            set
            {
                this.paymentDescriptionField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("PaymentElementType", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=35)]
        public PaymentElementType[] PaymentElementType
        {
            get
            {
                return this.paymentElementTypeField;
            }
            set
            {
                this.paymentElementTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=36)]
        public string ErrorCode
        {
            get
            {
                return this.errorCodeField;
            }
            set
            {
                this.errorCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=37)]
        public string ErrorDescription
        {
            get
            {
                return this.errorDescriptionField;
            }
            set
            {
                this.errorDescriptionField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=38)]
        public string TransactionID
        {
            get
            {
                return this.transactionIDField;
            }
            set
            {
                this.transactionIDField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=39)]
        public ThreeDSecurityDtls ThreeDSecurityDetails
        {
            get
            {
                return this.threeDSecurityDetailsField;
            }
            set
            {
                this.threeDSecurityDetailsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=40)]
        public string ConvenienceStoreCode
        {
            get
            {
                return this.convenienceStoreCodeField;
            }
            set
            {
                this.convenienceStoreCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=41)]
        public string ConvenienceStoreName
        {
            get
            {
                return this.convenienceStoreNameField;
            }
            set
            {
                this.convenienceStoreNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=42)]
        public string ConvenienceStoreType
        {
            get
            {
                return this.convenienceStoreTypeField;
            }
            set
            {
                this.convenienceStoreTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=43)]
        public string CustomerFirstName
        {
            get
            {
                return this.customerFirstNameField;
            }
            set
            {
                this.customerFirstNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=44)]
        public string CustomerLastName
        {
            get
            {
                return this.customerLastNameField;
            }
            set
            {
                this.customerLastNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=45)]
        public string CustomerPhoneNumber
        {
            get
            {
                return this.customerPhoneNumberField;
            }
            set
            {
                this.customerPhoneNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=46)]
        public string RecieptNumber
        {
            get
            {
                return this.recieptNumberField;
            }
            set
            {
                this.recieptNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=47)]
        public string ReceiptPrintURL
        {
            get
            {
                return this.receiptPrintURLField;
            }
            set
            {
                this.receiptPrintURLField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=48)]
        public string UsableCVSCompanycode
        {
            get
            {
                return this.usableCVSCompanycodeField;
            }
            set
            {
                this.usableCVSCompanycodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=49)]
        public string MaskedCreditcardNumber
        {
            get
            {
                return this.maskedCreditcardNumberField;
            }
            set
            {
                this.maskedCreditcardNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=50)]
        public AdditionalCCDetails additionalCCDetails
        {
            get
            {
                return this.additionalCCDetailsField;
            }
            set
            {
                this.additionalCCDetailsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=51)]
        public CashReceiptType CashReceiptType
        {
            get
            {
                return this.cashReceiptTypeField;
            }
            set
            {
                this.cashReceiptTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool CashReceiptTypeSpecified
        {
            get
            {
                return this.cashReceiptTypeFieldSpecified;
            }
            set
            {
                this.cashReceiptTypeFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=52)]
        public AdditionalCreditCardInfo AdditionalCreditCardInfo
        {
            get
            {
                return this.additionalCreditCardInfoField;
            }
            set
            {
                this.additionalCreditCardInfoField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=53)]
        public string OriginalAuthDate
        {
            get
            {
                return this.originalAuthDateField;
            }
            set
            {
                this.originalAuthDateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=54)]
        public string OriginalAuthCode
        {
            get
            {
                return this.originalAuthCodeField;
            }
            set
            {
                this.originalAuthCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=55)]
        public string customerId
        {
            get
            {
                return this.customerIdField;
            }
            set
            {
                this.customerIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=56)]
        public string ProcReturnCode
        {
            get
            {
                return this.procReturnCodeField;
            }
            set
            {
                this.procReturnCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=57)]
        public string PaymentMethod
        {
            get
            {
                return this.paymentMethodField;
            }
            set
            {
                this.paymentMethodField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=58)]
        public string TransactionKey
        {
            get
            {
                return this.transactionKeyField;
            }
            set
            {
                this.transactionKeyField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=59)]
        public string RequestURL
        {
            get
            {
                return this.requestURLField;
            }
            set
            {
                this.requestURLField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=60)]
        public string RedirectionURL
        {
            get
            {
                return this.redirectionURLField;
            }
            set
            {
                this.redirectionURLField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=61)]
        public string GatewayName
        {
            get
            {
                return this.gatewayNameField;
            }
            set
            {
                this.gatewayNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=62)]
        public string localeCountryCode
        {
            get
            {
                return this.localeCountryCodeField;
            }
            set
            {
                this.localeCountryCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=63)]
        public string LandingPage
        {
            get
            {
                return this.landingPageField;
            }
            set
            {
                this.landingPageField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=64)]
        public bool isPreAuthorized
        {
            get
            {
                return this.isPreAuthorizedField;
            }
            set
            {
                this.isPreAuthorizedField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool isPreAuthorizedSpecified
        {
            get
            {
                return this.isPreAuthorizedFieldSpecified;
            }
            set
            {
                this.isPreAuthorizedFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("CartDetailsType", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=65)]
        public CartDetailsType[] CartDetailsType
        {
            get
            {
                return this.cartDetailsTypeField;
            }
            set
            {
                this.cartDetailsTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=66)]
        public string SuccessURL
        {
            get
            {
                return this.successURLField;
            }
            set
            {
                this.successURLField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=67)]
        public string FailureURL
        {
            get
            {
                return this.failureURLField;
            }
            set
            {
                this.failureURLField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=68)]
        public string AccountHolderName
        {
            get
            {
                return this.accountHolderNameField;
            }
            set
            {
                this.accountHolderNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=69)]
        public string Iban
        {
            get
            {
                return this.ibanField;
            }
            set
            {
                this.ibanField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=70)]
        public string Bic
        {
            get
            {
                return this.bicField;
            }
            set
            {
                this.bicField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=71)]
        public string MobSuccessURL
        {
            get
            {
                return this.mobSuccessURLField;
            }
            set
            {
                this.mobSuccessURLField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=72)]
        public string MobCancelURL
        {
            get
            {
                return this.mobCancelURLField;
            }
            set
            {
                this.mobCancelURLField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=73)]
        public string MobSource
        {
            get
            {
                return this.mobSourceField;
            }
            set
            {
                this.mobSourceField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=74)]
        public string MobDeviceType
        {
            get
            {
                return this.mobDeviceTypeField;
            }
            set
            {
                this.mobDeviceTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=75)]
        public string MobOS
        {
            get
            {
                return this.mobOSField;
            }
            set
            {
                this.mobOSField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=76)]
        public string MobBrowser
        {
            get
            {
                return this.mobBrowserField;
            }
            set
            {
                this.mobBrowserField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=77)]
        public PNRType PnrType
        {
            get
            {
                return this.pnrTypeField;
            }
            set
            {
                this.pnrTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool PnrTypeSpecified
        {
            get
            {
                return this.pnrTypeFieldSpecified;
            }
            set
            {
                this.pnrTypeFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=78)]
        public string CustomerProfileId
        {
            get
            {
                return this.customerProfileIdField;
            }
            set
            {
                this.customerProfileIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=79)]
        public string AgencyCode
        {
            get
            {
                return this.agencyCodeField;
            }
            set
            {
                this.agencyCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=80)]
        [System.Xml.Serialization.XmlArrayItemAttribute("CustomField", typeof(CustomFieldType), Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=false)]
        public CustomFieldType[] CustomFieldList
        {
            get
            {
                return this.customFieldListField;
            }
            set
            {
                this.customFieldListField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=81)]
        public string ServiceInitiator
        {
            get
            {
                return this.serviceInitiatorField;
            }
            set
            {
                this.serviceInitiatorField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public enum FOPType
    {
        
        /// <remarks/>
        AG,
        
        /// <remarks/>
        CC,
        
        /// <remarks/>
        CF,
        
        /// <remarks/>
        CS,
        
        /// <remarks/>
        PA,
        
        /// <remarks/>
        CA,
        
        /// <remarks/>
        CK,
        
        /// <remarks/>
        DB,
        
        /// <remarks/>
        WO,
        
        /// <remarks/>
        GV,
        
        /// <remarks/>
        GC,
        
        /// <remarks/>
        LP,
        
        /// <remarks/>
        LT,
        
        /// <remarks/>
        TK,
        
        /// <remarks/>
        DD,
        
        /// <remarks/>
        CO,
        
        /// <remarks/>
        CR,
        
        /// <remarks/>
        PT,
        
        /// <remarks/>
        ET,
        
        /// <remarks/>
        IN,
        
        /// <remarks/>
        PP,
        
        /// <remarks/>
        WA,
        
        /// <remarks/>
        ED,
        
        /// <remarks/>
        CI,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public enum PaymentCode
    {
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("BANK TRANSFER")]
        BANKTRANSFER,
        
        /// <remarks/>
        IDEAL,
        
        /// <remarks/>
        RAZORPAY,
        
        /// <remarks/>
        VNPAY,
        
        /// <remarks/>
        CREDITCARD,
        
        /// <remarks/>
        ALEPAY,
        
        /// <remarks/>
        EGHLANY,
        
        /// <remarks/>
        EGHLCC,
        
        /// <remarks/>
        EGHLDD,
        
        /// <remarks/>
        EGHLWA,
        
        /// <remarks/>
        EGHLOTC,
        
        /// <remarks/>
        MOMO,
        
        /// <remarks/>
        BAVHUB,
        
        /// <remarks/>
        MOBILEXPRESS,
        
        /// <remarks/>
        SOFORT,
        
        /// <remarks/>
        SEPA,
        
        /// <remarks/>
        TOSS,
        
        /// <remarks/>
        IATAPAY,
        
        /// <remarks/>
        JUSPAY,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public enum WriteOffCodeType
    {
        
        /// <remarks/>
        ABSCC,
        
        /// <remarks/>
        ABSGUEST,
        
        /// <remarks/>
        ABSTA,
        
        /// <remarks/>
        ABBPAY,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public enum CardType
    {
        
        /// <remarks/>
        VISA,
        
        /// <remarks/>
        MASTERCARD,
        
        /// <remarks/>
        AMEX,
        
        /// <remarks/>
        UATP,
        
        /// <remarks/>
        DINERS,
        
        /// <remarks/>
        JCB,
        
        /// <remarks/>
        KOOKMIN,
        
        /// <remarks/>
        HYUNDAI,
        
        /// <remarks/>
        SHINHAN,
        
        /// <remarks/>
        EXBANK,
        
        /// <remarks/>
        LOTTE,
        
        /// <remarks/>
        SAMSUNG,
        
        /// <remarks/>
        BC,
        
        /// <remarks/>
        CREDITCARD,
        
        /// <remarks/>
        HANASK,
        
        /// <remarks/>
        NONGHYUP,
        
        /// <remarks/>
        DUMMY,
        
        /// <remarks/>
        PAYPAL,
        
        /// <remarks/>
        KAKAOPAY,
        
        /// <remarks/>
        MOBILETMONEY,
        
        /// <remarks/>
        ALIPAY,
        
        /// <remarks/>
        TENPAY,
        
        /// <remarks/>
        UNIONPAY,
        
        /// <remarks/>
        PAYSBUY,
        
        /// <remarks/>
        CVSPAYMENT,
        
        /// <remarks/>
        ABBCREDIT,
        
        /// <remarks/>
        WPCREDIT,
        
        /// <remarks/>
        AXESSCARD,
        
        /// <remarks/>
        BONUSCARD,
        
        /// <remarks/>
        WORLDCARD,
        
        /// <remarks/>
        MAXIMUMCARD,
        
        /// <remarks/>
        BKM,
        
        /// <remarks/>
        TROY,
        
        /// <remarks/>
        MOBILEPAY,
        
        /// <remarks/>
        PAYOO,
        
        /// <remarks/>
        EC,
        
        /// <remarks/>
        WECHATPAY,
        
        /// <remarks/>
        NAVERPAY,
        
        /// <remarks/>
        BA,
        
        /// <remarks/>
        VBA,
        
        /// <remarks/>
        QUNAR,
        
        /// <remarks/>
        DCKRW,
        
        /// <remarks/>
        DCOTH,
        
        /// <remarks/>
        OCKRW,
        
        /// <remarks/>
        OCOTH,
        
        /// <remarks/>
        NTTDATACREDIT,
        
        /// <remarks/>
        VISAAPPLEPAY,
        
        /// <remarks/>
        MASTERAPPLEPAY,
        
        /// <remarks/>
        AMEXAPPLEPAY,
        
        /// <remarks/>
        ICCARD,
        
        /// <remarks/>
        PAYCO,
        
        /// <remarks/>
        TOSS,
        
        /// <remarks/>
        TWAYPAY,
        
        /// <remarks/>
        NKAKAOPAY,
        
        /// <remarks/>
        LINEPAY,
        
        /// <remarks/>
        MISCELLANEOUS,
        
        /// <remarks/>
        DEBITCARD,
        
        /// <remarks/>
        KBKOOKMIN,
        
        /// <remarks/>
        KEBHANA,
        
        /// <remarks/>
        VISAINTL,
        
        /// <remarks/>
        JCBINTL,
        
        /// <remarks/>
        DINERSCLUB,
        
        /// <remarks/>
        LOTTEINTL,
        
        /// <remarks/>
        MASTERINTL,
        
        /// <remarks/>
        NH,
        
        /// <remarks/>
        CITI,
        
        /// <remarks/>
        WOORI,
        
        /// <remarks/>
        KWANGJU,
        
        /// <remarks/>
        SUHYUP,
        
        /// <remarks/>
        JEONBUK,
        
        /// <remarks/>
        JEJU,
        
        /// <remarks/>
        CHOHUNG,
        
        /// <remarks/>
        KDB,
        
        /// <remarks/>
        CARDPAY,
        
        /// <remarks/>
        PAYSAFE,
        
        /// <remarks/>
        SMILEPAY,
        
        /// <remarks/>
        PAYPAY,
        
        /// <remarks/>
        SSGPAY,
        
        /// <remarks/>
        EASYWELLPOINT,
        
        /// <remarks/>
        EASYPAY,
        
        /// <remarks/>
        UNION,
        
        /// <remarks/>
        BCCARD,
        
        /// <remarks/>
        PROMOTION,
        
        /// <remarks/>
        MOBILEXPRESS,
        
        /// <remarks/>
        TMONEY,
        
        /// <remarks/>
        TMONEYB,
        
        /// <remarks/>
        JINAIRPAY,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public enum CashReceiptType
    {
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("00")]
        Item00,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("01")]
        Item01,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public enum PNRType
    {
        
        /// <remarks/>
        NORMAL,
        
        /// <remarks/>
        GROUP,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class CustomFieldType
    {
        
        private CustomNameFieldType nameField;
        
        private string valueField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public CustomNameFieldType Name
        {
            get
            {
                return this.nameField;
            }
            set
            {
                this.nameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string Value
        {
            get
            {
                return this.valueField;
            }
            set
            {
                this.valueField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public enum CustomNameFieldType
    {
        
        /// <remarks/>
        PROFILE_TYPE,
        
        /// <remarks/>
        CARD_SEQ_NUM,
        
        /// <remarks/>
        TRANSACTION_ID,
        
        /// <remarks/>
        IS_ADC,
        
        /// <remarks/>
        TRIP_TYPE,
        
        /// <remarks/>
        PAYMENT_REQ_EXTRA_PARAMS,
        
        /// <remarks/>
        PROFILE_ID,
        
        /// <remarks/>
        PAYMENT_TOKEN_ID,
        
        /// <remarks/>
        LOYALTY_ID,
        
        /// <remarks/>
        CUSTOMER_CARD_ID,
        
        /// <remarks/>
        PARTIALLY_FLOWN_IND,
        
        /// <remarks/>
        REPRICE_DATE_GMT,
        
        /// <remarks/>
        REPRICE_DATE_GMT_NON_TKTED,
        
        /// <remarks/>
        TOKEN_GENERATION_KEY,
        
        /// <remarks/>
        TOKEN_GENERATION_URL,
        
        /// <remarks/>
        PAYMENT_ORDER_ID,
        
        /// <remarks/>
        PAYBACK_CARD_NUM,
        
        /// <remarks/>
        PAYBACK_COUPON_NUM,
        
        /// <remarks/>
        OFFER_META_DATA,
        
        /// <remarks/>
        ANCILLARY_OFFERMETA_DATA,
        
        /// <remarks/>
        BAGGAGE_OFFER_METADATA,
        
        /// <remarks/>
        SUCCESS_URL,
        
        /// <remarks/>
        FAILURE_URL,
        
        /// <remarks/>
        LOCALE_COUNTRY_CODE,
        
        /// <remarks/>
        IS_MOTO_TX,
        
        /// <remarks/>
        PAYMENT_GATEWAY_SUPPLEMENTARY_INFO,
        
        /// <remarks/>
        PNR_NUMBER,
        
        /// <remarks/>
        RLOC,
        
        /// <remarks/>
        INTERLINE_PARTNER_TYPE,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class CreatedGiftVoucherDetails
    {
        
        private string giftVoucherNumberField;
        
        private double giftVoucherValueField;
        
        private string giftVoucherCurrencyCodeField;
        
        private GiftVoucherTax[] giftVoucherTaxDetailsField;
        
        private string gcSeqIdField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string giftVoucherNumber
        {
            get
            {
                return this.giftVoucherNumberField;
            }
            set
            {
                this.giftVoucherNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public double giftVoucherValue
        {
            get
            {
                return this.giftVoucherValueField;
            }
            set
            {
                this.giftVoucherValueField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string giftVoucherCurrencyCode
        {
            get
            {
                return this.giftVoucherCurrencyCodeField;
            }
            set
            {
                this.giftVoucherCurrencyCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("GiftVoucherTaxDetails", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public GiftVoucherTax[] GiftVoucherTaxDetails
        {
            get
            {
                return this.giftVoucherTaxDetailsField;
            }
            set
            {
                this.giftVoucherTaxDetailsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string gcSeqId
        {
            get
            {
                return this.gcSeqIdField;
            }
            set
            {
                this.gcSeqIdField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class GiftVoucherTax
    {
        
        private string taxCodeField;
        
        private double taxAmountField;
        
        private string currencyField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string TaxCode
        {
            get
            {
                return this.taxCodeField;
            }
            set
            {
                this.taxCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public double TaxAmount
        {
            get
            {
                return this.taxAmountField;
            }
            set
            {
                this.taxAmountField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string Currency
        {
            get
            {
                return this.currencyField;
            }
            set
            {
                this.currencyField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class GVRecipientInformation
    {
        
        private GiftVoucherNameAndAddress nameAndAddressField;
        
        private RecipientType recipientTypeField;
        
        private string recipientProfileIdField;
        
        private string membershipNumberField;
        
        private string passwordField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public GiftVoucherNameAndAddress nameAndAddress
        {
            get
            {
                return this.nameAndAddressField;
            }
            set
            {
                this.nameAndAddressField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public RecipientType recipientType
        {
            get
            {
                return this.recipientTypeField;
            }
            set
            {
                this.recipientTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string recipientProfileId
        {
            get
            {
                return this.recipientProfileIdField;
            }
            set
            {
                this.recipientProfileIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string membershipNumber
        {
            get
            {
                return this.membershipNumberField;
            }
            set
            {
                this.membershipNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string password
        {
            get
            {
                return this.passwordField;
            }
            set
            {
                this.passwordField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class GiftVoucherNameAndAddress
    {
        
        private string titleField;
        
        private string firstNameField;
        
        private string lastNameField;
        
        private string organizationField;
        
        private string addressOneField;
        
        private string addressTwoField;
        
        private string streetField;
        
        private string townField;
        
        private string stateField;
        
        private string countryField;
        
        private string zipCodeField;
        
        private string homePhoneField;
        
        private string workPhoneField;
        
        private string mobilePhoneField;
        
        private string isdHomePhoneCodeField;
        
        private string isdWorkPhoneCodeField;
        
        private string isdMobilePhoneCodeField;
        
        private string faxField;
        
        private string emailField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string title
        {
            get
            {
                return this.titleField;
            }
            set
            {
                this.titleField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string firstName
        {
            get
            {
                return this.firstNameField;
            }
            set
            {
                this.firstNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string lastName
        {
            get
            {
                return this.lastNameField;
            }
            set
            {
                this.lastNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string organization
        {
            get
            {
                return this.organizationField;
            }
            set
            {
                this.organizationField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string addressOne
        {
            get
            {
                return this.addressOneField;
            }
            set
            {
                this.addressOneField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string addressTwo
        {
            get
            {
                return this.addressTwoField;
            }
            set
            {
                this.addressTwoField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string street
        {
            get
            {
                return this.streetField;
            }
            set
            {
                this.streetField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string town
        {
            get
            {
                return this.townField;
            }
            set
            {
                this.townField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public string state
        {
            get
            {
                return this.stateField;
            }
            set
            {
                this.stateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public string country
        {
            get
            {
                return this.countryField;
            }
            set
            {
                this.countryField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=10)]
        public string zipCode
        {
            get
            {
                return this.zipCodeField;
            }
            set
            {
                this.zipCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=11)]
        public string homePhone
        {
            get
            {
                return this.homePhoneField;
            }
            set
            {
                this.homePhoneField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=12)]
        public string workPhone
        {
            get
            {
                return this.workPhoneField;
            }
            set
            {
                this.workPhoneField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=13)]
        public string mobilePhone
        {
            get
            {
                return this.mobilePhoneField;
            }
            set
            {
                this.mobilePhoneField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=14)]
        public string isdHomePhoneCode
        {
            get
            {
                return this.isdHomePhoneCodeField;
            }
            set
            {
                this.isdHomePhoneCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=15)]
        public string IsdWorkPhoneCode
        {
            get
            {
                return this.isdWorkPhoneCodeField;
            }
            set
            {
                this.isdWorkPhoneCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=16)]
        public string isdMobilePhoneCode
        {
            get
            {
                return this.isdMobilePhoneCodeField;
            }
            set
            {
                this.isdMobilePhoneCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=17)]
        public string fax
        {
            get
            {
                return this.faxField;
            }
            set
            {
                this.faxField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=18)]
        public string email
        {
            get
            {
                return this.emailField;
            }
            set
            {
                this.emailField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class GVPurchaserInformation
    {
        
        private string purchaserTypeField;
        
        private string purchaserTypeValueField;
        
        private GiftVoucherNameAndAddress nameAndAddressField;
        
        private string loyaltyNumberField;
        
        private string loyaltyBonusCodeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string purchaserType
        {
            get
            {
                return this.purchaserTypeField;
            }
            set
            {
                this.purchaserTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string purchaserTypeValue
        {
            get
            {
                return this.purchaserTypeValueField;
            }
            set
            {
                this.purchaserTypeValueField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public GiftVoucherNameAndAddress nameAndAddress
        {
            get
            {
                return this.nameAndAddressField;
            }
            set
            {
                this.nameAndAddressField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string loyaltyNumber
        {
            get
            {
                return this.loyaltyNumberField;
            }
            set
            {
                this.loyaltyNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string loyaltyBonusCode
        {
            get
            {
                return this.loyaltyBonusCodeField;
            }
            set
            {
                this.loyaltyBonusCodeField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class ShopingCartDetailsType
    {
        
        private string cartNameField;
        
        private double cartAmountField;
        
        private bool cartAmountFieldSpecified;
        
        private int cartQtyField;
        
        private bool cartQtyFieldSpecified;
        
        private int cartOrderField;
        
        private bool cartOrderFieldSpecified;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string CartName
        {
            get
            {
                return this.cartNameField;
            }
            set
            {
                this.cartNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public double CartAmount
        {
            get
            {
                return this.cartAmountField;
            }
            set
            {
                this.cartAmountField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool CartAmountSpecified
        {
            get
            {
                return this.cartAmountFieldSpecified;
            }
            set
            {
                this.cartAmountFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public int CartQty
        {
            get
            {
                return this.cartQtyField;
            }
            set
            {
                this.cartQtyField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool CartQtySpecified
        {
            get
            {
                return this.cartQtyFieldSpecified;
            }
            set
            {
                this.cartQtyFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public int CartOrder
        {
            get
            {
                return this.cartOrderField;
            }
            set
            {
                this.cartOrderField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool CartOrderSpecified
        {
            get
            {
                return this.cartOrderFieldSpecified;
            }
            set
            {
                this.cartOrderFieldSpecified = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class AdditionalCreditCardInfoType
    {
        
        private string registrationNumberField;
        
        private string authTypeField;
        
        private string installmentPeriodField;
        
        private AuthenticationDetailsType authenticationTypeField;
        
        private bool authenticationTypeFieldSpecified;
        
        private string cardPasswordField;
        
        private int goodsCountField;
        
        private bool goodsCountFieldSpecified;
        
        private string goodsNameField;
        
        private string customerUserIDField;
        
        private string threeDNotificationURLField;
        
        private string cSSURLField;
        
        private string paymentPageTemplateField;
        
        private string posMessageField;
        
        private string timeStampField;
        
        private string signatureField;
        
        private string bankIdField;
        
        private string bankNameField;
        
        private string cardBinField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string RegistrationNumber
        {
            get
            {
                return this.registrationNumberField;
            }
            set
            {
                this.registrationNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string AuthType
        {
            get
            {
                return this.authTypeField;
            }
            set
            {
                this.authTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string InstallmentPeriod
        {
            get
            {
                return this.installmentPeriodField;
            }
            set
            {
                this.installmentPeriodField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public AuthenticationDetailsType AuthenticationType
        {
            get
            {
                return this.authenticationTypeField;
            }
            set
            {
                this.authenticationTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool AuthenticationTypeSpecified
        {
            get
            {
                return this.authenticationTypeFieldSpecified;
            }
            set
            {
                this.authenticationTypeFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string CardPassword
        {
            get
            {
                return this.cardPasswordField;
            }
            set
            {
                this.cardPasswordField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public int GoodsCount
        {
            get
            {
                return this.goodsCountField;
            }
            set
            {
                this.goodsCountField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool GoodsCountSpecified
        {
            get
            {
                return this.goodsCountFieldSpecified;
            }
            set
            {
                this.goodsCountFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string GoodsName
        {
            get
            {
                return this.goodsNameField;
            }
            set
            {
                this.goodsNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string CustomerUserID
        {
            get
            {
                return this.customerUserIDField;
            }
            set
            {
                this.customerUserIDField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public string ThreeDNotificationURL
        {
            get
            {
                return this.threeDNotificationURLField;
            }
            set
            {
                this.threeDNotificationURLField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public string CSSURL
        {
            get
            {
                return this.cSSURLField;
            }
            set
            {
                this.cSSURLField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=10)]
        public string PaymentPageTemplate
        {
            get
            {
                return this.paymentPageTemplateField;
            }
            set
            {
                this.paymentPageTemplateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=11)]
        public string PosMessage
        {
            get
            {
                return this.posMessageField;
            }
            set
            {
                this.posMessageField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=12)]
        public string TimeStamp
        {
            get
            {
                return this.timeStampField;
            }
            set
            {
                this.timeStampField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=13)]
        public string Signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=14)]
        public string BankId
        {
            get
            {
                return this.bankIdField;
            }
            set
            {
                this.bankIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=15)]
        public string BankName
        {
            get
            {
                return this.bankNameField;
            }
            set
            {
                this.bankNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=16)]
        public string CardBin
        {
            get
            {
                return this.cardBinField;
            }
            set
            {
                this.cardBinField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public enum AuthenticationDetailsType
    {
        
        /// <remarks/>
        JJ,
        
        /// <remarks/>
        BB,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class ThreeDSecurityDtlsType
    {
        
        private string paReqField;
        
        private string paResField;
        
        private string acsURLField;
        
        private string returnURLField;
        
        private string httpAcceptField;
        
        private string httpUserAgentField;
        
        private string xidField;
        
        private string mdstatusField;
        
        private string mderrormessageField;
        
        private string txnstatusField;
        
        private string eciField;
        
        private string cavvField;
        
        private string mdField;
        
        private string authcodeField;
        
        private string responseField;
        
        private string errmsgField;
        
        private string hostmsgField;
        
        private string procreturncodeField;
        
        private string hostrefnumField;
        
        private string transidField;
        
        private string maskedPanField;
        
        private string rndField;
        
        private string hashField;
        
        private string hashparamsField;
        
        private string hashparamsvalField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string paReq
        {
            get
            {
                return this.paReqField;
            }
            set
            {
                this.paReqField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string paRes
        {
            get
            {
                return this.paResField;
            }
            set
            {
                this.paResField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string acsURL
        {
            get
            {
                return this.acsURLField;
            }
            set
            {
                this.acsURLField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string returnURL
        {
            get
            {
                return this.returnURLField;
            }
            set
            {
                this.returnURLField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string httpAccept
        {
            get
            {
                return this.httpAcceptField;
            }
            set
            {
                this.httpAcceptField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string httpUserAgent
        {
            get
            {
                return this.httpUserAgentField;
            }
            set
            {
                this.httpUserAgentField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string xid
        {
            get
            {
                return this.xidField;
            }
            set
            {
                this.xidField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string mdstatus
        {
            get
            {
                return this.mdstatusField;
            }
            set
            {
                this.mdstatusField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public string mderrormessage
        {
            get
            {
                return this.mderrormessageField;
            }
            set
            {
                this.mderrormessageField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public string txnstatus
        {
            get
            {
                return this.txnstatusField;
            }
            set
            {
                this.txnstatusField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=10)]
        public string eci
        {
            get
            {
                return this.eciField;
            }
            set
            {
                this.eciField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=11)]
        public string cavv
        {
            get
            {
                return this.cavvField;
            }
            set
            {
                this.cavvField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=12)]
        public string md
        {
            get
            {
                return this.mdField;
            }
            set
            {
                this.mdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=13)]
        public string authcode
        {
            get
            {
                return this.authcodeField;
            }
            set
            {
                this.authcodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=14)]
        public string response
        {
            get
            {
                return this.responseField;
            }
            set
            {
                this.responseField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=15)]
        public string errmsg
        {
            get
            {
                return this.errmsgField;
            }
            set
            {
                this.errmsgField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=16)]
        public string hostmsg
        {
            get
            {
                return this.hostmsgField;
            }
            set
            {
                this.hostmsgField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=17)]
        public string procreturncode
        {
            get
            {
                return this.procreturncodeField;
            }
            set
            {
                this.procreturncodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=18)]
        public string hostrefnum
        {
            get
            {
                return this.hostrefnumField;
            }
            set
            {
                this.hostrefnumField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=19)]
        public string transid
        {
            get
            {
                return this.transidField;
            }
            set
            {
                this.transidField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=20)]
        public string maskedPan
        {
            get
            {
                return this.maskedPanField;
            }
            set
            {
                this.maskedPanField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=21)]
        public string rnd
        {
            get
            {
                return this.rndField;
            }
            set
            {
                this.rndField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=22)]
        public string hash
        {
            get
            {
                return this.hashField;
            }
            set
            {
                this.hashField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=23)]
        public string hashparams
        {
            get
            {
                return this.hashparamsField;
            }
            set
            {
                this.hashparamsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=24)]
        public string hashparamsval
        {
            get
            {
                return this.hashparamsvalField;
            }
            set
            {
                this.hashparamsvalField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class PaymentElementDetailsType
    {
        
        private long elementIdField;
        
        private string elementTypeField;
        
        private string elementSubTypeField;
        
        private double amountField;
        
        private bool amountFieldSpecified;
        
        private string currencyField;
        
        private double exchangeRateField;
        
        private bool exchangeRateFieldSpecified;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public long ElementId
        {
            get
            {
                return this.elementIdField;
            }
            set
            {
                this.elementIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string ElementType
        {
            get
            {
                return this.elementTypeField;
            }
            set
            {
                this.elementTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string ElementSubType
        {
            get
            {
                return this.elementSubTypeField;
            }
            set
            {
                this.elementSubTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public double Amount
        {
            get
            {
                return this.amountField;
            }
            set
            {
                this.amountField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool AmountSpecified
        {
            get
            {
                return this.amountFieldSpecified;
            }
            set
            {
                this.amountFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string Currency
        {
            get
            {
                return this.currencyField;
            }
            set
            {
                this.currencyField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public double ExchangeRate
        {
            get
            {
                return this.exchangeRateField;
            }
            set
            {
                this.exchangeRateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool ExchangeRateSpecified
        {
            get
            {
                return this.exchangeRateFieldSpecified;
            }
            set
            {
                this.exchangeRateFieldSpecified = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class BillingAddressType
    {
        
        private string addressOneField;
        
        private string addressTwoField;
        
        private string cityNameField;
        
        private string countryNameField;
        
        private string provinceField;
        
        private string zipCodeField;
        
        private string phoneNumberField;
        
        private string phoneNumberCountryCodeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string addressOne
        {
            get
            {
                return this.addressOneField;
            }
            set
            {
                this.addressOneField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string addressTwo
        {
            get
            {
                return this.addressTwoField;
            }
            set
            {
                this.addressTwoField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string cityName
        {
            get
            {
                return this.cityNameField;
            }
            set
            {
                this.cityNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string countryName
        {
            get
            {
                return this.countryNameField;
            }
            set
            {
                this.countryNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string province
        {
            get
            {
                return this.provinceField;
            }
            set
            {
                this.provinceField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string zipCode
        {
            get
            {
                return this.zipCodeField;
            }
            set
            {
                this.zipCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string phoneNumber
        {
            get
            {
                return this.phoneNumberField;
            }
            set
            {
                this.phoneNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string phoneNumberCountryCode
        {
            get
            {
                return this.phoneNumberCountryCodeField;
            }
            set
            {
                this.phoneNumberCountryCodeField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class DccPaymentDetailsType
    {
        
        private bool isDCCOfferedField;
        
        private bool hasUserAcceptedField;
        
        private string dccMerchantIDField;
        
        private double dccAmountField;
        
        private string dccCurrencyField;
        
        private double dccExchangeRateField;
        
        private string statusField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public bool isDCCOffered
        {
            get
            {
                return this.isDCCOfferedField;
            }
            set
            {
                this.isDCCOfferedField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public bool hasUserAccepted
        {
            get
            {
                return this.hasUserAcceptedField;
            }
            set
            {
                this.hasUserAcceptedField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string dccMerchantID
        {
            get
            {
                return this.dccMerchantIDField;
            }
            set
            {
                this.dccMerchantIDField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public double dccAmount
        {
            get
            {
                return this.dccAmountField;
            }
            set
            {
                this.dccAmountField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string dccCurrency
        {
            get
            {
                return this.dccCurrencyField;
            }
            set
            {
                this.dccCurrencyField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public double dccExchangeRate
        {
            get
            {
                return this.dccExchangeRateField;
            }
            set
            {
                this.dccExchangeRateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string status
        {
            get
            {
                return this.statusField;
            }
            set
            {
                this.statusField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class GuestPaymentInfoType
    {
        
        private long guestIdField;
        
        private bool guestIdFieldSpecified;
        
        private Payment_Type formOfPaymentCodeField;
        
        private double paymentAmountField;
        
        private double exchangeRateField;
        
        private bool exchangeRateFieldSpecified;
        
        private WriteOffCodeDetails_Type writeOffCodeField;
        
        private bool writeOffCodeFieldSpecified;
        
        private string writeOffReasonField;
        
        private CardDetails_Type cardTypeField;
        
        private bool cardTypeFieldSpecified;
        
        private string paymentCardTypeField;
        
        private string paymentTypeNumberField;
        
        private string authenticationTxnIdField;
        
        private string membershipNumberField;
        
        private string expirationMonthField;
        
        private string expirationYearField;
        
        private DccPaymentDetailsType dccDetailsTypeField;
        
        private string cvv2NumberField;
        
        private string agentIDField;
        
        private string passwordField;
        
        private string validationRequiredField;
        
        private string cardHolderNameField;
        
        private string authorisationCodeField;
        
        private string authorisationDateField;
        
        private string paymentCurrencyField;
        
        private string paymentIdField;
        
        private string paymentStatusField;
        
        private System.DateTime transactionTimeField;
        
        private bool transactionTimeFieldSpecified;
        
        private string transactionTimeZoneField;
        
        private string ipAddressField;
        
        private string emailIDField;
        
        private BillingAddressType billingAddressField;
        
        private string trackOneField;
        
        private string trackTwoField;
        
        private string paymentDescriptionField;
        
        private PaymentElementDetailsType[] paymentElementTypeField;
        
        private string errorCodeField;
        
        private string errorDescriptionField;
        
        private string transactionIDField;
        
        private ThreeDSecurityDtlsType threeDSecurityDetailsField;
        
        private string convenienceStoreCodeField;
        
        private string convenienceStoreNameField;
        
        private string convenienceStoreTypeField;
        
        private string customerFirstNameField;
        
        private string customerLastNameField;
        
        private string customerPhoneNumberField;
        
        private string recieptNumberField;
        
        private string receiptPrintURLField;
        
        private string usableCVSCompanycodeField;
        
        private string maskedCreditcardNumberField;
        
        private AdditionalCreditCardInfoType additionalCreditCardInfoField;
        
        private CashReceiptDetailsType cashReceiptTypeField;
        
        private bool cashReceiptTypeFieldSpecified;
        
        private string originalAuthDateField;
        
        private string originalAuthCodeField;
        
        private string customerIdField;
        
        private string procReturnCodeField;
        
        private string paymentMethodField;
        
        private string transactionKeyField;
        
        private string requestURLField;
        
        private string redirectionURLField;
        
        private string gatewayNameField;
        
        private string localeCountryCodeField;
        
        private string landingPageField;
        
        private bool isPreAuthorizedField;
        
        private bool isPreAuthorizedFieldSpecified;
        
        private ShopingCartDetailsType[] cartDetailsTypeField;
        
        private string iBANField;
        
        private string bICField;
        
        private string accountHolderNameField;
        
        private System.DateTime signedDateField;
        
        private bool signedDateFieldSpecified;
        
        private System.DateTime txnTimeInAgentOfficeTimeZoneField;
        
        private bool txnTimeInAgentOfficeTimeZoneFieldSpecified;
        
        private string customerProfileIdField;
        
        private string agencyCodeField;
        
        private CustomFieldType[] customFieldListField;
        
        private string paymentCodeField;
        
        private string paymentSystemCodeField;
        
        private string authorizationSequenceNumberField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public long GuestId
        {
            get
            {
                return this.guestIdField;
            }
            set
            {
                this.guestIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool GuestIdSpecified
        {
            get
            {
                return this.guestIdFieldSpecified;
            }
            set
            {
                this.guestIdFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public Payment_Type FormOfPaymentCode
        {
            get
            {
                return this.formOfPaymentCodeField;
            }
            set
            {
                this.formOfPaymentCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public double PaymentAmount
        {
            get
            {
                return this.paymentAmountField;
            }
            set
            {
                this.paymentAmountField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public double ExchangeRate
        {
            get
            {
                return this.exchangeRateField;
            }
            set
            {
                this.exchangeRateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool ExchangeRateSpecified
        {
            get
            {
                return this.exchangeRateFieldSpecified;
            }
            set
            {
                this.exchangeRateFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public WriteOffCodeDetails_Type WriteOffCode
        {
            get
            {
                return this.writeOffCodeField;
            }
            set
            {
                this.writeOffCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool WriteOffCodeSpecified
        {
            get
            {
                return this.writeOffCodeFieldSpecified;
            }
            set
            {
                this.writeOffCodeFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string WriteOffReason
        {
            get
            {
                return this.writeOffReasonField;
            }
            set
            {
                this.writeOffReasonField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public CardDetails_Type CardType
        {
            get
            {
                return this.cardTypeField;
            }
            set
            {
                this.cardTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool CardTypeSpecified
        {
            get
            {
                return this.cardTypeFieldSpecified;
            }
            set
            {
                this.cardTypeFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string PaymentCardType
        {
            get
            {
                return this.paymentCardTypeField;
            }
            set
            {
                this.paymentCardTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public string paymentTypeNumber
        {
            get
            {
                return this.paymentTypeNumberField;
            }
            set
            {
                this.paymentTypeNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public string authenticationTxnId
        {
            get
            {
                return this.authenticationTxnIdField;
            }
            set
            {
                this.authenticationTxnIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=10)]
        public string membershipNumber
        {
            get
            {
                return this.membershipNumberField;
            }
            set
            {
                this.membershipNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=11)]
        public string ExpirationMonth
        {
            get
            {
                return this.expirationMonthField;
            }
            set
            {
                this.expirationMonthField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=12)]
        public string ExpirationYear
        {
            get
            {
                return this.expirationYearField;
            }
            set
            {
                this.expirationYearField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=13)]
        public DccPaymentDetailsType DccDetailsType
        {
            get
            {
                return this.dccDetailsTypeField;
            }
            set
            {
                this.dccDetailsTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=14)]
        public string cvv2Number
        {
            get
            {
                return this.cvv2NumberField;
            }
            set
            {
                this.cvv2NumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=15)]
        public string agentID
        {
            get
            {
                return this.agentIDField;
            }
            set
            {
                this.agentIDField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=16)]
        public string password
        {
            get
            {
                return this.passwordField;
            }
            set
            {
                this.passwordField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=17)]
        public string validationRequired
        {
            get
            {
                return this.validationRequiredField;
            }
            set
            {
                this.validationRequiredField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=18)]
        public string CardHolderName
        {
            get
            {
                return this.cardHolderNameField;
            }
            set
            {
                this.cardHolderNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=19)]
        public string AuthorisationCode
        {
            get
            {
                return this.authorisationCodeField;
            }
            set
            {
                this.authorisationCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=20)]
        public string AuthorisationDate
        {
            get
            {
                return this.authorisationDateField;
            }
            set
            {
                this.authorisationDateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=21)]
        public string PaymentCurrency
        {
            get
            {
                return this.paymentCurrencyField;
            }
            set
            {
                this.paymentCurrencyField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=22)]
        public string PaymentId
        {
            get
            {
                return this.paymentIdField;
            }
            set
            {
                this.paymentIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=23)]
        public string PaymentStatus
        {
            get
            {
                return this.paymentStatusField;
            }
            set
            {
                this.paymentStatusField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=24)]
        public System.DateTime TransactionTime
        {
            get
            {
                return this.transactionTimeField;
            }
            set
            {
                this.transactionTimeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool TransactionTimeSpecified
        {
            get
            {
                return this.transactionTimeFieldSpecified;
            }
            set
            {
                this.transactionTimeFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=25)]
        public string TransactionTimeZone
        {
            get
            {
                return this.transactionTimeZoneField;
            }
            set
            {
                this.transactionTimeZoneField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=26)]
        public string IpAddress
        {
            get
            {
                return this.ipAddressField;
            }
            set
            {
                this.ipAddressField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=27)]
        public string EmailID
        {
            get
            {
                return this.emailIDField;
            }
            set
            {
                this.emailIDField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=28)]
        public BillingAddressType BillingAddress
        {
            get
            {
                return this.billingAddressField;
            }
            set
            {
                this.billingAddressField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=29)]
        public string TrackOne
        {
            get
            {
                return this.trackOneField;
            }
            set
            {
                this.trackOneField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=30)]
        public string TrackTwo
        {
            get
            {
                return this.trackTwoField;
            }
            set
            {
                this.trackTwoField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=31)]
        public string PaymentDescription
        {
            get
            {
                return this.paymentDescriptionField;
            }
            set
            {
                this.paymentDescriptionField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("PaymentElementType", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=32)]
        public PaymentElementDetailsType[] PaymentElementType
        {
            get
            {
                return this.paymentElementTypeField;
            }
            set
            {
                this.paymentElementTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=33)]
        public string ErrorCode
        {
            get
            {
                return this.errorCodeField;
            }
            set
            {
                this.errorCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=34)]
        public string ErrorDescription
        {
            get
            {
                return this.errorDescriptionField;
            }
            set
            {
                this.errorDescriptionField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=35)]
        public string TransactionID
        {
            get
            {
                return this.transactionIDField;
            }
            set
            {
                this.transactionIDField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=36)]
        public ThreeDSecurityDtlsType ThreeDSecurityDetails
        {
            get
            {
                return this.threeDSecurityDetailsField;
            }
            set
            {
                this.threeDSecurityDetailsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=37)]
        public string ConvenienceStoreCode
        {
            get
            {
                return this.convenienceStoreCodeField;
            }
            set
            {
                this.convenienceStoreCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=38)]
        public string ConvenienceStoreName
        {
            get
            {
                return this.convenienceStoreNameField;
            }
            set
            {
                this.convenienceStoreNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=39)]
        public string ConvenienceStoreType
        {
            get
            {
                return this.convenienceStoreTypeField;
            }
            set
            {
                this.convenienceStoreTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=40)]
        public string CustomerFirstName
        {
            get
            {
                return this.customerFirstNameField;
            }
            set
            {
                this.customerFirstNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=41)]
        public string CustomerLastName
        {
            get
            {
                return this.customerLastNameField;
            }
            set
            {
                this.customerLastNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=42)]
        public string CustomerPhoneNumber
        {
            get
            {
                return this.customerPhoneNumberField;
            }
            set
            {
                this.customerPhoneNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=43)]
        public string RecieptNumber
        {
            get
            {
                return this.recieptNumberField;
            }
            set
            {
                this.recieptNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=44)]
        public string ReceiptPrintURL
        {
            get
            {
                return this.receiptPrintURLField;
            }
            set
            {
                this.receiptPrintURLField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=45)]
        public string UsableCVSCompanycode
        {
            get
            {
                return this.usableCVSCompanycodeField;
            }
            set
            {
                this.usableCVSCompanycodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=46)]
        public string MaskedCreditcardNumber
        {
            get
            {
                return this.maskedCreditcardNumberField;
            }
            set
            {
                this.maskedCreditcardNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=47)]
        public AdditionalCreditCardInfoType AdditionalCreditCardInfo
        {
            get
            {
                return this.additionalCreditCardInfoField;
            }
            set
            {
                this.additionalCreditCardInfoField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=48)]
        public CashReceiptDetailsType CashReceiptType
        {
            get
            {
                return this.cashReceiptTypeField;
            }
            set
            {
                this.cashReceiptTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool CashReceiptTypeSpecified
        {
            get
            {
                return this.cashReceiptTypeFieldSpecified;
            }
            set
            {
                this.cashReceiptTypeFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=49)]
        public string OriginalAuthDate
        {
            get
            {
                return this.originalAuthDateField;
            }
            set
            {
                this.originalAuthDateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=50)]
        public string OriginalAuthCode
        {
            get
            {
                return this.originalAuthCodeField;
            }
            set
            {
                this.originalAuthCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=51)]
        public string customerId
        {
            get
            {
                return this.customerIdField;
            }
            set
            {
                this.customerIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=52)]
        public string ProcReturnCode
        {
            get
            {
                return this.procReturnCodeField;
            }
            set
            {
                this.procReturnCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=53)]
        public string PaymentMethod
        {
            get
            {
                return this.paymentMethodField;
            }
            set
            {
                this.paymentMethodField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=54)]
        public string TransactionKey
        {
            get
            {
                return this.transactionKeyField;
            }
            set
            {
                this.transactionKeyField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=55)]
        public string RequestURL
        {
            get
            {
                return this.requestURLField;
            }
            set
            {
                this.requestURLField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=56)]
        public string RedirectionURL
        {
            get
            {
                return this.redirectionURLField;
            }
            set
            {
                this.redirectionURLField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=57)]
        public string GatewayName
        {
            get
            {
                return this.gatewayNameField;
            }
            set
            {
                this.gatewayNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=58)]
        public string localeCountryCode
        {
            get
            {
                return this.localeCountryCodeField;
            }
            set
            {
                this.localeCountryCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=59)]
        public string LandingPage
        {
            get
            {
                return this.landingPageField;
            }
            set
            {
                this.landingPageField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=60)]
        public bool isPreAuthorized
        {
            get
            {
                return this.isPreAuthorizedField;
            }
            set
            {
                this.isPreAuthorizedField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool isPreAuthorizedSpecified
        {
            get
            {
                return this.isPreAuthorizedFieldSpecified;
            }
            set
            {
                this.isPreAuthorizedFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("CartDetailsType", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=61)]
        public ShopingCartDetailsType[] CartDetailsType
        {
            get
            {
                return this.cartDetailsTypeField;
            }
            set
            {
                this.cartDetailsTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=62)]
        public string IBAN
        {
            get
            {
                return this.iBANField;
            }
            set
            {
                this.iBANField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=63)]
        public string BIC
        {
            get
            {
                return this.bICField;
            }
            set
            {
                this.bICField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=64)]
        public string AccountHolderName
        {
            get
            {
                return this.accountHolderNameField;
            }
            set
            {
                this.accountHolderNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, DataType="date", Order=65)]
        public System.DateTime SignedDate
        {
            get
            {
                return this.signedDateField;
            }
            set
            {
                this.signedDateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool SignedDateSpecified
        {
            get
            {
                return this.signedDateFieldSpecified;
            }
            set
            {
                this.signedDateFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=66)]
        public System.DateTime txnTimeInAgentOfficeTimeZone
        {
            get
            {
                return this.txnTimeInAgentOfficeTimeZoneField;
            }
            set
            {
                this.txnTimeInAgentOfficeTimeZoneField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool txnTimeInAgentOfficeTimeZoneSpecified
        {
            get
            {
                return this.txnTimeInAgentOfficeTimeZoneFieldSpecified;
            }
            set
            {
                this.txnTimeInAgentOfficeTimeZoneFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=67)]
        public string CustomerProfileId
        {
            get
            {
                return this.customerProfileIdField;
            }
            set
            {
                this.customerProfileIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=68)]
        public string AgencyCode
        {
            get
            {
                return this.agencyCodeField;
            }
            set
            {
                this.agencyCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=69)]
        [System.Xml.Serialization.XmlArrayItemAttribute("CustomField", typeof(CustomFieldType), Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=false)]
        public CustomFieldType[] CustomFieldList
        {
            get
            {
                return this.customFieldListField;
            }
            set
            {
                this.customFieldListField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=70)]
        public string PaymentCode
        {
            get
            {
                return this.paymentCodeField;
            }
            set
            {
                this.paymentCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=71)]
        public string PaymentSystemCode
        {
            get
            {
                return this.paymentSystemCodeField;
            }
            set
            {
                this.paymentSystemCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=72)]
        public string AuthorizationSequenceNumber
        {
            get
            {
                return this.authorizationSequenceNumberField;
            }
            set
            {
                this.authorizationSequenceNumberField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public enum Payment_Type
    {
        
        /// <remarks/>
        AG,
        
        /// <remarks/>
        CC,
        
        /// <remarks/>
        CF,
        
        /// <remarks/>
        CS,
        
        /// <remarks/>
        PA,
        
        /// <remarks/>
        CA,
        
        /// <remarks/>
        CK,
        
        /// <remarks/>
        DB,
        
        /// <remarks/>
        WO,
        
        /// <remarks/>
        GV,
        
        /// <remarks/>
        GC,
        
        /// <remarks/>
        GS,
        
        /// <remarks/>
        LP,
        
        /// <remarks/>
        LT,
        
        /// <remarks/>
        TK,
        
        /// <remarks/>
        DD,
        
        /// <remarks/>
        CO,
        
        /// <remarks/>
        CR,
        
        /// <remarks/>
        PT,
        
        /// <remarks/>
        ET,
        
        /// <remarks/>
        IN,
        
        /// <remarks/>
        PP,
        
        /// <remarks/>
        ED,
        
        /// <remarks/>
        WA,
        
        /// <remarks/>
        CI,
        
        /// <remarks/>
        EP,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public enum WriteOffCodeDetails_Type
    {
        
        /// <remarks/>
        ABSCC,
        
        /// <remarks/>
        ABSGUEST,
        
        /// <remarks/>
        ABSTA,
        
        /// <remarks/>
        MIG,
        
        /// <remarks/>
        ABBPAY,
        
        /// <remarks/>
        VNPAYCAC,
        
        /// <remarks/>
        NAPASCAC,
        
        /// <remarks/>
        VIETTELCAC,
        
        /// <remarks/>
        VNPOSTCAC,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public enum CardDetails_Type
    {
        
        /// <remarks/>
        VISA,
        
        /// <remarks/>
        MASTERCARD,
        
        /// <remarks/>
        AMEX,
        
        /// <remarks/>
        UATP,
        
        /// <remarks/>
        DINERS,
        
        /// <remarks/>
        JCB,
        
        /// <remarks/>
        KOOKMIN,
        
        /// <remarks/>
        HYUNDAI,
        
        /// <remarks/>
        SHINHAN,
        
        /// <remarks/>
        EXBANK,
        
        /// <remarks/>
        LOTTE,
        
        /// <remarks/>
        SAMSUNG,
        
        /// <remarks/>
        BC,
        
        /// <remarks/>
        CREDITCARD,
        
        /// <remarks/>
        HANASK,
        
        /// <remarks/>
        NONGHYUP,
        
        /// <remarks/>
        DUMMY,
        
        /// <remarks/>
        PAYPAL,
        
        /// <remarks/>
        KAKAOPAY,
        
        /// <remarks/>
        MOBILETMONEY,
        
        /// <remarks/>
        ALIPAY,
        
        /// <remarks/>
        TENPAY,
        
        /// <remarks/>
        UNIONPAY,
        
        /// <remarks/>
        PAYSBUY,
        
        /// <remarks/>
        AXESSCARD,
        
        /// <remarks/>
        BONUSCARD,
        
        /// <remarks/>
        WORLDCARD,
        
        /// <remarks/>
        MAXIMUMCARD,
        
        /// <remarks/>
        BKM,
        
        /// <remarks/>
        TROY,
        
        /// <remarks/>
        CVSPAYMENT,
        
        /// <remarks/>
        EC,
        
        /// <remarks/>
        MOBILEPAY,
        
        /// <remarks/>
        PAYOO,
        
        /// <remarks/>
        ABBCREDIT,
        
        /// <remarks/>
        WPCREDIT,
        
        /// <remarks/>
        WECHATPAY,
        
        /// <remarks/>
        NAVERPAY,
        
        /// <remarks/>
        BA,
        
        /// <remarks/>
        VBA,
        
        /// <remarks/>
        QUNAR,
        
        /// <remarks/>
        DCKRW,
        
        /// <remarks/>
        DCOTH,
        
        /// <remarks/>
        OCKRW,
        
        /// <remarks/>
        OCOTH,
        
        /// <remarks/>
        NTTDATACREDIT,
        
        /// <remarks/>
        VISAAPPLEPAY,
        
        /// <remarks/>
        MASTERAPPLEPAY,
        
        /// <remarks/>
        AMEXAPPLEPAY,
        
        /// <remarks/>
        ICCARD,
        
        /// <remarks/>
        TOSS,
        
        /// <remarks/>
        TWAYPAY,
        
        /// <remarks/>
        MISCELLANEOUS,
        
        /// <remarks/>
        DEBITCARD,
        
        /// <remarks/>
        NKAKAOPAY,
        
        /// <remarks/>
        PAYCO,
        
        /// <remarks/>
        LINEPAY,
        
        /// <remarks/>
        KBKOOKMIN,
        
        /// <remarks/>
        KEBHANA,
        
        /// <remarks/>
        VISAINTL,
        
        /// <remarks/>
        JCBINTL,
        
        /// <remarks/>
        DINERSCLUB,
        
        /// <remarks/>
        LOTTEINTL,
        
        /// <remarks/>
        MASTERINTL,
        
        /// <remarks/>
        CITI,
        
        /// <remarks/>
        WOORI,
        
        /// <remarks/>
        KWANGJU,
        
        /// <remarks/>
        SUHYUP,
        
        /// <remarks/>
        JEONBUK,
        
        /// <remarks/>
        JEJU,
        
        /// <remarks/>
        CHOHUNG,
        
        /// <remarks/>
        KDB,
        
        /// <remarks/>
        DBPAYMENT,
        
        /// <remarks/>
        CARDPAY,
        
        /// <remarks/>
        PAYSAFE,
        
        /// <remarks/>
        SMILEPAY,
        
        /// <remarks/>
        PAYPAY,
        
        /// <remarks/>
        SSGPAY,
        
        /// <remarks/>
        EASYWELLPOINT,
        
        /// <remarks/>
        EASYPAY,
        
        /// <remarks/>
        UNION,
        
        /// <remarks/>
        BCCARD,
        
        /// <remarks/>
        PROMOTION,
        
        /// <remarks/>
        MOBILEXPRESS,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public enum CashReceiptDetailsType
    {
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("00")]
        Item00,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("01")]
        Item01,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class GiftVoucherDenominationDetails
    {
        
        private double giftVoucherValueField;
        
        private int giftVoucherQuantityField;
        
        private GiftVoucherTax[] giftVoucherTaxDetailsField;
        
        private double totalTaxAmountField;
        
        private bool totalTaxAmountFieldSpecified;
        
        private string gcSeqIdField;
        
        private string imageUrlField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public double giftVoucherValue
        {
            get
            {
                return this.giftVoucherValueField;
            }
            set
            {
                this.giftVoucherValueField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public int giftVoucherQuantity
        {
            get
            {
                return this.giftVoucherQuantityField;
            }
            set
            {
                this.giftVoucherQuantityField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("GiftVoucherTaxDetails", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public GiftVoucherTax[] GiftVoucherTaxDetails
        {
            get
            {
                return this.giftVoucherTaxDetailsField;
            }
            set
            {
                this.giftVoucherTaxDetailsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public double TotalTaxAmount
        {
            get
            {
                return this.totalTaxAmountField;
            }
            set
            {
                this.totalTaxAmountField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool TotalTaxAmountSpecified
        {
            get
            {
                return this.totalTaxAmountFieldSpecified;
            }
            set
            {
                this.totalTaxAmountFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string gcSeqId
        {
            get
            {
                return this.gcSeqIdField;
            }
            set
            {
                this.gcSeqIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string imageUrl
        {
            get
            {
                return this.imageUrlField;
            }
            set
            {
                this.imageUrlField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public enum VoucherDeliveryMode
    {
        
        /// <remarks/>
        NONE,
        
        /// <remarks/>
        EMAIL,
        
        /// <remarks/>
        FAX,
        
        /// <remarks/>
        MAIL,
        
        /// <remarks/>
        PICKUP,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public enum sendTo
    {
        
        /// <remarks/>
        PURCHASER,
        
        /// <remarks/>
        RECIPIENT,
        
        /// <remarks/>
        BOTH,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class CreateGiftVoucherRS
    {
        
        private string giftVoucherTypeCodeField;
        
        private string bulkOrderNumberField;
        
        private CreatedGiftVoucherDetails[] giftVoucherDetailsField;
        
        private PaymentDetailsType[] paymentDetailsField;
        
        private GVPurchaserInformation purchaserInformationField;
        
        private GVRecipientInformation recipientInformationField;
        
        private string personalMessageField;
        
        private string companyNameField;
        
        private string taxOfficeField;
        
        private string languageField;
        
        private ErrorType errorTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string giftVoucherTypeCode
        {
            get
            {
                return this.giftVoucherTypeCodeField;
            }
            set
            {
                this.giftVoucherTypeCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string bulkOrderNumber
        {
            get
            {
                return this.bulkOrderNumberField;
            }
            set
            {
                this.bulkOrderNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("giftVoucherDetails", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public CreatedGiftVoucherDetails[] giftVoucherDetails
        {
            get
            {
                return this.giftVoucherDetailsField;
            }
            set
            {
                this.giftVoucherDetailsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("PaymentDetails", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public PaymentDetailsType[] PaymentDetails
        {
            get
            {
                return this.paymentDetailsField;
            }
            set
            {
                this.paymentDetailsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public GVPurchaserInformation purchaserInformation
        {
            get
            {
                return this.purchaserInformationField;
            }
            set
            {
                this.purchaserInformationField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public GVRecipientInformation recipientInformation
        {
            get
            {
                return this.recipientInformationField;
            }
            set
            {
                this.recipientInformationField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string personalMessage
        {
            get
            {
                return this.personalMessageField;
            }
            set
            {
                this.personalMessageField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string companyName
        {
            get
            {
                return this.companyNameField;
            }
            set
            {
                this.companyNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public string taxOffice
        {
            get
            {
                return this.taxOfficeField;
            }
            set
            {
                this.taxOfficeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public string language
        {
            get
            {
                return this.languageField;
            }
            set
            {
                this.languageField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=10)]
        public ErrorType errorType
        {
            get
            {
                return this.errorTypeField;
            }
            set
            {
                this.errorTypeField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class createGiftVoucherRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public GiftVoucherPortServiceReference.CreateGiftVoucherRQ CreateGiftVoucherRQ;
        
        public createGiftVoucherRequest()
        {
        }
        
        public createGiftVoucherRequest(GiftVoucherPortServiceReference.CreateGiftVoucherRQ CreateGiftVoucherRQ)
        {
            this.CreateGiftVoucherRQ = CreateGiftVoucherRQ;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class createGiftVoucherResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public GiftVoucherPortServiceReference.CreateGiftVoucherRS CreateGiftVoucherRS;
        
        public createGiftVoucherResponse()
        {
        }
        
        public createGiftVoucherResponse(GiftVoucherPortServiceReference.CreateGiftVoucherRS CreateGiftVoucherRS)
        {
            this.CreateGiftVoucherRS = CreateGiftVoucherRS;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class IssueGiftVoucherRQ
    {
        
        private string airlineCodeField;
        
        private BookingChannelKeyType bookingChannelField;
        
        private string giftVoucherNumberField;
        
        private string customerLastNameField;
        
        private string customerFirstNameField;
        
        private string customerMembershipNumberField;
        
        private string recipientPasswordField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AirlineCode
        {
            get
            {
                return this.airlineCodeField;
            }
            set
            {
                this.airlineCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public BookingChannelKeyType BookingChannel
        {
            get
            {
                return this.bookingChannelField;
            }
            set
            {
                this.bookingChannelField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string GiftVoucherNumber
        {
            get
            {
                return this.giftVoucherNumberField;
            }
            set
            {
                this.giftVoucherNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string CustomerLastName
        {
            get
            {
                return this.customerLastNameField;
            }
            set
            {
                this.customerLastNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string CustomerFirstName
        {
            get
            {
                return this.customerFirstNameField;
            }
            set
            {
                this.customerFirstNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string customerMembershipNumber
        {
            get
            {
                return this.customerMembershipNumberField;
            }
            set
            {
                this.customerMembershipNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string recipientPassword
        {
            get
            {
                return this.recipientPasswordField;
            }
            set
            {
                this.recipientPasswordField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class IssueGiftVoucherRS
    {
        
        private DateOnlyType issueDateField;
        
        private string giftVoucherNumberField;
        
        private DateOnlyType giftVoucherExpiryDateField;
        
        private ErrorType errorTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public DateOnlyType IssueDate
        {
            get
            {
                return this.issueDateField;
            }
            set
            {
                this.issueDateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string GiftVoucherNumber
        {
            get
            {
                return this.giftVoucherNumberField;
            }
            set
            {
                this.giftVoucherNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public DateOnlyType GiftVoucherExpiryDate
        {
            get
            {
                return this.giftVoucherExpiryDateField;
            }
            set
            {
                this.giftVoucherExpiryDateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public ErrorType ErrorType
        {
            get
            {
                return this.errorTypeField;
            }
            set
            {
                this.errorTypeField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class issueGiftVoucherRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public GiftVoucherPortServiceReference.IssueGiftVoucherRQ IssueGiftVoucherRQ;
        
        public issueGiftVoucherRequest()
        {
        }
        
        public issueGiftVoucherRequest(GiftVoucherPortServiceReference.IssueGiftVoucherRQ IssueGiftVoucherRQ)
        {
            this.IssueGiftVoucherRQ = IssueGiftVoucherRQ;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class issueGiftVoucherResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public GiftVoucherPortServiceReference.IssueGiftVoucherRS IssueGiftVoucherRS;
        
        public issueGiftVoucherResponse()
        {
        }
        
        public issueGiftVoucherResponse(GiftVoucherPortServiceReference.IssueGiftVoucherRS IssueGiftVoucherRS)
        {
            this.IssueGiftVoucherRS = IssueGiftVoucherRS;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class ListGiftVouchersRQ
    {
        
        private string airlineCodeField;
        
        private BookingChannelKeyType bookingChannelField;
        
        private string loyaltyNumberField;
        
        private string agencyCodeField;
        
        private string vppIdField;
        
        private string giftVoucherNumberField;
        
        private string bulkOrderNumberField;
        
        private string giftVoucherTypeCodeField;
        
        private OwnerOfCreationTypes ownerOfCreationField;
        
        private string giftVoucherCurrencyCodeField;
        
        private double giftVoucherAmountField;
        
        private bool giftVoucherAmountFieldSpecified;
        
        private GiftVoucherStatusTypes giftVoucherStatusField;
        
        private System.DateTime fromExpiryDateField;
        
        private bool fromExpiryDateFieldSpecified;
        
        private System.DateTime toExpiryDateField;
        
        private bool toExpiryDateFieldSpecified;
        
        private System.DateTime fromCreationDateField;
        
        private bool fromCreationDateFieldSpecified;
        
        private System.DateTime toCreationDateField;
        
        private bool toCreationDateFieldSpecified;
        
        private int listIndexField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AirlineCode
        {
            get
            {
                return this.airlineCodeField;
            }
            set
            {
                this.airlineCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public BookingChannelKeyType BookingChannel
        {
            get
            {
                return this.bookingChannelField;
            }
            set
            {
                this.bookingChannelField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string loyaltyNumber
        {
            get
            {
                return this.loyaltyNumberField;
            }
            set
            {
                this.loyaltyNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string agencyCode
        {
            get
            {
                return this.agencyCodeField;
            }
            set
            {
                this.agencyCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string vppId
        {
            get
            {
                return this.vppIdField;
            }
            set
            {
                this.vppIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string GiftVoucherNumber
        {
            get
            {
                return this.giftVoucherNumberField;
            }
            set
            {
                this.giftVoucherNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string BulkOrderNumber
        {
            get
            {
                return this.bulkOrderNumberField;
            }
            set
            {
                this.bulkOrderNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string GiftVoucherTypeCode
        {
            get
            {
                return this.giftVoucherTypeCodeField;
            }
            set
            {
                this.giftVoucherTypeCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public OwnerOfCreationTypes OwnerOfCreation
        {
            get
            {
                return this.ownerOfCreationField;
            }
            set
            {
                this.ownerOfCreationField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public string GiftVoucherCurrencyCode
        {
            get
            {
                return this.giftVoucherCurrencyCodeField;
            }
            set
            {
                this.giftVoucherCurrencyCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=10)]
        public double GiftVoucherAmount
        {
            get
            {
                return this.giftVoucherAmountField;
            }
            set
            {
                this.giftVoucherAmountField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool GiftVoucherAmountSpecified
        {
            get
            {
                return this.giftVoucherAmountFieldSpecified;
            }
            set
            {
                this.giftVoucherAmountFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=11)]
        public GiftVoucherStatusTypes GiftVoucherStatus
        {
            get
            {
                return this.giftVoucherStatusField;
            }
            set
            {
                this.giftVoucherStatusField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, DataType="date", Order=12)]
        public System.DateTime FromExpiryDate
        {
            get
            {
                return this.fromExpiryDateField;
            }
            set
            {
                this.fromExpiryDateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool FromExpiryDateSpecified
        {
            get
            {
                return this.fromExpiryDateFieldSpecified;
            }
            set
            {
                this.fromExpiryDateFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, DataType="date", Order=13)]
        public System.DateTime ToExpiryDate
        {
            get
            {
                return this.toExpiryDateField;
            }
            set
            {
                this.toExpiryDateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool ToExpiryDateSpecified
        {
            get
            {
                return this.toExpiryDateFieldSpecified;
            }
            set
            {
                this.toExpiryDateFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, DataType="date", Order=14)]
        public System.DateTime FromCreationDate
        {
            get
            {
                return this.fromCreationDateField;
            }
            set
            {
                this.fromCreationDateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool FromCreationDateSpecified
        {
            get
            {
                return this.fromCreationDateFieldSpecified;
            }
            set
            {
                this.fromCreationDateFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, DataType="date", Order=15)]
        public System.DateTime ToCreationDate
        {
            get
            {
                return this.toCreationDateField;
            }
            set
            {
                this.toCreationDateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool ToCreationDateSpecified
        {
            get
            {
                return this.toCreationDateFieldSpecified;
            }
            set
            {
                this.toCreationDateFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=16)]
        public int listIndex
        {
            get
            {
                return this.listIndexField;
            }
            set
            {
                this.listIndexField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public enum OwnerOfCreationTypes
    {
        
        /// <remarks/>
        AIRLINE,
        
        /// <remarks/>
        VPP,
        
        /// <remarks/>
        TRAVEL_AGENT,
        
        /// <remarks/>
        PASSENGER,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public enum GiftVoucherStatusTypes
    {
        
        /// <remarks/>
        CREATED,
        
        /// <remarks/>
        EXPIRED,
        
        /// <remarks/>
        ISSUED,
        
        /// <remarks/>
        REFUNDED,
        
        /// <remarks/>
        VOID,
        
        /// <remarks/>
        USED,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class ListGiftVouchersRS
    {
        
        private ListGiftVoucherDetails[] listGiftVouchersField;
        
        private ErrorType errorTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("ListGiftVouchers", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public ListGiftVoucherDetails[] ListGiftVouchers
        {
            get
            {
                return this.listGiftVouchersField;
            }
            set
            {
                this.listGiftVouchersField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public ErrorType ErrorType
        {
            get
            {
                return this.errorTypeField;
            }
            set
            {
                this.errorTypeField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class listGiftVouchersRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public GiftVoucherPortServiceReference.ListGiftVouchersRQ ListGiftVouchersRQ;
        
        public listGiftVouchersRequest()
        {
        }
        
        public listGiftVouchersRequest(GiftVoucherPortServiceReference.ListGiftVouchersRQ ListGiftVouchersRQ)
        {
            this.ListGiftVouchersRQ = ListGiftVouchersRQ;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class listGiftVouchersResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public GiftVoucherPortServiceReference.ListGiftVouchersRS ListGiftVouchersRS;
        
        public listGiftVouchersResponse()
        {
        }
        
        public listGiftVouchersResponse(GiftVoucherPortServiceReference.ListGiftVouchersRS ListGiftVouchersRS)
        {
            this.ListGiftVouchersRS = ListGiftVouchersRS;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class LoginVPPRQ
    {
        
        private string vPPIDField;
        
        private string loginPasswordField;
        
        private string airlineCodeField;
        
        private BookingChannelKeyType bookingChannelField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string VPPID
        {
            get
            {
                return this.vPPIDField;
            }
            set
            {
                this.vPPIDField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string LoginPassword
        {
            get
            {
                return this.loginPasswordField;
            }
            set
            {
                this.loginPasswordField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string AirlineCode
        {
            get
            {
                return this.airlineCodeField;
            }
            set
            {
                this.airlineCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public BookingChannelKeyType BookingChannel
        {
            get
            {
                return this.bookingChannelField;
            }
            set
            {
                this.bookingChannelField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class LoginVPPRS
    {
        
        private string airlineCodeField;
        
        private string loginStatusField;
        
        private ErrorType errorTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AirlineCode
        {
            get
            {
                return this.airlineCodeField;
            }
            set
            {
                this.airlineCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string LoginStatus
        {
            get
            {
                return this.loginStatusField;
            }
            set
            {
                this.loginStatusField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public ErrorType ErrorType
        {
            get
            {
                return this.errorTypeField;
            }
            set
            {
                this.errorTypeField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class loginVPPRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public GiftVoucherPortServiceReference.LoginVPPRQ LoginVPPRQ;
        
        public loginVPPRequest()
        {
        }
        
        public loginVPPRequest(GiftVoucherPortServiceReference.LoginVPPRQ LoginVPPRQ)
        {
            this.LoginVPPRQ = LoginVPPRQ;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class loginVPPResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public GiftVoucherPortServiceReference.LoginVPPRS LoginVPPRS;
        
        public loginVPPResponse()
        {
        }
        
        public loginVPPResponse(GiftVoucherPortServiceReference.LoginVPPRS LoginVPPRS)
        {
            this.LoginVPPRS = LoginVPPRS;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class RetrieveGCWithProfileAliasRQ
    {
        
        private string airlineCodeField;
        
        private BookingChannelKeyType bookingChannelField;
        
        private string profileIDField;
        
        private string custIDField;
        
        private string gCNumberField;
        
        private string currencyField;
        
        private GiftVoucherStatusTypes giftVoucherStatusField;
        
        private bool giftVoucherStatusFieldSpecified;
        
        private string convertToCurrencyField;
        
        private bool isBalanceCheckRequiredField;
        
        private bool isBalanceCheckRequiredFieldSpecified;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AirlineCode
        {
            get
            {
                return this.airlineCodeField;
            }
            set
            {
                this.airlineCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public BookingChannelKeyType BookingChannel
        {
            get
            {
                return this.bookingChannelField;
            }
            set
            {
                this.bookingChannelField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string profileID
        {
            get
            {
                return this.profileIDField;
            }
            set
            {
                this.profileIDField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string custID
        {
            get
            {
                return this.custIDField;
            }
            set
            {
                this.custIDField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string GCNumber
        {
            get
            {
                return this.gCNumberField;
            }
            set
            {
                this.gCNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string Currency
        {
            get
            {
                return this.currencyField;
            }
            set
            {
                this.currencyField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public GiftVoucherStatusTypes GiftVoucherStatus
        {
            get
            {
                return this.giftVoucherStatusField;
            }
            set
            {
                this.giftVoucherStatusField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool GiftVoucherStatusSpecified
        {
            get
            {
                return this.giftVoucherStatusFieldSpecified;
            }
            set
            {
                this.giftVoucherStatusFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string ConvertToCurrency
        {
            get
            {
                return this.convertToCurrencyField;
            }
            set
            {
                this.convertToCurrencyField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public bool IsBalanceCheckRequired
        {
            get
            {
                return this.isBalanceCheckRequiredField;
            }
            set
            {
                this.isBalanceCheckRequiredField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool IsBalanceCheckRequiredSpecified
        {
            get
            {
                return this.isBalanceCheckRequiredFieldSpecified;
            }
            set
            {
                this.isBalanceCheckRequiredFieldSpecified = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class RetrieveGCWithProfileAliasRS
    {
        
        private RetrieveGCWithProfileAlias[] retrieveGCWithProfileAliasField;
        
        private ErrorType errorTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("RetrieveGCWithProfileAlias", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public RetrieveGCWithProfileAlias[] RetrieveGCWithProfileAlias
        {
            get
            {
                return this.retrieveGCWithProfileAliasField;
            }
            set
            {
                this.retrieveGCWithProfileAliasField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public ErrorType ErrorType
        {
            get
            {
                return this.errorTypeField;
            }
            set
            {
                this.errorTypeField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class retrieveGCWithProfileAliasRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public GiftVoucherPortServiceReference.RetrieveGCWithProfileAliasRQ RetrieveGCWithProfileAliasRQ;
        
        public retrieveGCWithProfileAliasRequest()
        {
        }
        
        public retrieveGCWithProfileAliasRequest(GiftVoucherPortServiceReference.RetrieveGCWithProfileAliasRQ RetrieveGCWithProfileAliasRQ)
        {
            this.RetrieveGCWithProfileAliasRQ = RetrieveGCWithProfileAliasRQ;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class retrieveGCWithProfileAliasResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public GiftVoucherPortServiceReference.RetrieveGCWithProfileAliasRS RetrieveGCWithProfileAliasRS;
        
        public retrieveGCWithProfileAliasResponse()
        {
        }
        
        public retrieveGCWithProfileAliasResponse(GiftVoucherPortServiceReference.RetrieveGCWithProfileAliasRS RetrieveGCWithProfileAliasRS)
        {
            this.RetrieveGCWithProfileAliasRS = RetrieveGCWithProfileAliasRS;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class RetrieveGiftCertificateDetailsRQ
    {
        
        private string airlineCodeField;
        
        private BookingChannelKeyType bookingChannelField;
        
        private string giftCertificateNumberField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AirlineCode
        {
            get
            {
                return this.airlineCodeField;
            }
            set
            {
                this.airlineCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public BookingChannelKeyType BookingChannel
        {
            get
            {
                return this.bookingChannelField;
            }
            set
            {
                this.bookingChannelField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string GiftCertificateNumber
        {
            get
            {
                return this.giftCertificateNumberField;
            }
            set
            {
                this.giftCertificateNumberField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class RetrieveGiftCertificateDetailsRS
    {
        
        private string giftCertificateTypeField;
        
        private string giftCertificateTypeCodeField;
        
        private string giftCertificateNumberField;
        
        private string bulkOrderNumberField;
        
        private string giftCertificateCurrencyCodeField;
        
        private double giftCertificateBalanceAmountField;
        
        private bool giftCertificateBalanceAmountFieldSpecified;
        
        private string giftCertificateStatusField;
        
        private System.DateTime giftCertificateExpiryDateField;
        
        private bool giftCertificateExpiryDateFieldSpecified;
        
        private string ownerOfCreationField;
        
        private PurchaserDetails purchaserDetailsField;
        
        private RecipientDetails recipientDetailsField;
        
        private ErrorType errorTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string GiftCertificateType
        {
            get
            {
                return this.giftCertificateTypeField;
            }
            set
            {
                this.giftCertificateTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string GiftCertificateTypeCode
        {
            get
            {
                return this.giftCertificateTypeCodeField;
            }
            set
            {
                this.giftCertificateTypeCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string GiftCertificateNumber
        {
            get
            {
                return this.giftCertificateNumberField;
            }
            set
            {
                this.giftCertificateNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string BulkOrderNumber
        {
            get
            {
                return this.bulkOrderNumberField;
            }
            set
            {
                this.bulkOrderNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string GiftCertificateCurrencyCode
        {
            get
            {
                return this.giftCertificateCurrencyCodeField;
            }
            set
            {
                this.giftCertificateCurrencyCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public double GiftCertificateBalanceAmount
        {
            get
            {
                return this.giftCertificateBalanceAmountField;
            }
            set
            {
                this.giftCertificateBalanceAmountField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool GiftCertificateBalanceAmountSpecified
        {
            get
            {
                return this.giftCertificateBalanceAmountFieldSpecified;
            }
            set
            {
                this.giftCertificateBalanceAmountFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string GiftCertificateStatus
        {
            get
            {
                return this.giftCertificateStatusField;
            }
            set
            {
                this.giftCertificateStatusField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, DataType="date", Order=7)]
        public System.DateTime GiftCertificateExpiryDate
        {
            get
            {
                return this.giftCertificateExpiryDateField;
            }
            set
            {
                this.giftCertificateExpiryDateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool GiftCertificateExpiryDateSpecified
        {
            get
            {
                return this.giftCertificateExpiryDateFieldSpecified;
            }
            set
            {
                this.giftCertificateExpiryDateFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public string OwnerOfCreation
        {
            get
            {
                return this.ownerOfCreationField;
            }
            set
            {
                this.ownerOfCreationField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public PurchaserDetails PurchaserDetails
        {
            get
            {
                return this.purchaserDetailsField;
            }
            set
            {
                this.purchaserDetailsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=10)]
        public RecipientDetails RecipientDetails
        {
            get
            {
                return this.recipientDetailsField;
            }
            set
            {
                this.recipientDetailsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=11)]
        public ErrorType ErrorType
        {
            get
            {
                return this.errorTypeField;
            }
            set
            {
                this.errorTypeField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class retrieveGiftCertificateDetailsRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public GiftVoucherPortServiceReference.RetrieveGiftCertificateDetailsRQ RetrieveGiftCertificateDetailsRQ;
        
        public retrieveGiftCertificateDetailsRequest()
        {
        }
        
        public retrieveGiftCertificateDetailsRequest(GiftVoucherPortServiceReference.RetrieveGiftCertificateDetailsRQ RetrieveGiftCertificateDetailsRQ)
        {
            this.RetrieveGiftCertificateDetailsRQ = RetrieveGiftCertificateDetailsRQ;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class retrieveGiftCertificateDetailsResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public GiftVoucherPortServiceReference.RetrieveGiftCertificateDetailsRS RetrieveGiftCertificateDetailsRS;
        
        public retrieveGiftCertificateDetailsResponse()
        {
        }
        
        public retrieveGiftCertificateDetailsResponse(GiftVoucherPortServiceReference.RetrieveGiftCertificateDetailsRS RetrieveGiftCertificateDetailsRS)
        {
            this.RetrieveGiftCertificateDetailsRS = RetrieveGiftCertificateDetailsRS;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class ViewVPPDetailsRQ
    {
        
        private string airlineCodeField;
        
        private string vPPIDField;
        
        private BookingChannelKeyType bookingChannelField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AirlineCode
        {
            get
            {
                return this.airlineCodeField;
            }
            set
            {
                this.airlineCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string VPPID
        {
            get
            {
                return this.vPPIDField;
            }
            set
            {
                this.vPPIDField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public BookingChannelKeyType BookingChannel
        {
            get
            {
                return this.bookingChannelField;
            }
            set
            {
                this.bookingChannelField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class ViewVPPDetailsRS
    {
        
        private VPPDetails vPPDetailsField;
        
        private ErrorType errorTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public VPPDetails VPPDetails
        {
            get
            {
                return this.vPPDetailsField;
            }
            set
            {
                this.vPPDetailsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public ErrorType ErrorType
        {
            get
            {
                return this.errorTypeField;
            }
            set
            {
                this.errorTypeField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class retrieveVPPDetailsRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public GiftVoucherPortServiceReference.ViewVPPDetailsRQ ViewVPPDetailsRQ;
        
        public retrieveVPPDetailsRequest()
        {
        }
        
        public retrieveVPPDetailsRequest(GiftVoucherPortServiceReference.ViewVPPDetailsRQ ViewVPPDetailsRQ)
        {
            this.ViewVPPDetailsRQ = ViewVPPDetailsRQ;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class retrieveVPPDetailsResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public GiftVoucherPortServiceReference.ViewVPPDetailsRS ViewVPPDetailsRS;
        
        public retrieveVPPDetailsResponse()
        {
        }
        
        public retrieveVPPDetailsResponse(GiftVoucherPortServiceReference.ViewVPPDetailsRS ViewVPPDetailsRS)
        {
            this.ViewVPPDetailsRS = ViewVPPDetailsRS;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class VoidVoucherRQ
    {
        
        private string airlineCodeField;
        
        private BookingChannelKeyType bookingChannelField;
        
        private string giftVoucherNumberField;
        
        private string voidReasonCodeField;
        
        private string voidReasonCommentField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AirlineCode
        {
            get
            {
                return this.airlineCodeField;
            }
            set
            {
                this.airlineCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public BookingChannelKeyType BookingChannel
        {
            get
            {
                return this.bookingChannelField;
            }
            set
            {
                this.bookingChannelField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string giftVoucherNumber
        {
            get
            {
                return this.giftVoucherNumberField;
            }
            set
            {
                this.giftVoucherNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string voidReasonCode
        {
            get
            {
                return this.voidReasonCodeField;
            }
            set
            {
                this.voidReasonCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string voidReasonComment
        {
            get
            {
                return this.voidReasonCommentField;
            }
            set
            {
                this.voidReasonCommentField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class VoidVoucherRS
    {
        
        private string airlineCodeField;
        
        private string statusField;
        
        private ErrorType errorTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AirlineCode
        {
            get
            {
                return this.airlineCodeField;
            }
            set
            {
                this.airlineCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string Status
        {
            get
            {
                return this.statusField;
            }
            set
            {
                this.statusField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public ErrorType errorType
        {
            get
            {
                return this.errorTypeField;
            }
            set
            {
                this.errorTypeField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class voidVoucherRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public GiftVoucherPortServiceReference.VoidVoucherRQ VoidVoucherRQ;
        
        public voidVoucherRequest()
        {
        }
        
        public voidVoucherRequest(GiftVoucherPortServiceReference.VoidVoucherRQ VoidVoucherRQ)
        {
            this.VoidVoucherRQ = VoidVoucherRQ;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class voidVoucherResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public GiftVoucherPortServiceReference.VoidVoucherRS VoidVoucherRS;
        
        public voidVoucherResponse()
        {
        }
        
        public voidVoucherResponse(GiftVoucherPortServiceReference.VoidVoucherRS VoidVoucherRS)
        {
            this.VoidVoucherRS = VoidVoucherRS;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class GetTaxForGiftVoucherRQ
    {
        
        private string airlineCodeField;
        
        private BookingChannelKeyType bookingChannelField;
        
        private string giftVoucherCurrencyCodeField;
        
        private string gCTaxCountryField;
        
        private GiftVoucherDenominationDetails[] denominationDetailsField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AirlineCode
        {
            get
            {
                return this.airlineCodeField;
            }
            set
            {
                this.airlineCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public BookingChannelKeyType BookingChannel
        {
            get
            {
                return this.bookingChannelField;
            }
            set
            {
                this.bookingChannelField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string GiftVoucherCurrencyCode
        {
            get
            {
                return this.giftVoucherCurrencyCodeField;
            }
            set
            {
                this.giftVoucherCurrencyCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string GCTaxCountry
        {
            get
            {
                return this.gCTaxCountryField;
            }
            set
            {
                this.gCTaxCountryField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("DenominationDetails", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public GiftVoucherDenominationDetails[] DenominationDetails
        {
            get
            {
                return this.denominationDetailsField;
            }
            set
            {
                this.denominationDetailsField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class GetTaxForGiftVoucherRS
    {
        
        private string giftVoucherCurrencyCodeField;
        
        private GiftVoucherDenominationDetails[] denominationDetailsField;
        
        private double totalTaxAmountField;
        
        private bool totalTaxAmountFieldSpecified;
        
        private ErrorType errorTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string GiftVoucherCurrencyCode
        {
            get
            {
                return this.giftVoucherCurrencyCodeField;
            }
            set
            {
                this.giftVoucherCurrencyCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("DenominationDetails", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public GiftVoucherDenominationDetails[] DenominationDetails
        {
            get
            {
                return this.denominationDetailsField;
            }
            set
            {
                this.denominationDetailsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public double TotalTaxAmount
        {
            get
            {
                return this.totalTaxAmountField;
            }
            set
            {
                this.totalTaxAmountField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool TotalTaxAmountSpecified
        {
            get
            {
                return this.totalTaxAmountFieldSpecified;
            }
            set
            {
                this.totalTaxAmountFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public ErrorType ErrorType
        {
            get
            {
                return this.errorTypeField;
            }
            set
            {
                this.errorTypeField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class getTaxForGiftVoucherRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public GiftVoucherPortServiceReference.GetTaxForGiftVoucherRQ GetTaxForGiftVoucherRQ;
        
        public getTaxForGiftVoucherRequest()
        {
        }
        
        public getTaxForGiftVoucherRequest(GiftVoucherPortServiceReference.GetTaxForGiftVoucherRQ GetTaxForGiftVoucherRQ)
        {
            this.GetTaxForGiftVoucherRQ = GetTaxForGiftVoucherRQ;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class getTaxForGiftVoucherResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public GiftVoucherPortServiceReference.GetTaxForGiftVoucherRS GetTaxForGiftVoucherRS;
        
        public getTaxForGiftVoucherResponse()
        {
        }
        
        public getTaxForGiftVoucherResponse(GiftVoucherPortServiceReference.GetTaxForGiftVoucherRS GetTaxForGiftVoucherRS)
        {
            this.GetTaxForGiftVoucherRS = GetTaxForGiftVoucherRS;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class SendGCEmailRQ
    {
        
        private string airlineCodeField;
        
        private BookingChannelKeyType bookingChannelField;
        
        private string[] giftVoucherNumberField;
        
        private string titleField;
        
        private string firstNameField;
        
        private string lastNameField;
        
        private string emailField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AirlineCode
        {
            get
            {
                return this.airlineCodeField;
            }
            set
            {
                this.airlineCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public BookingChannelKeyType BookingChannel
        {
            get
            {
                return this.bookingChannelField;
            }
            set
            {
                this.bookingChannelField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("giftVoucherNumber", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string[] giftVoucherNumber
        {
            get
            {
                return this.giftVoucherNumberField;
            }
            set
            {
                this.giftVoucherNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string title
        {
            get
            {
                return this.titleField;
            }
            set
            {
                this.titleField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string firstName
        {
            get
            {
                return this.firstNameField;
            }
            set
            {
                this.firstNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string lastName
        {
            get
            {
                return this.lastNameField;
            }
            set
            {
                this.lastNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string email
        {
            get
            {
                return this.emailField;
            }
            set
            {
                this.emailField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class SendGCEmailRS
    {
        
        private string statusField;
        
        private ErrorType errorTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string Status
        {
            get
            {
                return this.statusField;
            }
            set
            {
                this.statusField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public ErrorType errorType
        {
            get
            {
                return this.errorTypeField;
            }
            set
            {
                this.errorTypeField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class sendGCEmailRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public GiftVoucherPortServiceReference.SendGCEmailRQ SendGCEmailRQ;
        
        public sendGCEmailRequest()
        {
        }
        
        public sendGCEmailRequest(GiftVoucherPortServiceReference.SendGCEmailRQ SendGCEmailRQ)
        {
            this.SendGCEmailRQ = SendGCEmailRQ;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class sendGCEmailResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public GiftVoucherPortServiceReference.SendGCEmailRS SendGCEmailRS;
        
        public sendGCEmailResponse()
        {
        }
        
        public sendGCEmailResponse(GiftVoucherPortServiceReference.SendGCEmailRS SendGCEmailRS)
        {
            this.SendGCEmailRS = SendGCEmailRS;
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    public interface GiftVoucherPortChannel : GiftVoucherPortServiceReference.GiftVoucherPort, System.ServiceModel.IClientChannel
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    public partial class GiftVoucherPortClient : System.ServiceModel.ClientBase<GiftVoucherPortServiceReference.GiftVoucherPort>, GiftVoucherPortServiceReference.GiftVoucherPort
    {
        
        /// <summary>
        /// Implement this partial method to configure the service endpoint.
        /// </summary>
        /// <param name="serviceEndpoint">The endpoint to configure</param>
        /// <param name="clientCredentials">The client credentials</param>
        static partial void ConfigureEndpoint(System.ServiceModel.Description.ServiceEndpoint serviceEndpoint, System.ServiceModel.Description.ClientCredentials clientCredentials);
        
        public GiftVoucherPortClient() : 
                base(GiftVoucherPortClient.GetDefaultBinding(), GiftVoucherPortClient.GetDefaultEndpointAddress())
        {
            this.Endpoint.Name = EndpointConfiguration.GiftVoucherPort.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public GiftVoucherPortClient(EndpointConfiguration endpointConfiguration) : 
                base(GiftVoucherPortClient.GetBindingForEndpoint(endpointConfiguration), GiftVoucherPortClient.GetEndpointAddress(endpointConfiguration))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public GiftVoucherPortClient(EndpointConfiguration endpointConfiguration, string remoteAddress) : 
                base(GiftVoucherPortClient.GetBindingForEndpoint(endpointConfiguration), new System.ServiceModel.EndpointAddress(remoteAddress))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public GiftVoucherPortClient(EndpointConfiguration endpointConfiguration, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(GiftVoucherPortClient.GetBindingForEndpoint(endpointConfiguration), remoteAddress)
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public GiftVoucherPortClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress)
        {
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<GiftVoucherPortServiceReference.createGiftVoucherResponse> GiftVoucherPortServiceReference.GiftVoucherPort.createGiftVoucherAsync(GiftVoucherPortServiceReference.createGiftVoucherRequest request)
        {
            return base.Channel.createGiftVoucherAsync(request);
        }
        
        public System.Threading.Tasks.Task<GiftVoucherPortServiceReference.createGiftVoucherResponse> createGiftVoucherAsync(GiftVoucherPortServiceReference.CreateGiftVoucherRQ CreateGiftVoucherRQ)
        {
            GiftVoucherPortServiceReference.createGiftVoucherRequest inValue = new GiftVoucherPortServiceReference.createGiftVoucherRequest();
            inValue.CreateGiftVoucherRQ = CreateGiftVoucherRQ;
            return ((GiftVoucherPortServiceReference.GiftVoucherPort)(this)).createGiftVoucherAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<GiftVoucherPortServiceReference.issueGiftVoucherResponse> GiftVoucherPortServiceReference.GiftVoucherPort.issueGiftVoucherAsync(GiftVoucherPortServiceReference.issueGiftVoucherRequest request)
        {
            return base.Channel.issueGiftVoucherAsync(request);
        }
        
        public System.Threading.Tasks.Task<GiftVoucherPortServiceReference.issueGiftVoucherResponse> issueGiftVoucherAsync(GiftVoucherPortServiceReference.IssueGiftVoucherRQ IssueGiftVoucherRQ)
        {
            GiftVoucherPortServiceReference.issueGiftVoucherRequest inValue = new GiftVoucherPortServiceReference.issueGiftVoucherRequest();
            inValue.IssueGiftVoucherRQ = IssueGiftVoucherRQ;
            return ((GiftVoucherPortServiceReference.GiftVoucherPort)(this)).issueGiftVoucherAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<GiftVoucherPortServiceReference.listGiftVouchersResponse> GiftVoucherPortServiceReference.GiftVoucherPort.listGiftVouchersAsync(GiftVoucherPortServiceReference.listGiftVouchersRequest request)
        {
            return base.Channel.listGiftVouchersAsync(request);
        }
        
        public System.Threading.Tasks.Task<GiftVoucherPortServiceReference.listGiftVouchersResponse> listGiftVouchersAsync(GiftVoucherPortServiceReference.ListGiftVouchersRQ ListGiftVouchersRQ)
        {
            GiftVoucherPortServiceReference.listGiftVouchersRequest inValue = new GiftVoucherPortServiceReference.listGiftVouchersRequest();
            inValue.ListGiftVouchersRQ = ListGiftVouchersRQ;
            return ((GiftVoucherPortServiceReference.GiftVoucherPort)(this)).listGiftVouchersAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<GiftVoucherPortServiceReference.loginVPPResponse> GiftVoucherPortServiceReference.GiftVoucherPort.loginVPPAsync(GiftVoucherPortServiceReference.loginVPPRequest request)
        {
            return base.Channel.loginVPPAsync(request);
        }
        
        public System.Threading.Tasks.Task<GiftVoucherPortServiceReference.loginVPPResponse> loginVPPAsync(GiftVoucherPortServiceReference.LoginVPPRQ LoginVPPRQ)
        {
            GiftVoucherPortServiceReference.loginVPPRequest inValue = new GiftVoucherPortServiceReference.loginVPPRequest();
            inValue.LoginVPPRQ = LoginVPPRQ;
            return ((GiftVoucherPortServiceReference.GiftVoucherPort)(this)).loginVPPAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<GiftVoucherPortServiceReference.retrieveGCWithProfileAliasResponse> GiftVoucherPortServiceReference.GiftVoucherPort.retrieveGCWithProfileAliasAsync(GiftVoucherPortServiceReference.retrieveGCWithProfileAliasRequest request)
        {
            return base.Channel.retrieveGCWithProfileAliasAsync(request);
        }
        
        public System.Threading.Tasks.Task<GiftVoucherPortServiceReference.retrieveGCWithProfileAliasResponse> retrieveGCWithProfileAliasAsync(GiftVoucherPortServiceReference.RetrieveGCWithProfileAliasRQ RetrieveGCWithProfileAliasRQ)
        {
            GiftVoucherPortServiceReference.retrieveGCWithProfileAliasRequest inValue = new GiftVoucherPortServiceReference.retrieveGCWithProfileAliasRequest();
            inValue.RetrieveGCWithProfileAliasRQ = RetrieveGCWithProfileAliasRQ;
            return ((GiftVoucherPortServiceReference.GiftVoucherPort)(this)).retrieveGCWithProfileAliasAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<GiftVoucherPortServiceReference.retrieveGiftCertificateDetailsResponse> GiftVoucherPortServiceReference.GiftVoucherPort.retrieveGiftCertificateDetailsAsync(GiftVoucherPortServiceReference.retrieveGiftCertificateDetailsRequest request)
        {
            return base.Channel.retrieveGiftCertificateDetailsAsync(request);
        }
        
        public System.Threading.Tasks.Task<GiftVoucherPortServiceReference.retrieveGiftCertificateDetailsResponse> retrieveGiftCertificateDetailsAsync(GiftVoucherPortServiceReference.RetrieveGiftCertificateDetailsRQ RetrieveGiftCertificateDetailsRQ)
        {
            GiftVoucherPortServiceReference.retrieveGiftCertificateDetailsRequest inValue = new GiftVoucherPortServiceReference.retrieveGiftCertificateDetailsRequest();
            inValue.RetrieveGiftCertificateDetailsRQ = RetrieveGiftCertificateDetailsRQ;
            return ((GiftVoucherPortServiceReference.GiftVoucherPort)(this)).retrieveGiftCertificateDetailsAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<GiftVoucherPortServiceReference.retrieveVPPDetailsResponse> GiftVoucherPortServiceReference.GiftVoucherPort.retrieveVPPDetailsAsync(GiftVoucherPortServiceReference.retrieveVPPDetailsRequest request)
        {
            return base.Channel.retrieveVPPDetailsAsync(request);
        }
        
        public System.Threading.Tasks.Task<GiftVoucherPortServiceReference.retrieveVPPDetailsResponse> retrieveVPPDetailsAsync(GiftVoucherPortServiceReference.ViewVPPDetailsRQ ViewVPPDetailsRQ)
        {
            GiftVoucherPortServiceReference.retrieveVPPDetailsRequest inValue = new GiftVoucherPortServiceReference.retrieveVPPDetailsRequest();
            inValue.ViewVPPDetailsRQ = ViewVPPDetailsRQ;
            return ((GiftVoucherPortServiceReference.GiftVoucherPort)(this)).retrieveVPPDetailsAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<GiftVoucherPortServiceReference.voidVoucherResponse> GiftVoucherPortServiceReference.GiftVoucherPort.voidVoucherAsync(GiftVoucherPortServiceReference.voidVoucherRequest request)
        {
            return base.Channel.voidVoucherAsync(request);
        }
        
        public System.Threading.Tasks.Task<GiftVoucherPortServiceReference.voidVoucherResponse> voidVoucherAsync(GiftVoucherPortServiceReference.VoidVoucherRQ VoidVoucherRQ)
        {
            GiftVoucherPortServiceReference.voidVoucherRequest inValue = new GiftVoucherPortServiceReference.voidVoucherRequest();
            inValue.VoidVoucherRQ = VoidVoucherRQ;
            return ((GiftVoucherPortServiceReference.GiftVoucherPort)(this)).voidVoucherAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<GiftVoucherPortServiceReference.getTaxForGiftVoucherResponse> GiftVoucherPortServiceReference.GiftVoucherPort.getTaxForGiftVoucherAsync(GiftVoucherPortServiceReference.getTaxForGiftVoucherRequest request)
        {
            return base.Channel.getTaxForGiftVoucherAsync(request);
        }
        
        public System.Threading.Tasks.Task<GiftVoucherPortServiceReference.getTaxForGiftVoucherResponse> getTaxForGiftVoucherAsync(GiftVoucherPortServiceReference.GetTaxForGiftVoucherRQ GetTaxForGiftVoucherRQ)
        {
            GiftVoucherPortServiceReference.getTaxForGiftVoucherRequest inValue = new GiftVoucherPortServiceReference.getTaxForGiftVoucherRequest();
            inValue.GetTaxForGiftVoucherRQ = GetTaxForGiftVoucherRQ;
            return ((GiftVoucherPortServiceReference.GiftVoucherPort)(this)).getTaxForGiftVoucherAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<GiftVoucherPortServiceReference.sendGCEmailResponse> GiftVoucherPortServiceReference.GiftVoucherPort.sendGCEmailAsync(GiftVoucherPortServiceReference.sendGCEmailRequest request)
        {
            return base.Channel.sendGCEmailAsync(request);
        }
        
        public System.Threading.Tasks.Task<GiftVoucherPortServiceReference.sendGCEmailResponse> sendGCEmailAsync(GiftVoucherPortServiceReference.SendGCEmailRQ SendGCEmailRQ)
        {
            GiftVoucherPortServiceReference.sendGCEmailRequest inValue = new GiftVoucherPortServiceReference.sendGCEmailRequest();
            inValue.SendGCEmailRQ = SendGCEmailRQ;
            return ((GiftVoucherPortServiceReference.GiftVoucherPort)(this)).sendGCEmailAsync(inValue);
        }
        
        public virtual System.Threading.Tasks.Task OpenAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginOpen(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndOpen));
        }
        
        private static System.ServiceModel.Channels.Binding GetBindingForEndpoint(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.GiftVoucherPort))
            {
                System.ServiceModel.BasicHttpBinding result = new System.ServiceModel.BasicHttpBinding();
                result.MaxBufferSize = int.MaxValue;
                result.ReaderQuotas = System.Xml.XmlDictionaryReaderQuotas.Max;
                result.MaxReceivedMessageSize = int.MaxValue;
                result.AllowCookies = true;
                result.Security.Mode = System.ServiceModel.BasicHttpSecurityMode.Transport;
                return result;
            }
            throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
        }
        
        private static System.ServiceModel.EndpointAddress GetEndpointAddress(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.GiftVoucherPort))
            {
                return new System.ServiceModel.EndpointAddress("https://fhstg.ibsplc.aero/iRes_Booking_WS/services/GiftVoucherPort");
            }
            throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
        }
        
        private static System.ServiceModel.Channels.Binding GetDefaultBinding()
        {
            return GiftVoucherPortClient.GetBindingForEndpoint(EndpointConfiguration.GiftVoucherPort);
        }
        
        private static System.ServiceModel.EndpointAddress GetDefaultEndpointAddress()
        {
            return GiftVoucherPortClient.GetEndpointAddress(EndpointConfiguration.GiftVoucherPort);
        }
        
        public enum EndpointConfiguration
        {
            
            GiftVoucherPort,
        }
    }
}
