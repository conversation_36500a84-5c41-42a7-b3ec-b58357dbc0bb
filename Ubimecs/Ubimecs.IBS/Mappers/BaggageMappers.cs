using Ubimecs.IBS.Mappers.RequestMappers;
using Ubimecs.IBS.Models;
using Ubimecs.IBS.Utility;
using Ubimecs.Infrastructure.Caching;
using Ubimecs.Infrastructure.Models.Common;
using Ubimecs.Infrastructure.Models.DTO.Service;
using Ubimecs.Infrastructure.Models.Request;
using Ubimecs.Infrastructure.Models.Response;

namespace Ubimecs.IBS.Mappers
{
    public static class BaggageMappers
    {
        public static BaggageAllowanceRQ MapToAllowanceRequest(this GetBaggageAllowanceRequest request, SessionCache session)
        {
            BaggageAllowanceRQQueryOriginDestination originDestination = new BaggageAllowanceRQQueryOriginDestination();

            var flight = session.CurrentFlow.PNR.Flights.FirstOrDefault(w => w.TmobId == request.TmobId);

            if (session.CurrentFlow.Type == Infrastructure.Models.Flow.FlowType.Booking)
            {
                var airShoppingResponse = session.CurrentFlow.GetIbsData<AirShoppingRS>(IbsDataTypeEnum.ShopAirResponse.ToString());

                var airShoppingSegments = airShoppingResponse.GetDataList()?.FlightSegmentList?.Where(f => flight.Segments.Any(a => a.Id == f.SegmentKey));
                originDestination = new BaggageAllowanceRQQueryOriginDestination()
                {
                    OriginDestinationKey = airShoppingResponse.GetOriginDestinationId(flight.Id),
                    Flight = airShoppingSegments.Select(s => new FlightTypeFlight
                    {
                        SegmentKey = s.SegmentKey,
                        Departure = s.Departure,
                        Arrival = s.Arrival,
                        MarketingCarrier = s.MarketingCarrier,
                        Equipment = s.Equipment,
                        CabinType = new CabinType
                        {
                            Name = airShoppingResponse.GetCabinType(s.SegmentKey)?.CabinTypeName?.ToString(),
                            Code = airShoppingResponse.GetCabinType(s.SegmentKey)?.CabinTypeCode?.ToString()
                        },
                        ClassOfService = s.ClassOfService,
                        Details = s.FlightDetail
                    }).ToArray()
                };
            }
            else if (session.CurrentFlow.Type == Infrastructure.Models.Flow.FlowType.Rebooking)
            {
                var reshopResponse = session.CurrentFlow.GetIbsData<OrderReshopRS>(IbsDataTypeEnum.LatestRebookingSearchFlightResponse.ToString());

                if (reshopResponse != null)
                {
                    var reshopSegments = reshopResponse.GetDataList()?.FlightSegmentList?.Where(f => flight.Segments.Any(a => a.Id == f.SegmentKey));

                    originDestination = new BaggageAllowanceRQQueryOriginDestination()
                    {
                        OriginDestinationKey = reshopResponse.GetOriginDestinationId(flight.Id),
                        Flight = reshopSegments.Select(s => new FlightTypeFlight
                        {
                            SegmentKey = s.SegmentKey,
                            Departure = s.Departure,
                            Arrival = s.Arrival,
                            MarketingCarrier = s.MarketingCarrier,
                            Equipment = s.Equipment,
                            CabinType = new CabinType
                            {
                                Name = reshopResponse.GetCabinType(s.SegmentKey)?.CabinTypeName?.ToString(),
                                Code = reshopResponse.GetCabinType(s.SegmentKey)?.CabinTypeCode?.ToString()
                            },
                            ClassOfService = s.ClassOfService,
                            Details = s.FlightDetail
                        }).ToArray()
                    };
                }
                else
                {
                    var orderViewRS = session.CurrentFlow.GetIbsData<OrderViewRS>(IbsDataTypeEnum.OrderRetrieve);

                    var reshopSegments = orderViewRS.GetDataList()?.FlightSegmentList?.Where(f => flight.Segments.Any(a => a.Id == f.SegmentKey));

                    originDestination = new BaggageAllowanceRQQueryOriginDestination()
                    {
                        OriginDestinationKey = orderViewRS.GetOriginDestinationId(flight.Id),
                        Flight = reshopSegments.Select(s => new FlightTypeFlight
                        {
                            SegmentKey = s.SegmentKey,
                            Departure = s.Departure,
                            Arrival = s.Arrival,
                            MarketingCarrier = s.MarketingCarrier,
                            Equipment = s.Equipment,
                            CabinType = new CabinType
                            {
                                Name = orderViewRS.GetCabinType(s.SegmentKey)?.CabinTypeName?.ToString(),
                                Code = orderViewRS.GetCabinType(s.SegmentKey)?.CabinTypeCode?.ToString()
                            },
                            ClassOfService = s.ClassOfService,
                            Details = s.FlightDetail
                        }).ToArray()
                    };
                }
            }


            return new BaggageAllowanceRQ
            {
                PointOfSale = IbsUtility.PointOfSaleType(flight.Segments.First().DepartureCode, session.CurrentFlow.PNR.Currency),
                Document = IbsUtility.GetDocument(),
                Party = IbsUtility.GetPartiesType(),

                Item = new BaggageAllowanceRQQuery()
                {
                    Items = new BaggageAllowanceRQQueryOriginDestination[1] { originDestination }
                },
                DataLists = new BaggageAllowanceRQDataLists()
                {
                    PassengerList = session.CurrentFlow.PNR.Passengers.Select(s => s.Map()).ToArray()
                },

                EchoToken = null,
                TimeStampSpecified = false,
                Target = GeneralConstants.ServiceEnvironment == ServiceEnvironmentTypeEnum.Prod ? BaggageAllowanceRQTarget.Production : BaggageAllowanceRQTarget.Test,
                Version = GeneralConstants.BOOKING_VERSION,
                TransactionIdentifier = null,
                SequenceNmbr = null,
                TransactionStatusCode = BaggageAllowanceRQTransactionStatusCode.Start,
                TransactionStatusCodeSpecified = false,
                RetransmissionIndicator = false,
                RetransmissionIndicatorSpecified = false,
                CorrelationID = null,
                AsynchronousAllowedInd = false,
                AsynchronousAllowedIndSpecified = false
            };
        }
        public static BaggageChargesRQ MapToChageRequest(this GetBaggageAllowanceRequest request, SessionCache session)
        {
            BaggageChargesRQQueryOriginDestination originDestination = new BaggageChargesRQQueryOriginDestination();

            var flight = session.CurrentFlow.PNR.Flights.FirstOrDefault(w => w.TmobId == request.TmobId);

            if (session.CurrentFlow.Type == Infrastructure.Models.Flow.FlowType.Booking)
            {
                var airShoppingResponse = session.CurrentFlow.GetIbsData<AirShoppingRS>(IbsDataTypeEnum.ShopAirResponse.ToString());
                var airShoppingSegments = airShoppingResponse.GetDataList()?.FlightSegmentList?.Where(f => flight.Segments.Any(a => a.Id == f.SegmentKey));

                originDestination = new BaggageChargesRQQueryOriginDestination()
                {
                    OriginDestinationKey = airShoppingResponse.GetOriginDestinationId(flight.Id),
                    Flight = airShoppingSegments.Select(s => new FlightTypeFlight
                    {
                        SegmentKey = s.SegmentKey,
                        Departure = s.Departure,
                        Arrival = s.Arrival,
                        MarketingCarrier = s.MarketingCarrier,
                        Equipment = s.Equipment,
                        CabinType = new CabinType
                        {
                            Name = airShoppingResponse.GetCabinType(s.SegmentKey)?.CabinTypeName?.ToString(),
                            Code = airShoppingResponse.GetCabinType(s.SegmentKey)?.CabinTypeCode?.ToString()
                        },
                        ClassOfService = s.ClassOfService,
                        Details = s.FlightDetail
                    }).ToArray()
                };
            }
            else if (session.CurrentFlow.Type == Infrastructure.Models.Flow.FlowType.Rebooking)
            {
                var reshopResponse = session.CurrentFlow.GetIbsData<OrderReshopRS>(IbsDataTypeEnum.LatestRebookingSearchFlightResponse.ToString());

                if (reshopResponse != null)
                {
                    var reshopSegments = reshopResponse.GetDataList()?.FlightSegmentList?.Where(f => flight.Segments.Any(a => a.Id == f.SegmentKey));

                    originDestination = new BaggageChargesRQQueryOriginDestination()
                    {
                        OriginDestinationKey = reshopResponse.GetOriginDestinationId(flight.Id),
                        Flight = reshopSegments.Select(s => new FlightTypeFlight
                        {
                            SegmentKey = s.SegmentKey,
                            Departure = s.Departure,
                            Arrival = s.Arrival,
                            MarketingCarrier = s.MarketingCarrier,
                            Equipment = s.Equipment,
                            CabinType = new CabinType
                            {
                                Name = reshopResponse.GetCabinType(s.SegmentKey)?.CabinTypeName?.ToString(),
                                Code = reshopResponse.GetCabinType(s.SegmentKey)?.CabinTypeCode?.ToString()
                            },
                            ClassOfService = s.ClassOfService,
                            Details = s.FlightDetail
                        }).ToArray()
                    };
                }
                else
                {
                    var orderViewRS = session.CurrentFlow.GetIbsData<OrderViewRS>(IbsDataTypeEnum.OrderRetrieve);

                    var reshopSegments = orderViewRS.GetDataList()?.FlightSegmentList?.Where(f => flight.Segments.Any(a => a.Id == f.SegmentKey));

                    originDestination = new BaggageChargesRQQueryOriginDestination()
                    {
                        OriginDestinationKey = orderViewRS.GetOriginDestinationId(flight.Id),
                        Flight = reshopSegments.Select(s => new FlightTypeFlight
                        {
                            SegmentKey = s.SegmentKey,
                            Departure = s.Departure,
                            Arrival = s.Arrival,
                            MarketingCarrier = s.MarketingCarrier,
                            Equipment = s.Equipment,
                            CabinType = new CabinType
                            {
                                Name = orderViewRS.GetCabinType(s.SegmentKey)?.CabinTypeName?.ToString(),
                                Code = orderViewRS.GetCabinType(s.SegmentKey)?.CabinTypeCode?.ToString()
                            },
                            ClassOfService = s.ClassOfService,
                            Details = s.FlightDetail
                        }).ToArray()
                    };
                }
            }


            return new BaggageChargesRQ
            {
                PointOfSale = IbsUtility.PointOfSaleType(flight.Segments.First().DepartureCode, session.CurrentFlow.PNR.Currency),
                Document = IbsUtility.GetDocument(),
                Party = IbsUtility.GetPartiesType(),

                Item = new BaggageChargesRQQuery()
                {
                    Items = new BaggageChargesRQQueryOriginDestination[1] { originDestination }
                },
                DataLists = new BaggageChargesRQDataLists()
                {
                    PassengerList = session.CurrentFlow.PNR.Passengers.Select(s => s.Map()).ToArray()
                },

                EchoToken = null,
                TimeStampSpecified = false,
                Target = GeneralConstants.ServiceEnvironment == ServiceEnvironmentTypeEnum.Prod ? BaggageChargesRQTarget.Production : BaggageChargesRQTarget.Test,
                Version = GeneralConstants.BOOKING_VERSION,
                TransactionIdentifier = null,
                SequenceNmbr = null,
                TransactionStatusCode = BaggageChargesRQTransactionStatusCode.Start,
                TransactionStatusCodeSpecified = false,
                RetransmissionIndicator = false,
                RetransmissionIndicatorSpecified = false,
                CorrelationID = null,
                AsynchronousAllowedInd = false,
                AsynchronousAllowedIndSpecified = false
            };
        }



        public static GetBaggageAllowanceResponse Map(BaggageAllowanceRS allowanceResponse, BaggageChargesRS chargeResponse, SessionCache session, string flightTmobId)
        {
            var flight = session.CurrentFlow.PNR.Flights.FirstOrDefault(f => f.TmobId == flightTmobId);

            var result = new GetBaggageAllowanceResponse();

            var isInternational = flight.Segments.Any(a => a.IsInternational);

            for (int i = 0; i < session.CurrentFlow.PNR.Passengers.Count; i++)
            {
                var pass = session.CurrentFlow.PNR.Passengers[i];

                List<BaseBaggageDTO> baggages = new List<BaseBaggageDTO>();

                if (session.CurrentFlow.LatestPriceResponse != null)
                {
                    baggages = session.CurrentFlow.LatestPriceResponse.Services.Baggages;
                }
                else if (session.CurrentFlow.PnrInfoResponse != null)
                {
                    baggages = session.CurrentFlow.PnrInfoResponse.Services.Baggages;
                }
                if (session.CurrentFlow.RebookingType == Infrastructure.Models.Flow.RebookingTypeEnum.AddFlight )
                {
                    var checkedBags = ((BaggageAllowanceRSOriginDestination)allowanceResponse.Items.FirstOrDefault(t => t.GetType() == typeof(BaggageAllowanceRSOriginDestination))).CheckedBags;
                    var services = ((BaggageAllowanceRSOriginDestination)allowanceResponse.Items.FirstOrDefault(t => t.GetType() == typeof(BaggageAllowanceRSOriginDestination))).Services;
                    var item = checkedBags.CheckedBag[i];
                    var service = services.FirstOrDefault(t => t.ObjectKey == item.refs);
                    //var passengerId = service.Associations.FirstOrDefault().Passenger.Item.ToString();
                    var segmentReference = (SegmentReferences)service.Associations.FirstOrDefault()?.Flight.Items.FirstOrDefault();

                    string[] passengerIDs = new string[] { pass.Id };
                    string[] segmentIDs = segmentReference.Value.Split(' ').ToArray();


                    var newBaggageItem = new BaseBaggageDTO
                    {
                        Code = service.Encoding.Code.Value.ToString(),
                        Currency = service.Price.FirstOrDefault().Total.Code.ToString(),
                        DescriptionText = service.Name.Value.ToString(),
                        Name = service.Name.Value.ToString(),
                        MaximumWeight = item.WeightAllowance.MaximumWeight.FirstOrDefault().Value,
                        Id = service.ServiceID.Value.ToString(),
                        PassengerIds = passengerIDs,
                        Count = 1,
                        SegmentIds = segmentIDs,
                        TotalPrice = service.Price.FirstOrDefault().Total.Value
                    };
                    baggages.Add(newBaggageItem);
                    session.CurrentFlow.PnrInfoResponse.Services.Baggages = baggages;
                }
                var passengerExtraBaggageForCurrentSegment = session.CurrentFlow.PNR.ExtraBaggages?.FirstOrDefault(t => t.PassengerId == pass.Id && t.TmobId == flightTmobId)?.ExtraWeight ?? 0;
                result.PassengersFreeBagAllowances[session.CurrentFlow.PNR.Passengers[i].Id] = baggages
                     .FirstOrDefault(f =>
                            f.PassengerIds.Contains(pass.Id) &&
                            (
                                f.SegmentIds.Any(q=> flight.SegmentRefs.Split(' ').ToList().Contains(q)) ||
                                flight.SegmentRefs.Contains(f.SegmentIds.FirstOrDefault())
                            )
                            && f.MaximumWeight != 0)?.MaximumWeight - passengerExtraBaggageForCurrentSegment ?? 0;

                result.PassengersMaxBagAllowances[pass.Id] = (isInternational ? 40 : 35) - (result.PassengersFreeBagAllowances[session.CurrentFlow.PNR.Passengers[i].Id]);
            }

            return result;
        }

        public static List<ServiceDetailType> GetServices(this BaggageChargesRS response)
        {
            return response.Items?
                .Where(w => w is BaggageChargesRSBaggageCharge)?
                .Select(s => s as BaggageChargesRSBaggageCharge)?
                .FirstOrDefault()?
                .OriginDestination?
                .SelectMany(s => s.Services)?.ToList() ?? new List<ServiceDetailType>();
        }

        public static List<CheckedBagAllowanceTypeCheckedBag> GetGetCheckedBags(this BaggageChargesRS response)
        {
            return response.Items?
              .Where(w => w is BaggageChargesRSBaggageCharge)?
              .Select(s => s as BaggageChargesRSBaggageCharge)?
              .FirstOrDefault()?
              .OriginDestination?
              .SelectMany(s => s.CheckedBags?.CheckedBag)?.ToList() ?? new List<CheckedBagAllowanceTypeCheckedBag>();
        }


        public static List<ServiceDetailType> GetServices(this BaggageAllowanceRS response)
        {
            return response.Items?
                .Where(w => w is BaggageAllowanceRSOriginDestination)?
                .Select(s => s as BaggageAllowanceRSOriginDestination)?
                .FirstOrDefault()?
                .Services?.ToList() ?? new List<ServiceDetailType>();
        }

        public static List<CheckedBagAllowanceTypeCheckedBag> GetGetCheckedBags(this BaggageAllowanceRS response)
        {
            return response.Items?
               .Where(w => w is BaggageAllowanceRSOriginDestination)?
               .Select(s => s as BaggageAllowanceRSOriginDestination)?
               .FirstOrDefault()?
               .CheckedBags?.CheckedBag?.ToList() ?? new List<CheckedBagAllowanceTypeCheckedBag>();
        }
    }
}
