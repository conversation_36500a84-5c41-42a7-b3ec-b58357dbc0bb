using Ubimecs.Domain.Entities.UbimecsEntities;
using Ubimecs.IBS.bookingUtility;
using Ubimecs.Infrastructure.Caching;

namespace Ubimecs.IBS.Mappers.NativeApiMappers;

public static class BookingUtilityMappers
{
    public static CurrencyConvertorRQ Map(SessionCache session, ChannelConfiguration channelConfiguration, string fromCurrency, string toCurrency)
    {
        return new CurrencyConvertorRQ
        {
            AirlineCode = GeneralConstants.AIRLINECODE,
            BookingChannel = new bookingUtility.BookingChannelKeyType
            {
                ChannelType = GeneralConstants.BOOKING_CHANNEL_TYPE,
                Channel = channelConfiguration.ChannelCode, //GeneralConstants.BOOKING_CHANNEL_CODE,
                Locale = GeneralConstants.LOCALE,
                SessionId = !string.IsNullOrEmpty(session.AgencyInfo.SessionId) ? session.AgencyInfo.SessionId : null
            },
            FromCurrency = fromCurrency,
            ToCurrency =  toCurrency,
            FromAmount = 1
            
        };
    }
}