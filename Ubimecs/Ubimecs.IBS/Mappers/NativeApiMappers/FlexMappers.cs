using AncillaryPortServiceReference;
using Ubimecs.Domain.Entities.UbimecsEntities;
using Ubimecs.IBS.Models.Constants;
using Ubimecs.IBS.Utility;
using Ubimecs.Infrastructure.Models.Response;
using Ubimecs.Infrastructure.Models.Common;
using Ubimecs.Infrastructure.Caching;
using Ubimecs.Infrastructure.Models.DTO.Flight;

namespace Ubimecs.IBS.Mappers.NativeApiMappers
{
    public static class FlexMappers
    {
        public static ListSaleableAncillaryServicesRQ Map(SessionCache session, ChannelConfiguration channelConfiguration, string SegmentTmobId = null)
        {
            var flights = session.CurrentFlow?.PNR?.Flights.ToList();
            if (SegmentTmobId != null)
            {
                flights = flights.Where(f => f.Segments.Any(s => s.TmobId == SegmentTmobId)).ToList();
            }
            
            #region PassengerList
            var passengers = session.CurrentFlow?.PNR.Passengers.Where(x=>x.PassengerType != PassengerTypeEnum.INF).ToList();
            var passengerListMap = passengers?.MapToPassengerList();
            #endregion
            
            List<FlightSegmentDetailsType> flightSegmentInfos = new List<FlightSegmentDetailsType>();
            List<FareInfoType> fareInfos = new List<FareInfoType>();
            List<BundleAncillaries> bundleAncillaries = new List<BundleAncillaries>();
            string[] segmentIds = Array.Empty<string>();
            foreach (var flight in flights)
            {
                #region FlightSegmentDetails
                var flightSegmentDetailsMap = TmobIdToSegmentInfo(session, flight);
                flightSegmentInfos.Add(flightSegmentDetailsMap);
                #endregion   

                #region FareInfo
                var fareInfoType = MapFlightToFareInfoType(session, flight, passengerListMap);
                fareInfos.Add(fareInfoType);
                #endregion

                #region BundleAncillaries
                segmentIds = flight.SegmentRefs.Split(' ').Select(s => IbsUtility.ParseFullId(s)).ToArray();                
                if (flight.IsConnectedFlight)
                {
                    segmentIds = new[] {flightSegmentDetailsMap.SegmentId};
                }

                var selectedBundle = flight.SelectedBundleCode;
                var ancillaryListMap = MapBundleAncillaries(selectedBundle, segmentIds);
                bundleAncillaries.Add(ancillaryListMap);
                #endregion
            }

            return new ListSaleableAncillaryServicesRQ
            {                 
                AirlineCode = GeneralConstants.AIRLINECODE,
                BookingChannel = new BookingChannelKeyType
                {
                    ChannelType = GeneralConstants.BOOKING_CHANNEL_TYPE,
                    Channel = channelConfiguration.ChannelCode, //GeneralConstants.BOOKING_CHANNEL_CODE,
                    Locale = GeneralConstants.LOCALE,
                    SessionId = !string.IsNullOrEmpty(session.AgencyInfo.SessionId) ? session.AgencyInfo.SessionId : null
                },
                PointOfSale = IbsUtility.PointOfSaleType(flights.FirstOrDefault()?.DepartureCode, session.CurrentFlow.PNR.Currency).Location.CountryCode.Value,
                Itinerary = flightSegmentInfos.ToArray(),
                FareInfo = fareInfos.ToArray(),
                BundleAncillaries = bundleAncillaries.ToArray(),
                PassengerList = passengerListMap?.ToArray(),
                SaleDate = flights.FirstOrDefault()?.FlightDate ?? DateTime.Now,
                IsNotifiableSSR = false,
                IsNotifiableSSRSpecified = true
            };
        }
        public static GetFlexesResponse MapToFlexes(this listSaleableAncillaryServicesResponse response, SessionCache session, string SegmentTmobId = null)
        {
            var flights = session.CurrentFlow.PNR.Flights.ToList();
            var packages = flights.Where(f => !string.IsNullOrEmpty(f.SelectedBundleCode))
                .Select(f => (FlightPackage)Enum.Parse(typeof(FlightPackage), f.SelectedBundleCode))
                .ToList();  
            
            if (SegmentTmobId != null)
            {
                flights = flights.Where(f => f.Segments.Any(s => s.TmobId == SegmentTmobId)).ToList();
                var selectedFlight = flights.FirstOrDefault();
                if (selectedFlight?.SelectedBundleCode != null)
                {
                    packages = new List<FlightPackage> { (FlightPackage)Enum.Parse(typeof(FlightPackage), selectedFlight.SelectedBundleCode) };
                }
            }
                     
            var flexOptions = DetermineFlexOption(packages);
            var commonFlexPackages = GetCommonFlexOptions(flights, flexOptions); 

            List<FlexResponseDTO> flexes = new List<FlexResponseDTO>();
            
            var flexExtras = response.MapToAncillaryService(NativeAPIAncillaryConstants.FLEXEXTRA);
            flexes = flexExtras
                .Where(f => commonFlexPackages.Contains(f.ServiceCode))
                .DistinctBy(f => f.ServiceCode)
                .Select(f => new FlexResponseDTO
                {
                    Code = f.ServiceCode,
                    Category = f.ServiceCategory,
                    Name = f.ServiceName,
                    IsBundleFlex = f.IsBundle,
                    FeeAmount = f.FeeInformation.FeeAmount,
                    Currency = f.FeeInformation.FeeCurrency
                })
                .ToList();

            return new GetFlexesResponse
            {
                Flexes = flexes
            };
        }
        public static HashSet<string> GetCommonFlexOptions(List<SessionPnrFlightDTO> flights, List<string> flexOptions)
        {
            var now = DateTime.Now;

            // Uçuşlar üzerinden flex seçeneklerini belirliyoruz
            var flexOptionsPerFlight = flights.Select(flight =>
            {
                var flightFirstSegmentDepartureTime = flight.Segments.Min(x => x.CompleteDepartureTime);
                var hoursUntilFlight = (flightFirstSegmentDepartureTime - now).TotalHours;
                var availableFlexOptions = new HashSet<string>();

                if (hoursUntilFlight > 168) // 7 gün = 168 saat
                    availableFlexOptions.Add(FlexTypes.FLEX7);

                if (hoursUntilFlight > 72) // 3 gün = 72 saat
                    availableFlexOptions.Add(FlexTypes.FLEX3);

                return availableFlexOptions;
            }).ToList();

            // Ortak flex seçeneklerini buluyoruz
            var commonFlexOptions = flexOptionsPerFlight
                .Aggregate((prev, next) => prev.Intersect(next).ToHashSet());

            // Ortak olan flex seçenekleri ile DetermineFlexOption'dan dönen seçenekleri kesiştiriyoruz
            return commonFlexOptions.Intersect(flexOptions).ToHashSet();
        }
        public static List<string> DetermineFlexOption(List<FlightPackage> packages)
        {
            List<string> flexOptions = new ();
            
            bool hasEasyOrEco = packages.Any(p => p == FlightPackage.EasyBird || p == FlightPackage.EcoBird);
            if (!hasEasyOrEco)
            {
                return flexOptions;
            }
            
            var highestPriorityPackage = packages
                .OrderByDescending(p => (int)p)
                .First();
            
            switch (highestPriorityPackage)
            {
                case FlightPackage.PremiumBird:
                    flexOptions.Add(FlexTypes.FLEX3); // PremiumBird için sadece Flex3
                    break;
                case FlightPackage.SmartBird:
                    flexOptions.Add(FlexTypes.FLEX7); // SmartBird için sadece Flex7
                    break;
                case FlightPackage.EcoBird:
                case FlightPackage.EasyBird:
                    flexOptions.Add(FlexTypes.FLEX7);
                    flexOptions.Add(FlexTypes.FLEX3); // EcoBird ve EasyBird için Flex7 ve Flex3
                    break; // EcoBird ve EasyBird için Flex7 ve Flex3
            }
            return flexOptions;
        }
        
        public static FlightSegmentDetailsType TmobIdToSegmentInfo(SessionCache session, SessionPnrFlightDTO flight)
        {
            AvailabilityPortServiceReference.AirAvailabilityRS airAvailabilityRS = session.CurrentFlow.GetIbsData<AvailabilityPortServiceReference.AirAvailabilityRS>(IbsDataTypeEnum.ShopAirResponse.ToString());
            var segmentAvailability = airAvailabilityRS.GetSegmentAvailability(IbsUtility.ParseFullId(IbsUtility.ParseFullId(flight.Id)));
            var segmentInfo = airAvailabilityRS.GetSegmentInfoByFlightId(IbsUtility.ParseFullId(flight.Id));
            return segmentInfo.FlightSegmentDetailsMap(segmentAvailability);
        }
        public static FlightSegmentDetailsType FlightSegmentDetailsMap(this AvailabilityPortServiceReference.SegmentInfoType segmentInfoType, AvailabilityPortServiceReference.SegmentAvailabilityType segmentAvailability)
        {
            var flightSegmentDetailMap = new FlightSegmentDetailsType
            {
                SegmentId = segmentInfoType.SegmentIndex.ToString(),
                carrierCode = GeneralConstants.CARRIERCODE,
                fltNumber = segmentInfoType.FlightIdentifierInfo.FlightNumber.ToString(),
                fltSuffix = "*",
                flightDate = new DateOnlyDetailsType
                {
                    Date = segmentInfoType.FlightIdentifierInfo.FlightDate
                },
                boardPoint = segmentInfoType.DepartureInfo.AirportCode,
                offPoint = segmentInfoType.ArrivalInfo.AirportCode,
                CabinClass = segmentAvailability.CabinClass,
                FareClass = segmentAvailability.BookingClass,
                FlightSegmentGroupID = segmentInfoType.SegmentIndex.ToString(),
                scheduledDepartureDateTime = segmentInfoType.DepartureInfo.DateTime,
                scheduledArrivalTime = segmentInfoType.ArrivalInfo.DateTime,
                DepartureTimeZone = segmentInfoType.DepartureInfo.TimeZoneOffset,
                ArrivalTimeZone = segmentInfoType.ArrivalInfo.TimeZoneOffset,
                stops = segmentInfoType.Stops,
                arrivalDayChange = segmentInfoType.DayChange
            };
            return flightSegmentDetailMap;
        }
        public static FareInfoType MapFlightToFareInfoType(SessionCache session, SessionPnrFlightDTO flight, List<Passenger> paxCountDetails)
        {
            AvailabilityPortServiceReference.AirAvailabilityRS airAvailabilityRS = session.CurrentFlow.GetIbsData<AvailabilityPortServiceReference.AirAvailabilityRS>(IbsDataTypeEnum.ShopAirResponse.ToString());
            var pricingInfo = airAvailabilityRS.GetPricingInfo(IbsUtility.ParseFullId(flight.Id));
            var segmentInfo = airAvailabilityRS.GetSegmentInfoByFlightId(IbsUtility.ParseFullId(flight.Id));
            var originDestinationInfo = airAvailabilityRS.OriginDestinationInfo.FirstOrDefault(od => od.TripInfo.Any(ti => ti.TripIndex == Convert.ToInt64(IbsUtility.ParseFullId(flight.Id))));
            
            return new FareInfoType
            {
                FareDetailsForGuestType = paxCountDetails.Select(x => new FareDetailsForGuestType
                {
                    SegmentId = new long[]{segmentInfo.SegmentIndex},
                    PricingUnitIDSpecified = true,
                    BaseFareSpecified = true,
                    FareTransactionIDSpecified = true,
                    GuestTypeSpecified = true,
                    FareLevel = pricingInfo.PricingComponentInfo.FirstOrDefault()?.FareLevel,
                    FareType = pricingInfo.PricingComponentInfo.FirstOrDefault()?.FareType,
                    FareBasisCode = pricingInfo.PricingComponentInfo.FirstOrDefault()?.FareBasis,
                    GuestType = IbsUtility.Ancillary_PaxToPaxDetailsType(x.PaxType),
                    BaseFare = Convert.ToDouble(pricingInfo.PricingComponentInfo.FirstOrDefault()?.PaxBaseFare.FirstOrDefault(z=>z.PaxType == x.PaxType)?.Amount),
                    Currency = pricingInfo.PricingComponentInfo.FirstOrDefault()?.PaxBaseFare.FirstOrDefault(z=>z.PaxType == x.PaxType)?.CurrencyCode,
                    FareTransactionID = Convert.ToInt64(pricingInfo.PricingComponentInfo.FirstOrDefault()?.FareId),
                    PricingUnitID = originDestinationInfo.PricingUnitID,
                    FareComponentId = pricingInfo.PricingComponentInfo.FirstOrDefault()?.PricingComponentIndex.ToString()
                }).ToArray()
            };
        }
        public static BundleAncillaries MapBundleAncillaries(string selectedBundle, string[] segmentRefs)
        {
            return new BundleAncillaries
            {
                Bundle = selectedBundle,
                SegmentIds = segmentRefs.Select(s => Convert.ToInt64(IbsUtility.ParseFullId(s)))
                    .ToArray()
            };
        }
    }
}
