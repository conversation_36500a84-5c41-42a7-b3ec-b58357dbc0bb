using System.Linq;
using Ubimecs.Infrastructure.Models.Response;
using Ubimecs.Infrastructure.Models.DTO;
using Ubimecs.Infrastructure.Models.DTO.Passenger;
using Ubimecs.Infrastructure.Models.DTO.Price;
using Ubimecs.Infrastructure.Models.DTO.Service;
using Ubimecs.Infrastructure.Models.DTO.Mail;
using Ubimecs.Infrastructure.Models.DTO.Flight;
using Ubimecs.Infrastructure.Models.Common;
using Ubimecs.Infrastructure.Mailing.Models.IbsNative;

namespace Ubimecs.IBS.Mappers.NativeApiMappers;

public static class MailMappers
{
    public static SendItrMailResponse Map(this GetPnrInfoResponse getPnrInfoResponse)
    {

        var groupedPaymentDetails = getPnrInfoResponse.PaymentDetails.GroupBy(g => g.CardInfo.CardHolderName);
        List<GuestPaymentDetailsDTO> guestPaymentDetails = new List<GuestPaymentDetailsDTO>();
        foreach (var groupedPaymentDetail in groupedPaymentDetails)
        {
            guestPaymentDetails.Add(new GuestPaymentDetailsDTO
            {
                CardHolderName = groupedPaymentDetail.Key,
                PaymentRows = groupedPaymentDetail.ToList()
            });
        }

        // Flight segments
        var flightDestinations = getPnrInfoResponse?.FlightDestionationList ?? new List<OrderRetrieveFlightDestination>();
        var firstDestination = flightDestinations.FirstOrDefault();
        var secondDestination = flightDestinations.ElementAtOrDefault(1);
        var firstSegment = firstDestination?.Segments?.FirstOrDefault();
        var secondSegment = secondDestination?.Segments?.FirstOrDefault();

        // Contact and address information
        var primaryContact = getPnrInfoResponse?.ContactList?.FirstOrDefault();
        var postalAddress = primaryContact?.PostalAddress;
        var priceInfo = getPnrInfoResponse?.PriceInfo;
        var services = getPnrInfoResponse?.Services;

        // Get passenger and pricing information for dynamic mapping
        var passengers = getPnrInfoResponse?.PassengerList ?? new List<PassengerInformationDTO>();
        var firstFlightPrices = firstDestination?.Prices ?? new List<BasePriceDTO>();
        var secondFlightPrices = secondDestination?.Prices ?? new List<BasePriceDTO>();

        return new SendItrMailResponse
        {
            // Basic Information
            PnrNumber = getPnrInfoResponse.PNRNumber,
            FullName = $"{getPnrInfoResponse.Title ?? ""} {getPnrInfoResponse.Name ?? ""} {getPnrInfoResponse.Surname ?? ""}".Trim(),

            // Address information from contact details
            Address = postalAddress?.Street?.FirstOrDefault() ?? "-",
            PostalCode = postalAddress?.PostalCode ?? "-",
            City = postalAddress?.CityName ?? "-",
            Country = postalAddress?.CountryCode ?? "-",

            // Booking date from first segment departure date
            BookingDate = firstSegment?.DepartureDate.ToString("yyyy-MM-dd") ?? "-",

            // Airline name - use carrier name if available, otherwise airline ID
            AirlineName = firstSegment?.CarrierName ?? firstSegment?.AirlineId ?? "-",

            // IssuedBy field - use order created channel or default
            IssuedBy = !string.IsNullOrEmpty(getPnrInfoResponse.OrderCreatedChannel)
                ? getPnrInfoResponse.OrderCreatedChannel
                : "Online Booking System",

            // Flight Information
            FlightDay = firstSegment?.DepartureDate.ToString("dddd") ?? "-",
            FlightDate = firstSegment?.DepartureDate.ToString("dd MMMM yyyy") ?? "-",
            DepartureCity = firstSegment?.DepartureCity ?? "-",
            DepartureCode = firstSegment?.DepartureCode ?? "-",
            DepartureTime = firstSegment?.DepartureTime ?? "-",
            ArrivalCity = firstSegment?.ArrivalCity ?? "-",
            ArrivalCode = firstSegment?.ArrivalCode ?? "-",
            ArrivalTime = firstSegment?.ArrivalTime ?? "-",
            FlightNumber = firstSegment?.FlightNumber ?? "-",
            OperatorName = firstSegment?.CarrierName ?? "-",

            // Outbound/Inbound Flight Details
            OutboundDay = firstSegment?.DepartureDate.ToString("dddd") ?? "-",
            OutboundDate = firstSegment?.DepartureDate.ToString("dd MMMM yyyy") ?? "-",
            InboundDay = secondSegment?.DepartureDate.ToString("dddd") ?? "-",
            InboundDate = secondSegment?.DepartureDate.ToString("dd MMMM yyyy") ?? "-",

            // Dynamic Passenger Fare Information
            PassengerFares = MapPassengerFares(passengers, firstFlightPrices, secondFlightPrices,
                firstDestination, secondDestination, services, firstSegment, getPnrInfoResponse.Currency),

            // Total Pricing
            TotalFarePrice = FormatCurrency(priceInfo?.TotalAmountToBePaid, getPnrInfoResponse.Currency),
            NonRefundablePrice = FormatCurrency(priceInfo?.NonRefundableComponents, getPnrInfoResponse.Currency),
            CancellationFee1 = FormatCurrency(priceInfo?.CancellationFee, getPnrInfoResponse.Currency),
            CancellationFee2 = FormatCurrency(priceInfo?.CancellationFee, getPnrInfoResponse.Currency),
            TotalAmount = !string.IsNullOrEmpty(getPnrInfoResponse?.TotalAmount) &&
                         decimal.TryParse(getPnrInfoResponse.TotalAmount, out var totalAmountValue)
                         ? FormatCurrency(totalAmountValue, getPnrInfoResponse.Currency)
                         : FormatCurrency(priceInfo?.TotalAmountToBePaid ?? 0, getPnrInfoResponse.Currency),
            Currency = getPnrInfoResponse.Currency ?? "EUR",

            // Payment Information - Get from actual payment details
            Installment = GetInstallmentInfo(getPnrInfoResponse.PaymentDetails),

            // Dynamic Service Features
            SeatFeatures = MapServiceFeatures(services?.Seats, new[]
            {
                "Choose your preferred seat",
                "Extra legroom available",
                "Window or aisle preference"
            }),
            LuggageFeatures = MapServiceFeatures(services?.Baggages, new[]
            {
                "Additional baggage allowance",
                "Priority baggage handling",
                "Oversized baggage options"
            }),
            MealFeatures = MapServiceFeatures(services?.Meals, new[]
            {
                "Special dietary requirements",
                "Premium meal selection",
                "Pre-order your meal"
            }),

            // Button Texts - Configurable based on availability
            SeatButtonText = services?.Seats?.Any() == true ? "Select Seats" : "Seats Not Available",
            LuggageButtonText = services?.Baggages?.Any() == true ? "Add Baggage" : "Baggage Not Available",
            MealButtonText = services?.Meals?.Any() == true ? "Choose Meals" : "Meals Not Available",

            // URLs - Generate based on PNR and flight information
            AllTicketsUrl = GenerateTicketUrl(getPnrInfoResponse.PNRNumber, "all"),
            TicketUrl1 = GenerateTicketUrl(getPnrInfoResponse.PNRNumber, passengers.FirstOrDefault()?.Id),
            SeatReservationUrl = GenerateServiceUrl(getPnrInfoResponse.PNRNumber, "seats"),
            LuggageUrl = GenerateServiceUrl(getPnrInfoResponse.PNRNumber, "baggage"),
            MealUrl = GenerateServiceUrl(getPnrInfoResponse.PNRNumber, "meals"),

            // Icon URLs - Use configured paths
            // DocumentIconUrl = "/images/icons/document.png",
            // DownloadIconUrl = "/images/icons/download.png",
            // UserIconUrl = "/images/icons/user.png",
            // ArrowIconUrl = "/images/icons/arrow.png",
            // CheckmarkIconUrl = "/images/icons/checkmark.png",
            // SeatMapImageUrl = "/images/seat-map.png",
            // LuggageImageUrl = "/images/luggage.png",
            // MealImageUrl = "/images/meal.png",

            // Miscellaneous - Determine based on flight type
            HoursBeforeTime = GetCheckInTime(firstSegment?.IsInternational),
            isInternational = firstSegment?.IsInternational ?? false,

            PassengerList = getPnrInfoResponse?.PassengerList?.Select(p => new PassengerInformationDTO
            {
                Id = p.Id,
                PassengerType = p.PassengerType,
                Birthdate = p.Birthdate,
                Birthplace = p.Birthplace,
                Gender = p.Gender,
                NameTitle = (p.NameTitle ?? "").Trim(),
                GivenName = (p.GivenName ?? "").Trim(),
                Surname = (p.Surname ?? "").Trim(),
                InfantId = p.InfantId,
                ParentGuestID = p.ParentGuestID,
                PaybackAccountNumber = p.PaybackAccountNumber,
                PaybackCouponNumber = p.PaybackCouponNumber,
                CountryCode = p.CountryCode,
                IdentityNumber = p.IdentityNumber,
                MemberShipNumber = p.MemberShipNumber,
                NumberInParty = p.NumberInParty,
                ETicketNumber = p.ETicketNumber,
                HesCode = p.HesCode,
                DocumentNumber = p.DocumentNumber,
                DocumentType = p.DocumentType,
                DateOfExpiry = p.DateOfExpiry,
                IssuingCountry = p.IssuingCountry,
                CitizenshipCountryCode = p.CitizenshipCountryCode,
                ResidenceCountryCode = p.ResidenceCountryCode,
                VisaNo = p.VisaNo,
                VisaIssuedBy = p.VisaIssuedBy,
                IFEAccessCode = p.IFEAccessCode
            }).ToList(),
            GuestPaymentDetails = guestPaymentDetails
        };
    }



    private static string GetPassengerFarePrice(List<BasePriceDTO> prices, string passengerId, string currency)
    {
        if (prices == null)
            return FormatCurrency(0, currency);

        // Get fare price from the flight's price breakdown
        var farePrice = prices.FirstOrDefault(p => p.FareType == BasePriceFareTypeEnum.FARE);
        if (farePrice != null)
        {
            // Parse the value which might be in format "123.45 EUR"
            var valueStr = farePrice.Value;
            if (!string.IsNullOrEmpty(valueStr))
            {
                // Extract numeric part from value like "123.45 EUR"
                var parts = valueStr.Split(' ');
                if (parts.Length > 0 && decimal.TryParse(parts[0], out var amount))
                    return FormatCurrency(amount, currency);
            }
        }

        // If no specific fare found, try to get base price
        var basePrice = prices.FirstOrDefault(p => !string.IsNullOrEmpty(p.Value));
        if (basePrice != null)
        {
            var valueStr = basePrice.Value;
            if (!string.IsNullOrEmpty(valueStr))
            {
                // Extract numeric part from value like "123.45 EUR"
                var parts = valueStr.Split(' ');
                if (parts.Length > 0 && decimal.TryParse(parts[0], out var baseAmount))
                    return FormatCurrency(baseAmount, currency);
            }
        }

        return FormatCurrency(0, currency);
    }

    private static string GetServicePrice(List<BaseServiceDTO> services, string flightId, string passengerId, string currency)
    {
        if (services == null || services.Count == 0)
            return FormatCurrency(0, currency);

        // Try to find service for specific flight and passenger
        var service = services.FirstOrDefault(s =>
            (string.IsNullOrEmpty(flightId) || s.FlightIds?.Contains(flightId) == true) &&
            (string.IsNullOrEmpty(passengerId) || s.PassengerIds?.Contains(passengerId) == true));

        // If no specific service found, get the first available service
        if (service == null)
            service = services.FirstOrDefault();

        return service != null ? FormatCurrency(service.TotalPrice, currency) : FormatCurrency(0, currency);
    }

    private static BaseServiceDTO GetService(List<BaseServiceDTO> services, string flightId, string passengerId)
    {
        if (services == null || services.Count == 0)
            return null;

        // Try to find service for specific flight and passenger
        var service = services.FirstOrDefault(s =>
            (string.IsNullOrEmpty(flightId) || s.FlightIds?.Contains(flightId) == true) &&
            (string.IsNullOrEmpty(passengerId) || s.PassengerIds?.Contains(passengerId) == true));

        // If no specific service found, get the first available service
        if (service == null)
            service = services.FirstOrDefault();

        return service;
    }

    private static string GetBundlePrice(List<BaseBundleDTO> bundles, string flightId, string currency)
    {
        if (bundles == null || bundles.Count == 0)
            return FormatCurrency(0, currency);

        // Try to find bundle for specific flight
        var bundle = bundles.FirstOrDefault(b =>
            string.IsNullOrEmpty(flightId) || b.FlightIds?.Contains(flightId) == true);

        // If no specific bundle found, get the first available bundle
        if (bundle == null)
            bundle = bundles.FirstOrDefault();

        return bundle != null ? FormatCurrency(bundle.TotalPrice, currency) : FormatCurrency(0, currency);
    }

    private static string FormatCurrency(decimal? amount, string currency)
    {
        if (!amount.HasValue) return $"0.00 {currency ?? "EUR"}";

        return currency?.ToUpper() switch
        {
            "EUR" => amount.Value.ToString("C", new System.Globalization.CultureInfo("de-DE")),
            "USD" => amount.Value.ToString("C", new System.Globalization.CultureInfo("en-US")),
            "TRY" => amount.Value.ToString("C", new System.Globalization.CultureInfo("tr-TR")),
            _ => $"{amount.Value:F2} {currency ?? "EUR"}"
        };
    }

    private static string GetInstallmentInfo(List<GuestPaymentInfoDTO> paymentDetails)
    {
        if (paymentDetails == null || !paymentDetails.Any())
            return "1";

        // Count unique payment dates to determine installments
        var uniquePaymentDates = paymentDetails.Select(p => p.PaymentDate).Distinct().Count();
        return uniquePaymentDates.ToString();
    }

    private static string GetServiceFeature(List<BaseServiceDTO> services, int index)
    {
        return services?.ElementAtOrDefault(index)?.DescriptionText;
    }

    private static string GetServiceFeature(List<BaseBaggageDTO> baggages, int index)
    {
        return baggages?.ElementAtOrDefault(index)?.DescriptionText;
    }

    private static string GetServiceFeature(List<BaseSeatDTO> seats, int index)
    {
        return seats?.ElementAtOrDefault(index)?.DescriptionText;
    }

    private static string GenerateTicketUrl(string pnrNumber, string identifier)
    {
        if (string.IsNullOrEmpty(pnrNumber))
            return "#";

        return $"/download/ticket/{pnrNumber}/{identifier}";
    }

    private static string GenerateServiceUrl(string pnrNumber, string serviceType)
    {
        if (string.IsNullOrEmpty(pnrNumber))
            return "#";

        return $"/services/{serviceType}?pnr={pnrNumber}";
    }

    private static string GetCheckInTime(bool? isInternational)
    {
        return isInternational == true ? "3 hours" : "2 hours";
    }

    private static List<PassengerFareInfoDTO> MapPassengerFares(
        List<PassengerInformationDTO> passengers,
        List<BasePriceDTO> firstFlightPrices,
        List<BasePriceDTO> secondFlightPrices,
        OrderRetrieveFlightDestination firstDestination,
        OrderRetrieveFlightDestination secondDestination,
        AllServices services,
        FlightSegmentDTO firstSegment,
        string currency)
    {
        if (passengers == null || passengers.Count == 0)
            return new List<PassengerFareInfoDTO>();

        var fareType = firstSegment?.PriceClassName ?? firstSegment?.CabinClass ?? "Economy";

        return passengers.Select(passenger => new PassengerFareInfoDTO
        {
            PassengerName = $"{passenger.GivenName?.Trim()} {passenger.Surname?.Trim()}".Trim(),
            FareType = fareType,
            OutboundFarePrice = GetPassengerFarePrice(firstFlightPrices, passenger.Id, currency),
            InboundFarePrice = GetPassengerFarePrice(secondFlightPrices, passenger.Id, currency),
            OutboundServices = MapPassengerServices(services, firstDestination?.Id, passenger.Id, currency),
            InboundServices = MapPassengerServices(services, secondDestination?.Id, passenger.Id, currency)
        }).ToList();
    }

    private static List<ServicePriceDTO> MapPassengerServices(
        AllServices services,
        string flightId,
        string passengerId,
        string currency)
    {
        var serviceList = new List<ServicePriceDTO>();

        // Pet Cabin Service
        var petCabinPrice = GetServicePrice(services?.Others, flightId, passengerId, currency);
        if (!string.IsNullOrEmpty(petCabinPrice) && petCabinPrice != FormatCurrency(0, currency))
        {
            serviceList.Add(new ServicePriceDTO
            {
                ServiceType = "PET_CABIN",
                ServiceName = "PET IN CABIN",
                Price = petCabinPrice
            });
        }

        // Meal Service
        var mealService = GetService(services?.Meals, flightId, passengerId);
        if (mealService != null)
        {
            var mealPrice = FormatCurrency(mealService.TotalPrice, currency);
            if (!string.IsNullOrEmpty(mealPrice) && mealPrice != FormatCurrency(0, currency))
            {
                serviceList.Add(new ServicePriceDTO
                {
                    ServiceType = "MEAL",
                    ServiceName = !string.IsNullOrEmpty(mealService.Name) ? mealService.Name : "Meal Service",
                    Price = mealPrice
                });
            }
        }

        // Standard Bundle Service
        var standardPrice = GetBundlePrice(services?.Bundles, flightId, currency);
        if (!string.IsNullOrEmpty(standardPrice) && standardPrice != FormatCurrency(0, currency))
        {
            serviceList.Add(new ServicePriceDTO
            {
                ServiceType = "STANDARD",
                ServiceName = "STANDARD",
                Price = standardPrice
            });
        }

        return serviceList;
    }

    private static List<ServiceFeatureDTO> MapServiceFeatures<T>(List<T> services, string[] defaultFeatures)
        where T : class
    {
        var features = new List<ServiceFeatureDTO>();

        if (services != null && services.Count > 0)
        {
            // Use actual service descriptions if available
            for (int i = 0; i < services.Count && i < 3; i++)
            {
                var description = GetServiceDescription(services[i]);
                if (!string.IsNullOrEmpty(description))
                {
                    features.Add(new ServiceFeatureDTO { FeatureText = description });
                }
            }
        }

        // Fill remaining slots with defaults
        while (features.Count < 3 && features.Count < defaultFeatures.Length)
        {
            features.Add(new ServiceFeatureDTO { FeatureText = defaultFeatures[features.Count] });
        }

        return features;
    }

    private static string GetServiceDescription(object service)
    {
        return service switch
        {
            BaseBaggageDTO baggage => baggage.DescriptionText,
            BaseSeatDTO seat => seat.DescriptionText,
            BaseServiceDTO baseService => baseService.DescriptionText,
            _ => null
        };
    }

    public static ScheduleChangeMailModel Map(this SendScheduleChangeDto request)
    {
        var originalFlight = request.AffectedBookingInfo?.OriginalFlightInfo;
        var reaccommodatedFlight = request.AffectedBookingInfo?.ReAccomodatedBookingInfo;
        var contacts = reaccommodatedFlight?.Pnrinfo?.Contacts;
        var customerInfo = reaccommodatedFlight?.Pnrinfo?.CustomerInfo;

        // Parse original flight dates and times
        var originalDepDate = ParseFlightDate(originalFlight?.SegmentInfo?.DepDate);
        var originalDepTime = FormatFlightTime(originalFlight?.SegmentInfo?.DepTime);
        var originalArrTime = FormatFlightTime(originalFlight?.SegmentInfo?.ArrTime);
        var originalArrDate = ParseFlightDate(originalFlight?.SegmentInfo?.ArrDate);

        // Parse new flight dates and times
        var newDepDate = ParseFlightDate(reaccommodatedFlight?.SegmentInfo?.DepDate);
        var newDepTime = FormatFlightTime(reaccommodatedFlight?.SegmentInfo?.DepTime);
        var newArrTime = FormatFlightTime(reaccommodatedFlight?.SegmentInfo?.ArrTime);
        var newArrDate = ParseFlightDate(reaccommodatedFlight?.SegmentInfo?.ArrDate);

        // Extract airport codes from route
        var originalRoute = ParseRoute(originalFlight?.Route);
        var newRoute = ParseRoute(reaccommodatedFlight?.Route);

        // Determine if we have updated flight information
        // For TIME_CHANGE_WITHIN_THRESHOLD, we always show both original and new times even for minor changes
        bool hasUpdatedFlight = newDepDate.HasValue && reaccommodatedFlight?.SegmentInfo != null &&
            (newDepDate != originalDepDate || newDepTime != originalDepTime || newArrTime != originalArrTime);

        return new ScheduleChangeMailModel
        {
            // Basic Information
            PnrNumber = reaccommodatedFlight?.Pnrinfo?.PnrNo ?? request.PNRNo ?? "",
            FullName = GetFirstPassengerName(customerInfo?.PaxDetails),

            // Contact Information
            ContactEmail = contacts?.Email ?? request.Email ?? "",
            ContactPhone = contacts?.PhoneNo ?? "",
            AlternateEmail = contacts?.AlternateEmail ?? "",
            ContactType = contacts?.ContactType ?? "",
            IsPreferredContact = contacts?.IsPreferredContact ?? "",

            // Booking Information
            BookingDate = DateTime.Now.ToString("dd MMMM yyyy"),
            IssuedBy = reaccommodatedFlight?.Pnrinfo?.PointOfsale ?? "",
            AirlineName = ExtractAirlineFromFlightNumber(originalFlight?.SegmentInfo?.FltNo),

            // Action and Reason Codes
            ActionCode = request.ActionCode ?? "",
            ReasonCode = request.ReasonCode ?? "",

            // Original Flight Information - Pre-formatted
            OriginalFlightDay = originalDepDate?.ToString("dddd") ?? "",
            OriginalFlightDate = originalDepDate?.ToString("dd MMMM yyyy") ?? "",
            OriginalDepartureCity = GetCityFromCode(originalRoute.DepartureCode),
            OriginalDepartureCode = originalRoute.DepartureCode,
            OriginalDepartureTime = originalDepTime,
            OriginalArrivalCity = GetCityFromCode(originalRoute.ArrivalCode),
            OriginalArrivalCode = originalRoute.ArrivalCode,
            OriginalArrivalTime = originalArrTime,
            OriginalArrivalDate = originalArrDate?.ToString("dd MMMM yyyy") ?? "",
            OriginalFlightNumber = originalFlight?.SegmentInfo?.FltNo ?? "",
            OriginalRoute = originalFlight?.Route ?? "",
            OriginalBoardingPoint = originalFlight?.SegmentInfo?.BrdPoint ?? "",
            OriginalOffPoint = originalFlight?.SegmentInfo?.OffPoint ?? "",
            OriginalStops = originalFlight?.SegmentInfo?.Stops ?? "",

            // New Flight Information - Pre-formatted
            HasUpdatedFlight = hasUpdatedFlight,
            NewFlightDay = hasUpdatedFlight ? newDepDate?.ToString("dddd") ?? "" : "",
            NewFlightDate = hasUpdatedFlight ? newDepDate?.ToString("dd MMMM yyyy") ?? "" : "",
            NewDepartureCity = hasUpdatedFlight ? GetCityFromCode(newRoute.DepartureCode) : "",
            NewDepartureCode = hasUpdatedFlight ? newRoute.DepartureCode : "",
            NewDepartureTime = hasUpdatedFlight ? newDepTime : "",
            NewArrivalCity = hasUpdatedFlight ? GetCityFromCode(newRoute.ArrivalCode) : "",
            NewArrivalCode = hasUpdatedFlight ? newRoute.ArrivalCode : "",
            NewArrivalTime = hasUpdatedFlight ? newArrTime : "",
            NewArrivalDate = hasUpdatedFlight ? newArrDate?.ToString("dd MMMM yyyy") ?? "" : "",
            NewFlightNumber = hasUpdatedFlight ? reaccommodatedFlight?.SegmentInfo?.FltNo ?? "" : "",
            NewRoute = hasUpdatedFlight ? reaccommodatedFlight?.Route ?? "" : "",
            NewBoardingPoint = hasUpdatedFlight ? reaccommodatedFlight?.SegmentInfo?.BrdPoint ?? "" : "",
            NewOffPoint = hasUpdatedFlight ? reaccommodatedFlight?.SegmentInfo?.OffPoint ?? "" : "",
            NewStops = hasUpdatedFlight ? reaccommodatedFlight?.SegmentInfo?.Stops ?? "" : "",

            // Passenger Information - Dynamic
            PassengerList = MapScheduleChangePassengers(customerInfo?.PaxDetails),

            // Additional Information
            IsInternational = IsInternationalFlight(originalRoute.DepartureCode, originalRoute.ArrivalCode),
            HoursBeforeTime = IsInternationalFlight(originalRoute.DepartureCode, originalRoute.ArrivalCode) ? "3 hours" : "2 hours",

            // URLs
            ManageBookingUrl = $"/manage-booking?pnr={reaccommodatedFlight?.Pnrinfo?.PnrNo ?? request.PNRNo}",

            // Operator Information
            OperatorName = ExtractAirlineFromFlightNumber(originalFlight?.SegmentInfo?.FltNo),

            // Additional fields for TimeChange template compatibility
            Address = "", // Not available in SendScheduleChangeDto
            PostalCode = "", // Not available in SendScheduleChangeDto
            City = "", // Not available in SendScheduleChangeDto
            Country = "", // Not available in SendScheduleChangeDto
            Currency = "EUR" // Default currency, could be enhanced later
        };
    }

    private static DateTime? ParseFlightDate(string dateStr)
    {
        if (string.IsNullOrEmpty(dateStr) || dateStr.Length != 8)
            return null;

        if (DateTime.TryParseExact(dateStr, "yyyyMMdd", System.Globalization.CultureInfo.InvariantCulture,
            System.Globalization.DateTimeStyles.None, out var date))
            return date;

        return null;
    }

    private static string FormatFlightTime(string timeStr)
    {
        if (string.IsNullOrEmpty(timeStr) || timeStr.Length != 4)
            return "";

        if (TimeSpan.TryParseExact(timeStr, "hhmm", System.Globalization.CultureInfo.InvariantCulture, out var time))
            return time.ToString(@"hh\:mm");

        return timeStr;
    }

    private static (string DepartureCode, string ArrivalCode) ParseRoute(string route)
    {
        if (string.IsNullOrEmpty(route))
            return ("", "");

        var parts = route.Split('-', StringSplitOptions.RemoveEmptyEntries);

        if (parts.Length == 0)
            return ("", "");

        if (parts.Length == 1)
            return (parts[0].Trim(), "");

        return (parts[0].Trim(), parts[parts.Length - 1].Trim());
    }

    private static string GetFirstPassengerName(List<PaxDetailDto> paxDetails)
    {
        if (paxDetails == null || !paxDetails.Any())
            return ""; // Return empty string instead of hardcoded "Passenger"

        var firstPassenger = paxDetails.First();
        var givenName = firstPassenger.GivenName ?? "";
        var surname = firstPassenger.SurName ?? "";

        return $"{givenName} {surname}".Trim();
    }

    private static List<ScheduleChangePassengerDto> MapScheduleChangePassengers(List<PaxDetailDto> paxDetails)
    {
        if (paxDetails == null)
            return new List<ScheduleChangePassengerDto>();

        return paxDetails.Select(pax => new ScheduleChangePassengerDto
        {
            PassengerId = pax.PaxID ?? "",
            GivenName = pax.GivenName ?? "",
            Surname = pax.SurName ?? "",
            FullName = $"{pax.GivenName} {pax.SurName}".Trim(),
            TicketNumber = pax.TicketNo ?? "",
            PassengerType = "Adult" // Default, can be enhanced based on age or other criteria
        }).ToList();
    }

    private static string ExtractAirlineFromFlightNumber(string flightNumber)
    {
        // TODO: Implement proper airline code to airline name mapping service
        if (string.IsNullOrEmpty(flightNumber))
            return ""; // Return empty string instead of hardcoded "Airline"

        // Extract airline code from flight number (e.g., "FH512" -> "FH")
        var airlineCode = new string(flightNumber.TakeWhile(char.IsLetter).ToArray());

        if (string.IsNullOrEmpty(airlineCode))
            return ""; // Return empty if no airline code found

        return airlineCode.ToUpper() switch
        {
            "FH" => "Freebird Airlines",
            // Add more airline mappings as needed
            _ => airlineCode.ToUpper() // Return airline code if no name mapping found
        };
    }

    private static string GetCityFromCode(string airportCode)
    {
        // TODO: Implement proper airport code to city mapping service
        // For now, return the airport code itself if no mapping is available
        if (string.IsNullOrEmpty(airportCode))
            return "";

        return airportCode switch
        {
            "BCN" => "Barcelona",
            "RMO" => "Rome",
            "FCO" => "Rome",
            "IST" => "Istanbul",
            "ADB" => "Izmir",
            "AYT" => "Antalya",
            "ESB" => "Ankara",
            "AMS" => "Amsterdam",
            "LHR" => "London",
            "CDG" => "Paris",
            "MAD" => "Madrid",
            "FRA" => "Frankfurt",
            // Add more mappings as needed
            _ => airportCode // Return airport code if no city mapping found
        };
    }

    private static bool IsInternationalFlight(string departureCode, string arrivalCode)
    {
        // TODO: Implement proper country code mapping service to determine international flights
        // For now, use a basic Turkish domestic airport list
        if (string.IsNullOrEmpty(departureCode) || string.IsNullOrEmpty(arrivalCode))
            return false; // Default to domestic if codes are missing

        var turkishDomesticCodes = new[] {
            "IST", "ADB", "AYT", "ESB", "TZX", "BJV", "GZT", "MLX",
            "VAN", "ERZ", "TRA", "SZF", "DNZ", "ERC", "KYA", "MSR",
            "ASR", "BAL", "BZI", "CKZ", "DIY", "EZS", "GNY", "HTY",
            "IGD", "KFS", "KSY", "MZH", "NOP", "OGU", "SIC", "TEQ",
            "USQ", "YEI", "ZAP"
        };

        // If both airports are in Turkish domestic list, it's domestic
        bool isDomestic = turkishDomesticCodes.Contains(departureCode) &&
                         turkishDomesticCodes.Contains(arrivalCode);

        return !isDomestic; // Return true if not domestic (i.e., international)
    }
}