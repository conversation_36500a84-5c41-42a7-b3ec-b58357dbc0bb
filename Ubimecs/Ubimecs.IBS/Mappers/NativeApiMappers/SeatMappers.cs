using AvailabilityPortServiceReference;
using FlightPortServiceReference;
using ReservationsPortServiceReference;
using Ubimecs.Domain.Entities.UbimecsEntities;
using Ubimecs.IBS.Models.Constants;
using Ubimecs.IBS.Utility;
using Ubimecs.Infrastructure.Caching;
using Ubimecs.Infrastructure.Models.Common;
using Ubimecs.Infrastructure.Models.DTO.Seat;
using Ubimecs.Infrastructure.Models.Flow;
using Ubimecs.Infrastructure.Models.Request;
using Ubimecs.Infrastructure.Models.Response;
using BookingChannelKeyType = FlightPortServiceReference.BookingChannelKeyType;
using DateOnlyType = FlightPortServiceReference.DateOnlyType;
using FlightSegmentDetailsType = FlightPortServiceReference.FlightSegmentDetailsType;
using PaxCountType = FlightPortServiceReference.PaxCountType;

namespace Ubimecs.IBS.Mappers.NativeApiMappers
{
    public static class SeatMappers
    {
        public static ShowSeatMapRQ ShowSeatsRequestMap(this GetFlightSeatsRequest request, SessionCache session, ChannelConfiguration channelConfiguration) 
        {
            List<PaxCountType> passengerMap = new();

            var flight = session.CurrentFlow.PNR.Flights.FirstOrDefault(f => f.Segments.Any(s => s.TmobId == request.SegmentTmobId));
            var flightSelectedBundleCode = flight?.SelectedBundleCode;
            var flightSegment = flight?.Segments.FirstOrDefault(f => f.TmobId == request.SegmentTmobId);
            var passengers = session.CurrentFlow?.PNR?.Passengers;
            var groupedPassengers = passengers?.GroupBy(x => x.PassengerType);

            foreach (var gp in groupedPassengers)
            {
                var paxType = IbsUtility.MapPaxType(gp.Key);
                passengerMap.Add(new PaxCountType { PaxType = paxType, PaxCount = gp.Count() });
            }

            List<FlightSegmentDetailsType> segmentInfos = new List<FlightSegmentDetailsType>();
            if (session.CurrentFlow.Type == FlowType.Rebooking)
            {
                var latestGetPnrInfoResponse = session.CurrentFlow.GetIbsData<RetrieveBookingRS>(IbsDataTypeEnum.OrderRetrieve);
                var itinary = latestGetPnrInfoResponse.Itinerary.FirstOrDefault();
                
                if (itinary != null)
                {
                    var flightSegmentDetails = new FlightPortServiceReference.FlightSegmentDetailsType
                    {
                        FlightSegmentGroupID = itinary.FlightSegmentGroupID,
                        SegmentId = itinary.SegmentId,
                        carrierCode = GeneralConstants.CARRIERCODE,
                        fltNumber = itinary.fltNumber,
                        fltSuffix = "*",
                        flightDate = new FlightPortServiceReference.DateOnlyDetailsType
                        {
                            Date = itinary.flightDate.Date
                        },
                        boardPoint = itinary.boardPoint,
                        offPoint = itinary.offPoint,
                        CabinClass = itinary.CabinClass,
                        FareClass = itinary.FareClass,
                        scheduledDepartureDateTime = itinary.scheduledDepartureDateTime,
                        scheduledArrivalTime = itinary.scheduledArrivalTime,
                        DepartureTimeZone = itinary.DepartureTimeZone,
                        ArrivalTimeZone = itinary.ArrivalTimeZone,
                        stops = itinary.stops,
                        arrivalDayChange = itinary.arrivalDayChange
                    };
                    
                    segmentInfos.Add(flightSegmentDetails);
                }
            }
            else
            {
                var flightSegmentDetails = TmobIdToSegmentInfo(session, flight.TmobId);
                segmentInfos.Add(flightSegmentDetails);
            }
            
            return new ShowSeatMapRQ
            {
                AirlineCode = GeneralConstants.CARRIERCODE,
                SegmentInfo = new SegmentInfo
                {
                    BoardPoint = flight.DepartureCode,
                    OffPoint = flight.ArrivalCode,
                },
                BookingChannel = new BookingChannelKeyType
                {
                    ChannelType = GeneralConstants.BOOKING_CHANNEL_TYPE, 
                    Channel = channelConfiguration.ChannelCode, //IbsUtility.GetChannel(session), //GeneralConstants.BOOKING_CHANNEL_CODE,
                    Locale = GeneralConstants.LOCALE,
                    SessionId = !string.IsNullOrEmpty(session.AgencyInfo.SessionId) ? session.AgencyInfo.SessionId : null
                },
                PointOfSale = IbsUtility.PointOfSaleType(flight.DepartureCode, session.CurrentFlow.PNR.Currency).Location.CountryCode.Value,
                FlightIdentifier = new FlightIdentifierType
                {
                    fltNumber = flightSegment.FlightNumber,                
                    airlineCode = GeneralConstants.CARRIERCODE,
                    flightDate = new DateOnlyType
                    {
                        Date = flight.FlightDate.Value,
                    },
                    fltSuffix = "*",
                    carrierCode = GeneralConstants.CARRIERCODE
                },
                BundleAncillaries = new BundleAncillaries[]
                {
                    new BundleAncillaries()
                    {
                        Bundle = flightSelectedBundleCode
                    }
                },
                isSeatFeeRequired = true,
                isSeatFeeRequiredSpecified = true,
                Itinerary = segmentInfos.ToArray(),
                PaxCountDetails = passengerMap.ToArray()                
            };
        }

        public static GetFlightSeatsResponse ShowSeatsResponseMap(this showSeatMapResponse response)
        {
            ShowSeatMapRS showSeatMapRS = response.ShowSeatMapRS;
            
            var result = new GetFlightSeatsResponse
            {
                CabinLayout = showSeatMapRS.GetCabinLayout(),
                Seats = showSeatMapRS.GetSeats()
                .OrderBy(seat => int.Parse(seat.SeatNumber))
                .ThenBy(seat => seat.Column)
                .ToList()
            };

            result.SeatTypePrices = result.Seats.GroupBy(g => g.SeatType).ToDictionary(k => k.Key, v => v.Min(m => m.Price));

            return result;
        }

        public static FlightSegmentDetailsType TmobIdToSegmentInfo(SessionCache session,string tmobId)
        {
            AirAvailabilityRS airAvailabilityRS = session.CurrentFlow.GetIbsData<AirAvailabilityRS>(IbsDataTypeEnum.ShopAirResponse.ToString());
            var flight = session.CurrentFlow.PNR.Flights.FirstOrDefault(f => f.TmobId == tmobId);
            var segmentAvailability = airAvailabilityRS.GetSegmentAvailability(IbsUtility.ParseFullId(IbsUtility.ParseFullId(flight.Id)));
            var segmentInfo = airAvailabilityRS.GetSegmentInfoByFlightId(IbsUtility.ParseFullId(flight.Id));
            return segmentInfo.MapSegmentInfoTypeToFlightSegment(segmentAvailability);
        }
        
        public static FlightPortServiceReference.FlightSegmentDetailsType MapSegmentInfoTypeToFlightSegment(this SegmentInfoType segmentInfoType, SegmentAvailabilityType segmentAvailability)
        {
            var flightSegmentDetailMap = new FlightPortServiceReference.FlightSegmentDetailsType
            {
                SegmentId = segmentInfoType.SegmentIndex.ToString(),
                carrierCode = GeneralConstants.CARRIERCODE,
                fltNumber = segmentInfoType.FlightIdentifierInfo.FlightNumber.ToString(),
                fltSuffix = "*",
                flightDate = new FlightPortServiceReference.DateOnlyDetailsType
                {
                    Date = segmentInfoType.FlightIdentifierInfo.FlightDate
                },
                boardPoint = segmentInfoType.DepartureInfo.AirportCode,
                offPoint = segmentInfoType.ArrivalInfo.AirportCode,
                CabinClass = segmentAvailability.CabinClass,
                FareClass = segmentAvailability.BookingClass,
                FlightSegmentGroupID = segmentInfoType.SegmentIndex.ToString(),
                scheduledDepartureDateTime = segmentInfoType.DepartureInfo.DateTime,
                scheduledArrivalTime = segmentInfoType.ArrivalInfo.DateTime,
                DepartureTimeZone = segmentInfoType.DepartureInfo.TimeZoneOffset,
                ArrivalTimeZone = segmentInfoType.ArrivalInfo.TimeZoneOffset,
                stops = segmentInfoType.Stops,
                arrivalDayChange = segmentInfoType.DayChange,
                isThroughFlight = segmentInfoType.Stops > 0,
                isThroughFlightSpecified = segmentInfoType.Stops > 0,
            };
            return flightSegmentDetailMap;
        }

        public static CabinLayout GetCabinLayout(this ShowSeatMapRS response)
        {
            var deckDetails = response.GetDeckDetails();
            var cabinDetails = response.GetCabinDetails();
            
            if (cabinDetails == null || deckDetails == null)
            {
                return null;
            }
            //TODO:IBS'te fist ve last row ters geliyor M.A
            var rows = new Row
            {
                First = cabinDetails.EndRow,
                Last = cabinDetails.StartRow
            };

            var internalSeatConfiguration = cabinDetails.CompartmentDetails.FirstOrDefault()?.InternalSeatConfiguration;
            var seatConfiguration = cabinDetails.CompartmentDetails.FirstOrDefault()?.SeatConfiguration;
            var columns = new List<string>();

            if (!string.IsNullOrEmpty(internalSeatConfiguration))
            {
                columns = internalSeatConfiguration
                    .Split('-')
                    .ToList();
            }

            var exitRowPositions = response.GetExitRowPositions();

            return new CabinLayout
            {
                Columns = columns,
                Rows = rows,
                ExitRowPosition = exitRowPositions,
                SeatConfiguration = seatConfiguration,
                InternalSeatConfiguration = internalSeatConfiguration
            };
        }

        public static List<SeatDTO> GetSeats(this ShowSeatMapRS response)
        {
            var cabinDetails = response.GetCabinDetails();
            var compartmentDetails = response.GetCompartmentDetails();

            List<SeatDTO> seats = new();

            foreach (var compartment in compartmentDetails)
            {
                seats.AddRange(compartment.SeatDetails.Select(seat => response.CreateSeatDTO(seat)));
            }           

            return seats;
        }

        private static SeatDTO CreateSeatDTO(this ShowSeatMapRS response, SeatDetailsType seat)
        {
            var fee = seat.SeatAssignMentFee != null ? seat.SeatAssignMentFee.FirstOrDefault() : default;
            var amount = fee != null ? (decimal)fee.Amount : 0m;
            var currency = fee?.currency ?? string.Empty;
            var seatType = response.GetSeatType(seat);
           
            return new SeatDTO
            {
                SeatNumber = seat.ExternalRowNumber,
                Column = seat.ExternalColumnName,
                IsEnabled = (seat.ControlAttribute == NativeAPISeatConstants.SEAT_AVAILABILITY_ATTRIBUTE || seat.ControlAttribute == NativeAPISeatConstants.RESTRICTED),
                SeatType = seatType,
                PriceDescription = $"+ {amount} {currency}",
                Price = amount,
                AllocatedPassengerType = seat.AllocatedPassengerType
            };
        }

        public static CabinDetailsType GetCabinDetails(this ShowSeatMapRS response)
        {
            var deckDetails = response.SeatMapInformation.SeatMapdetails.DeckDetails.FirstOrDefault();
            return deckDetails?.CabinDetails.FirstOrDefault();
        }

        public static DeckDetailsType GetDeckDetails(this ShowSeatMapRS response)
        {
            return response.SeatMapInformation?.SeatMapdetails?.DeckDetails.FirstOrDefault();
        }

        public static List<CompartmentDetailsType> GetCompartmentDetails(this ShowSeatMapRS response) 
        {
            var compartmentDetails = response.SeatMapInformation?
                                    .SeatMapdetails?
                                    .DeckDetails.FirstOrDefault()?
                                    .CabinDetails?.FirstOrDefault()?.CompartmentDetails;

            return compartmentDetails?.ToList() ?? new ();
        }

        public static SeatTypeEnum GetSeatType(this ShowSeatMapRS response, SeatDetailsType seat)
        {
            if (seat.AttachedSsr != null)
            {
                if (seat.AttachedSsr.Contains("AXLST"))
                {
                    return SeatTypeEnum.Xleg;
                }
                else if (seat.AttachedSsr.Contains("AFRST"))
                {
                    return SeatTypeEnum.FrontSection;
                }
                else if (seat.AttachedSsr.Contains("NRST"))
                {
                    return SeatTypeEnum.Normal;
                }
                else if (seat.AttachedSsr.Contains("EXIT"))
                {
                    return SeatTypeEnum.Exit;
                }
            }

            return seat.FacilityAttribute != null && seat.FacilityAttribute.Contains(NativeAPISeatConstants.XLEG_ATTRIBUTE) ? SeatTypeEnum.Xleg : SeatTypeEnum.Normal;
        }

        public static List<Row> GetExitRowPositions(this ShowSeatMapRS response)
        {
            var compartmentDetails = response.GetCompartmentDetails();

            return compartmentDetails
                    .SelectMany(compartment => compartment.SeatDetails)
                    .Where(seatDetail => seatDetail.LocationAttribute != null &&
                                         seatDetail.LocationAttribute.Any(attribute => attribute == NativeAPISeatConstants.EXIT_ROW_ATTRIBUTE))
                    .Select(seat => new Row { First = seat.ExternalRowNumber, Last = seat.ExternalRowNumber })
                    .GroupBy(row => new { row.First, row.Last })
                    .Select(group => new Row { First = group.Key.First, Last = group.Key.Last })
                    .ToList();
        }
    }
}
