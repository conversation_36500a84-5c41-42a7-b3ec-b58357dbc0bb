using System.Xml.Linq;
using Ubimecs.IBS.Models;
using Ubimecs.IBS.Models.Fare;
using Ubimecs.IBS.Utility;
using Ubimecs.Infrastructure.Caching;
using Ubimecs.Infrastructure.Models.Common;
using Ubimecs.Infrastructure.Models.DTO.Flight;
using Ubimecs.Infrastructure.Models.DTO.Meal;
using Ubimecs.Infrastructure.Models.DTO.Price;
using Ubimecs.Infrastructure.Models.DTO.Service;
using Ubimecs.Infrastructure.Models.Request;
using Ubimecs.Infrastructure.Models.Response;
using static Ubimecs.IBS.Models.OrderReshopRQ;
using static Ubimecs.Infrastructure.Models.Response.BasePriceResponse;

namespace Ubimecs.IBS.Mappers.RequestMappers
{
    public static class OrderReshopMappers
    {

        public static OrderReshopRQ Map(SessionCache session, RebookingSearchFlightRequest request = null, bool isbundleTypeChanging = false)
        {
            List<ServiceDefinitionType> services = new List<ServiceDefinitionType>();
            List<OrderReshopRQDataListsBaggageAllowance> baggageAllowances = new List<OrderReshopRQDataListsBaggageAllowance>();
            session.CurrentFlow.ManageBookingServiceData = new List<Ubimecs.Infrastructure.Models.Flow.ManageBookingServiceData>();

            var latestReshopResponse = session.CurrentFlow.GetIbsData<OrderReshopRS>(IbsDataTypeEnum.LatestReshopResponse);
            var searchFlightResponse = session.CurrentFlow.GetIbsData<OrderReshopRS>(IbsDataTypeEnum.LatestRebookingSearchFlightResponse);

            var orderViewRS = session.CurrentFlow.GetIbsData<OrderViewRS>(IbsDataTypeEnum.OrderRetrieve);

            OrderReshopRQDataLists dataLists = new OrderReshopRQDataLists();


            string firstDepartureCode = null;
            string arrivalCode = null;
            object QueryObject = null;
            if (request != null)
            {
                if (string.IsNullOrEmpty(request.OldFlightTmobId))
                {
                    var firstFlight = session.CurrentFlow.PNR.Flights.First();

                    if (IbsUtility.GetIsInternationalFlight(firstFlight.DepartureCode, firstFlight.ArrivalCode) != IbsUtility.GetIsInternationalFlight(request.DepartureCode, request.ArrivalCode))
                    {
                        if (IbsUtility.GetIsInternationalFlight(firstFlight.DepartureCode, firstFlight.ArrivalCode))
                        {
                            throw new TmobException(CachedData.GetCMS(session.Language, "no_valid_offers_pop_up_text_int_pnr"));
                        }
                        else
                        {
                            throw new TmobException(CachedData.GetCMS(session.Language, "no_valid_offers_pop_up_text_dom_pnr"));

                        }
                    }


                    firstDepartureCode = request.DepartureCode;
                    QueryObject = GetSearchFlightQuery(session, request, request.DepartureCode, request.ArrivalCode);
                }
                else
                {
                    var currentFlight = session.CurrentFlow.PNR?.Flights?.FirstOrDefault(f => f.TmobId == request.OldFlightTmobId);
                    firstDepartureCode = currentFlight?.DepartureCode;
                    arrivalCode = currentFlight?.ArrivalCode;
                    QueryObject = GetSearchFlightQuery(session, request, firstDepartureCode, arrivalCode);
                }
            }
            else
            {
                List<OrderReshopRQQueryReshopOrderServicingAddQualifier> addQualifiers = new List<OrderReshopRQQueryReshopOrderServicingAddQualifier>();
                List<OrderReshopRQQueryReshopOrderServicingOrderItem> deleteItemList = new List<OrderReshopRQQueryReshopOrderServicingOrderItem>();
                OrderReshopRQQueryReshopOrderServicingAddFlightQuery tourOperatorFlightQuery = new OrderReshopRQQueryReshopOrderServicingAddFlightQuery();

                if (session.CurrentFlow.RebookingType == Ubimecs.Infrastructure.Models.Flow.RebookingTypeEnum.ChangeFlight)
                {
                    addQualifiers.AddRange(GetFlightAddQuery(session, searchFlightResponse));
                    deleteItemList.AddRange(GetFlightDeleteItems(session));
                    deleteItemList.AddRange(GetServiceDeleteItems(session));
                    GetPaymentQualifierAddQuery(session, ref addQualifiers);

                    LoadBundles(session, ref services, ref addQualifiers, isbundleTypeChanging);
                    LoadSeats(session, ref addQualifiers);
                    LoadBaggages(session, ref services, ref addQualifiers, ref baggageAllowances);
                    LoadSportEquipments(session, ref services, ref addQualifiers);
                    LoadCorona(session, ref services, ref addQualifiers);
                    LoadMeals(session, ref services, ref addQualifiers);
                    LoadIfeServices(session, ref services, ref addQualifiers);
                    LoadGolfBundles(session, ref services, ref addQualifiers);
                    dataLists = new OrderReshopRQDataLists()
                    {
                        FareList = GetFares(session),
                        FlightSegmentList = GetFlightSegments(session, searchFlightResponse, null),
                        PassengerList = orderViewRS.GetDataList()?.PassengerList?.ToArray(),
                        ServiceDefinitionList = services.Count > 0 && services != null ? services.ToArray() : null,
                        BaggageAllowanceList = baggageAllowances.Count > 0 ? baggageAllowances.ToArray() : null
                    };
                }
                else if (session.CurrentFlow.RebookingType == Ubimecs.Infrastructure.Models.Flow.RebookingTypeEnum.CancelFlight)
                {
                    deleteItemList.AddRange(GetFlightDeleteItems(session));

                    GetPaymentQualifierAddQuery(session, ref addQualifiers);

                    dataLists = new OrderReshopRQDataLists()
                    {
                        PassengerList = orderViewRS.GetDataList()?.PassengerList?.ToArray(),
                    };
                }
                else if (session.CurrentFlow.RebookingType == Ubimecs.Infrastructure.Models.Flow.RebookingTypeEnum.CancelPNR)
                {
                    GetPaymentQualifierAddQuery(session, ref addQualifiers);

                    foreach (var item in orderViewRS.GetOrders())
                    {
                        deleteItemList.Add(new OrderReshopRQQueryReshopOrderServicingOrderItem
                        {
                            OrderItemID = item.OrderItemID
                        });
                    }

                    dataLists = new OrderReshopRQDataLists()
                    {
                        PassengerList = orderViewRS.GetDataList()?.PassengerList?.ToArray(),
                    };
                }
                else if (session.CurrentFlow.RebookingType == Ubimecs.Infrastructure.Models.Flow.RebookingTypeEnum.AddExtras)
                {
                    deleteItemList.AddRange(GetServiceDeleteItems(session));
                    GetPaymentQualifierAddQuery(session, ref addQualifiers);

                    LoadSeats(session, ref addQualifiers);
                    LoadBaggages(session, ref services, ref addQualifiers, ref baggageAllowances);
                    LoadSportEquipments(session, ref services, ref addQualifiers);
                    LoadCorona(session, ref services, ref addQualifiers);
                    LoadMeals(session, ref services, ref addQualifiers);
                    LoadIfeServices(session, ref services, ref addQualifiers);
                    LoadGolfBundles(session, ref services, ref addQualifiers);

                    dataLists = new OrderReshopRQDataLists()
                    {
                        FlightSegmentList = GetFlightSegments(session, searchFlightResponse, orderViewRS),
                        PassengerList = orderViewRS.GetDataList()?.PassengerList?.ToArray(),
                        ServiceDefinitionList = services.Count > 0 && services != null ? services.ToArray() : null,
                        BaggageAllowanceList = baggageAllowances.Count > 0 ? baggageAllowances.ToArray() : null

                    };
                }
                else if (session.CurrentFlow.RebookingType == Ubimecs.Infrastructure.Models.Flow.RebookingTypeEnum.AddFlight)
                {
                    addQualifiers.AddRange(GetFlightAddQuery(session, searchFlightResponse));
                    GetPaymentQualifierAddQuery(session, ref addQualifiers);

                    LoadBundles(session, ref services, ref addQualifiers, isbundleTypeChanging);
                    LoadSeats(session, ref addQualifiers);
                    LoadBaggages(session, ref services, ref addQualifiers, ref baggageAllowances);
                    LoadSportEquipments(session, ref services, ref addQualifiers);
                    LoadCorona(session, ref services, ref addQualifiers);
                    LoadMeals(session, ref services, ref addQualifiers);
                    LoadIfeServices(session, ref services, ref addQualifiers);
                    LoadGolfBundles(session, ref services, ref addQualifiers);

                    dataLists = new OrderReshopRQDataLists()
                    {
                        FareList = GetFares(session),
                        FlightSegmentList = GetFlightSegments(session, searchFlightResponse, orderViewRS),
                        PassengerList = orderViewRS.GetDataList()?.PassengerList?.ToArray(),
                        ServiceDefinitionList = services.Count > 0 && services != null ? services.ToArray() : null,
                        BaggageAllowanceList = baggageAllowances.Count > 0 ? baggageAllowances.ToArray() : null
                    };
                }
                else if (session.CurrentFlow.RebookingType == Ubimecs.Infrastructure.Models.Flow.RebookingTypeEnum.UpgradeBundle)
                {
                    deleteItemList.AddRange(GetServiceDeleteItems(session));
                    GetPaymentQualifierAddQuery(session, ref addQualifiers);

                    LoadBundles(session, ref services, ref addQualifiers, isbundleTypeChanging);
                    LoadSeats(session, ref addQualifiers);
                    LoadBaggages(session, ref services, ref addQualifiers, ref baggageAllowances);
                    LoadSportEquipments(session, ref services, ref addQualifiers);
                    LoadCorona(session, ref services, ref addQualifiers);
                    LoadMeals(session, ref services, ref addQualifiers);
                    LoadIfeServices(session, ref services, ref addQualifiers);
                    LoadGolfBundles(session, ref services, ref addQualifiers);

                    dataLists = new OrderReshopRQDataLists()
                    {
                        FlightSegmentList = GetFlightSegments(session, searchFlightResponse, orderViewRS),
                        PassengerList = orderViewRS.GetDataList()?.PassengerList?.ToArray(),
                        ServiceDefinitionList = services.Count > 0 ? services.ToArray() : null,
                        BaggageAllowanceList = baggageAllowances.Count > 0 ? baggageAllowances.ToArray() : null

                    };
                }
                else if (session.CurrentFlow.RebookingType == Ubimecs.Infrastructure.Models.Flow.RebookingTypeEnum.OptionalPNR)
                {
                    addQualifiers.Add(GetAddQualifierForOptionalPNR(session, orderViewRS));
                    GetPaymentQualifierAddQuery(session, ref addQualifiers);

                    dataLists = new OrderReshopRQDataLists()
                    {
                        PassengerList = orderViewRS.GetDataList()?.PassengerList?.ToArray()
                    };
                }
                else if (session.CurrentFlow.RebookingType == Ubimecs.Infrastructure.Models.Flow.RebookingTypeEnum.TourOperator)
                {
                    //tourOperatorFlightQuery.Item = GetFlightAddForTourOperator(session, orderViewRS);

                    LoadBundles(session, ref services, ref addQualifiers, isbundleTypeChanging);
                    LoadSeats(session, ref addQualifiers);
                    LoadBaggages(session, ref services, ref addQualifiers, ref baggageAllowances);
                    LoadSportEquipments(session, ref services, ref addQualifiers);
                    LoadCorona(session, ref services, ref addQualifiers);
                    LoadMeals(session, ref services, ref addQualifiers);
                    LoadIfeServices(session, ref services, ref addQualifiers);
                    LoadGolfBundles(session, ref services, ref addQualifiers);

                    dataLists = new OrderReshopRQDataLists()
                    {
                        //FareList = GetFares(session),
                        PassengerList = orderViewRS.GetDataList()?.PassengerList?.ToArray(),
                        BaggageAllowanceList = baggageAllowances.Count > 0 ? baggageAllowances.ToArray() : null,
                        FlightSegmentList = orderViewRS.GetDataList()?.FlightSegmentList?.ToArray(),
                        ServiceDefinitionList = services.Count > 0 && services != null ? services.ToArray() : null
                    };
                }

                services = services.GroupBy(g => g.ServiceDefinitionID).Select(s => s.FirstOrDefault()).ToList();

                if (addQualifiers.Count > 0 || deleteItemList.Count > 0)
                {
                    QueryObject = new OrderReshopRQQueryReshop()
                    {
                        Item = new OrderReshopRQQueryReshopOrderServicing()
                        {
                            Add = new OrderReshopRQQueryReshopOrderServicingAdd()
                            {
                                FlightQuery = tourOperatorFlightQuery.Item != null ? tourOperatorFlightQuery : null,
                                Qualifier = addQualifiers?.Count > 0 ? addQualifiers.ToArray() : null
                            },
                            Delete = deleteItemList?.Count > 0 ? deleteItemList.ToArray() : null
                        }
                    };
                }

            }

            if (dataLists.ServiceDefinitionList != null)
            {
                var serviceCategories = CachedData.ServiceCodeCategories;

                foreach (var item in dataLists.ServiceDefinitionList)
                {
                    if (IbsUtility.GetCategory(serviceCategories, item.Encoding.Code.Value) == ServiceCategoryEnum.BAGGAGE)
                    {

                        //TODO: BaggageAllowence paramteresi Cevat'ın dediğine göre gerekliymir.
                        //Bu yüzden bu kodun kaldırılması gerekti.
                        //Eğer kod tamameiyle doğru çalışırsa bu blok komple kaldırılması gerekecek.
                        //item.Item = null;
                    }

                }
            }
            var OtherMetadata = new OrderReshopReqMetadataTypeOtherMetadata();
            if (session.CurrentFlow.PNR.PaymentInfo != null && session.CurrentFlow.PNR.PaymentInfo.CardNumber != null)
            {
                OtherMetadata = GetOtherMetadata(session);
            }

            var ibsRequest = new OrderReshopRQ
            {
                PointOfSale = IbsUtility.PointOfSaleType(firstDepartureCode, request?.CurrencyCode ?? session.CurrentFlow.PNR.Currency),
                Document = IbsUtility.GetDocument(),
                Party = IbsUtility.GetPartiesType(),

                Query = new OrderReshopRQQuery()
                {
                    OrderID = session.CurrentFlow.PNR.Number,
                    Item = QueryObject
                },
                DataLists = dataLists,
                Metadata = OtherMetadata.Item != null ? new OrderReshopRQMetadata
                {
                    Other = new OrderReshopReqMetadataTypeOtherMetadata[] { OtherMetadata }
                } : null,
                EchoToken = null,
                TimeStampSpecified = false,
                Version = GeneralConstants.BOOKING_VERSION,
                TransactionIdentifier = null,
                SequenceNmbr = null,
                TransactionStatusCodeSpecified = false,
                RetransmissionIndicator = false,
                RetransmissionIndicatorSpecified = false,
                CorrelationID = null,
                AsynchronousAllowedInd = false,
                AsynchronousAllowedIndSpecified = false,
            };

            session.CurrentFlow.SaveIbsData(ibsRequest, IbsDataTypeEnum.LatestReshopRequest);

            return ibsRequest;
        }
        public static BookingSearchFlightResponse MapToSearchFlightResponse(OrderReshopRS response, SessionCache session)
        {
            session.CurrentFlow.SaveIbsData(response, IbsDataTypeEnum.LatestReshopResponse);
            BookingSearchFlightResponse result = new BookingSearchFlightResponse();
            result.Currency = session.CurrentFlow.PNR.Currency;


            #region Flights

            #region Flex Types,Headers & Messages
            List<string> flexMessages = CachedData.GetCMS(session.Language, "flex_types_messages").Split(';').ToList();
            List<string> flexTypes = CachedData.GetCMS(session.Language, "flex_types").Split(';').ToList();
            #endregion

            int pricedPasengerCount = response.GetPassengers().Count(c => c.PTC != PassengerTypeEnum.INF.ToString());

            var odItem = response?.GetOriginDestinations()?.FirstOrDefault();

            if (odItem != null)
            {
                result.Flights.Add(response.GetFlightInfoDTO(odItem, pricedPasengerCount, session.Language, flexTypes, flexMessages));
            }
            else
            {
                result.Flights.Add(new SearchFlightInfoDTO
                {
                    DepartureCode = odItem.DepartureCode.Value,
                    ArrivalCode = odItem.ArrivalCode.Value,
                });
            }

            #endregion

            return result;
        }
        public static BasePriceResponse Map(this OrderReshopRS response, SessionCache session)
        {
            session.CurrentFlow.SaveIbsData(response, IbsDataTypeEnum.LatestReshopResponse);
            var result = new BasePriceResponse();
            result.Currency = session.CurrentFlow.PNR.Currency;
            result.Flights = new List<PriceFlightDTO>();
            var languageId = (int)session.Language;


            //if (session.CurrentFlow.RebookingType == Model.Flow.RebookingTypeEnum.CancelPNR)
            //{
            //    //TODO: Cancel Flight ile alakalı olarak düzenleme yapılması gerekiyor.
            //    return result;
            //}

            if (response.GetReponseObject() == null)
            {
                var listOfFlights = new List<PriceFlightDTO>();
                if (session.CurrentFlow.RebookingType != Ubimecs.Infrastructure.Models.Flow.RebookingTypeEnum.ChangeFlight)
                {
                    if (session.CurrentFlow.RebookingType == Ubimecs.Infrastructure.Models.Flow.RebookingTypeEnum.TourOperator)
                    {
                        foreach (var flight in session.CurrentFlow.PNR.Flights.Where(t => t.State == OfferRebookingStateEnum.ServiceChanging))
                        {
                            var priceFlightInfo = new PriceFlightDTO
                            {
                                Prices = new List<BasePriceDTO>(),
                                Id = flight.SegmentRefs,
                                Segments = flight.Segments,//segmentItems.Select(s => s.GetSegmentDTO((LanguageEnum)languageId)).ToList(),
                                                           //TotalPrice = flight.p response.GetTotalPrice(segment),
                                BasePrice = flight?.BasePrice ?? decimal.Zero,
                                FormattedFirstDepartureDate = IbsUtility.FormatDate(session.Language, flight.Segments.FirstOrDefault().DepartureDate),
                                Title = flight?.Title ?? string.Empty,
                                JourneyTime = flight?.JourneyTime ?? TimeSpan.Zero

                            };
                            listOfFlights.Add(priceFlightInfo);
                        }
                    }
                    else
                    {
                        foreach (var flight in session.CurrentFlow.PNR.Flights.Where(t => t.State != OfferRebookingStateEnum.NotChanged))
                        {
                            var priceFlightInfo = new PriceFlightDTO
                            {
                                Prices = new List<BasePriceDTO>(),
                                Id = flight.SegmentRefs,
                                Segments = flight.Segments,//segmentItems.Select(s => s.GetSegmentDTO((LanguageEnum)languageId)).ToList(),
                                                           //TotalPrice = flight.p response.GetTotalPrice(segment),
                                BasePrice = flight?.BasePrice ?? decimal.Zero,
                                JourneyTime = flight?.JourneyTime ?? TimeSpan.Zero,
                                Title = flight?.Title ?? string.Empty
                            };
                            listOfFlights.Add(priceFlightInfo);
                        }
                    }
                }
                else
                {
                    foreach (var flight in session.CurrentFlow.PNR.Flights)
                    {
                        var priceFlightInfo = new PriceFlightDTO
                        {
                            Prices = new List<BasePriceDTO>(),
                            Id = flight.SegmentRefs,
                            Segments = flight.Segments,//segmentItems.Select(s => s.GetSegmentDTO((LanguageEnum)languageId)).ToList(),
                                                       //TotalPrice = flight.p response.GetTotalPrice(segment),
                            BasePrice = flight?.BasePrice ?? decimal.Zero,
                            Title = flight?.Title ?? string.Empty,
                            JourneyTime = flight?.JourneyTime ?? TimeSpan.Zero
                        };
                        listOfFlights.Add(priceFlightInfo);
                    }
                }

                result.Flights = listOfFlights;
                if (session.CurrentFlow.RebookingType == Ubimecs.Infrastructure.Models.Flow.RebookingTypeEnum.AddExtras || session.CurrentFlow.RebookingType == Ubimecs.Infrastructure.Models.Flow.RebookingTypeEnum.UpgradeBundle || session.CurrentFlow.RebookingType == Ubimecs.Infrastructure.Models.Flow.RebookingTypeEnum.TourOperator)
                {
                    var services = session.CurrentFlow.PnrInfoResponse.Services;

                    if ((result.Services.Bundles?.Count ?? 0) == 0) result.Services.Bundles = services.Bundles;
                    if ((result.Services.FlexServices?.Count ?? 0) == 0) result.Services.FlexServices = services.FlexServices;
                    if ((result.Services.GolfBundles?.Count ?? 0) == 0) result.Services.GolfBundles = services.GolfBundles;
                    if ((result.Services.IfeServices?.Count ?? 0) == 0) result.Services.IfeServices = services.IfeServices;
                    if ((result.Services.Others?.Count ?? 0) == 0) result.Services.Others = services.Others;

                    foreach (var item in services.Meals)
                    {
                        if (!result.Services.Meals.Any(a => IbsUtility.CheckIdsEqual(a.PassengerIds, item.PassengerIds) && IbsUtility.CheckIdsEqual(a.SegmentIds, item.SegmentIds)))
                        {
                            result.Services.Meals.Add(item);
                        }
                    }

                    foreach (var item in services.CoronaServices)
                    {
                        if (!result.Services.CoronaServices.Any(a => IbsUtility.CheckIdsEqual(a.PassengerIds, item.PassengerIds) && IbsUtility.CheckIdsEqual(a.SegmentIds, item.SegmentIds)))
                        {
                            result.Services.CoronaServices.Add(item);
                        }
                    }

                    foreach (var item in services.Baggages)
                    {
                        if (!result.Services.Baggages.Any(a => IbsUtility.CheckIdsEqual(a.PassengerIds, item.PassengerIds) && IbsUtility.CheckIdsEqual(a.SegmentIds, item.SegmentIds)))
                        {
                            result.Services.Baggages.Add(item);
                        }
                    }

                    foreach (var item in services.Seats)
                    {
                        if (!result.Services.Seats.Any(a => IbsUtility.CheckIdsEqual(a.PassengerIds, item.PassengerIds) && IbsUtility.CheckIdsEqual(a.SegmentIds, item.SegmentIds)) && session.CurrentFlow.RebookingType != Ubimecs.Infrastructure.Models.Flow.RebookingTypeEnum.UpgradeBundle)
                        {
                            result.Services.Seats.Add(item);
                        }
                    }

                    foreach (var item in services.SportEquipments)
                    {
                        if (!result.Services.SportEquipments.Any(a => IbsUtility.CheckIdsEqual(a.PassengerIds, item.PassengerIds) && IbsUtility.CheckIdsEqual(a.SegmentIds, item.SegmentIds) && a.Id == item.Id))
                        {
                            result.Services.SportEquipments.Add(item);
                        }
                    }
                    foreach (var item in services.IfeServices)
                    {
                        if (!result.Services.IfeServices.Any(a => IbsUtility.CheckIdsEqual(a.PassengerIds, item.PassengerIds) && IbsUtility.CheckIdsEqual(a.SegmentIds, item.SegmentIds) && a.Id == item.Id))
                        {
                            result.Services.IfeServices.Add(item);
                        }
                    }

                }
                if(session.CurrentFlow.PNR.Flights.FirstOrDefault()?.SelectedBundle == ServiceCategoryEnum.SUN_LIGHT_BUNDLE)
                {
                    result.Flights.ForEach((flight) =>
                    {
                        flight.SelectedBundleCategoryId = ServiceCategoryEnum.SUN_LIGHT_BUNDLE;
                    });
                }
                return result;
            }

            var offers = response.GetOffers();

            var segments = response.GetDataList()?.FlightList?.Select(s => s.SegmentReferences.Value)?.ToList() ?? new List<string>();

            foreach (var segment in segments)
            {
                var flight = session.CurrentFlow.PNR.Flights.FirstOrDefault(f => f.SegmentRefs == segment) ?? session.CurrentFlow.PNR.Flights.FirstOrDefault(f => f.State == OfferRebookingStateEnum.Added);

                //Add Extras & Upgrade ... Cases
                if (session.CurrentFlow.RebookingType != Ubimecs.Infrastructure.Models.Flow.RebookingTypeEnum.ChangeFlight)
                {
                    if ((flight.State != OfferRebookingStateEnum.NotChanged) || (flight.State == OfferRebookingStateEnum.ServiceChanging && session.CurrentFlow.RebookingType == Ubimecs.Infrastructure.Models.Flow.RebookingTypeEnum.TourOperator))
                    {
                        for (int i = 0; i < IbsUtility.SplitIds(segment).Length; i++)
                        {
                            flight.Segments[i].LocalId = IbsUtility.SplitIds(segment)[i];
                        }

                        flight.IncludedSeats = new List<SeatTypeEnum>();

                        var adultPricedCount = session.CurrentFlow.PNR.Passengers.Count(c => c.PassengerType == PassengerTypeEnum.ADT);
                        var childPricedCount = session.CurrentFlow.PNR.Passengers.Count(c => c.PassengerType == PassengerTypeEnum.CHD);
                        var infantPricedCount = session.CurrentFlow.PNR.Passengers.Count(c => c.PassengerType == PassengerTypeEnum.INF);
                        string currencyCode = string.Empty;

                        //Optional PNR olma durumunda Döviz kodu bu şekilde çekilmesi gerekiyor.
                        if (session.CurrentFlow.PNR.IsOptionalPNR)
                        {
                            currencyCode = response.GetCurrenyCodeOptionalPNR() ?? session.CurrentFlow.PNR.Currency;
                        }
                        else
                        {
                            currencyCode = response.GetCurrencyCode() ?? session.CurrentFlow.PNR.Currency;
                        }

                        var segmentItems = response.GetSegments(segment).OrderBy(o => o.Departure.Date).ThenBy(t => t.Departure.Time);


                        var priceFlightInfo = new PriceFlightDTO
                        {
                            Prices = new List<BasePriceDTO>(),
                            Id = segment,
                            Segments = segmentItems.Select(s => s.GetSegmentDTO((LanguageEnum)languageId,response.GetDataList()?.DisclosureList)).ToList(),
                            TotalPrice = response.GetTotalPrice(segment, session),
                            BasePrice = session.CurrentFlow.PNR.Flights.FirstOrDefault(f => f.SegmentRefs == segment)?.BasePrice ?? 0,
                        };
                        for (int i = 0; i < priceFlightInfo.Segments.Count; i++)
                        {
                            priceFlightInfo.Segments[i].Id = flight.Segments[i].Id;
                            priceFlightInfo.Segments[i].LocalId = flight.Segments[i].LocalId;
                        }

                        priceFlightInfo.BasePriceDescription = priceFlightInfo.BasePrice + " " + session.CurrentFlow.PNR.Currency;
                        priceFlightInfo.TotalPriceDescription = priceFlightInfo.TotalPrice + " " + session.CurrentFlow.PNR.Currency;
                        priceFlightInfo.JourneyTime = GetFlightJourneyTime(session, priceFlightInfo.TmobId);

                        priceFlightInfo.FormattedFirstDepartureDate = IbsUtility.FormatDate(session.Language, priceFlightInfo.Segments.FirstOrDefault().DepartureDate);

                        foreach (var segmentDTO in priceFlightInfo.Segments)
                        {
                            //TODO: Kontrol edilmeli
                            segmentDTO.CarrierName = "SUNEXPRESS";

                            if (segmentDTO.AirlineId == "SM")
                            {
                                segmentDTO.CarrierName = "Air Cairo";

                            }
                        }




                        #region Prices

                        #region PassengerPrices

                        if (!session.CurrentFlow.PNR.IsOptionalPNR && adultPricedCount > 0 && response.GetPassengerTypeTotalPrice(PassengerTypeEnum.ADT.ToString(), segment) > 0)
                        {
                            priceFlightInfo.Prices.Add(new BasePriceDTO(
                                        $"{CachedData.GetCMS(languageId, "price_detail_fare")} ({adultPricedCount} {(adultPricedCount > 1 ? CachedData.GetCMS(languageId, "adult_plural") : CachedData.GetCMS(languageId, "adult"))})",
                                        response.GetPassengerTypeTotalPriceDescription(PassengerTypeEnum.ADT.ToString(), segment)));
                        }

                        if (!session.CurrentFlow.PNR.IsOptionalPNR && childPricedCount > 0 && response.GetPassengerTypeTotalPrice(PassengerTypeEnum.CHD.ToString(), segment) > 0)
                        {
                            priceFlightInfo.Prices.Add(new BasePriceDTO(
                                        $"{CachedData.GetCMS(languageId, "price_detail_fare")} ({childPricedCount} {(childPricedCount > 1 ? CachedData.GetCMS(languageId, "child_plural") : CachedData.GetCMS(languageId, "child"))})",
                                        response.GetPassengerTypeTotalPriceDescription(PassengerTypeEnum.CHD.ToString(), segment)));
                        }

                        if (!session.CurrentFlow.PNR.IsOptionalPNR && infantPricedCount > 0 && response.GetPassengerTypeTotalPrice(PassengerTypeEnum.INF.ToString(), segment) > 0)
                        {
                            priceFlightInfo.Prices.Add(new BasePriceDTO(
                                        $"{CachedData.GetCMS(languageId, "price_detail_fare")} ({infantPricedCount} {(infantPricedCount > 1 ? CachedData.GetCMS(languageId, "infant_plural") : CachedData.GetCMS(languageId, "infant"))})",
                                        response.GetPassengerTypeTotalPriceDescription(PassengerTypeEnum.INF.ToString(), segment)));
                        }

                        #endregion

                        #region Tax Prices
                        if (!session.CurrentFlow.PNR.IsOptionalPNR)
                        {
                            var taxes = response.GetTotalTaxPrice(segment);
                            var totalSurcaharge = response.GetTotalSurCharge(segment);

                            foreach (var item in taxes)
                            {
                                priceFlightInfo.Prices.Add(new BasePriceDTO(CachedData.GetCMS(languageId, item.Key), $"{item.Value.ToString("0.00")} {currencyCode}"));
                            }

                            if (totalSurcaharge > 0)
                            {
                                priceFlightInfo.Prices.Add(new BasePriceDTO(CachedData.GetCMS(languageId, "price_detail_fuel_surcharge"), $"{totalSurcaharge.ToString("0.00")} {currencyCode}"));
                            }
                        }
                        #endregion


                        var services = new List<ServiceDefinitionType>();
                        if (!session.CurrentFlow.PNR.IsOptionalPNR)
                        {
                            services = response.GetServices(segment);
                        }

                        if (session.CurrentFlow.RebookingType == Ubimecs.Infrastructure.Models.Flow.RebookingTypeEnum.AddExtras || session.CurrentFlow.RebookingType == Ubimecs.Infrastructure.Models.Flow.RebookingTypeEnum.OptionalPNR)
                        {
                            var pnrInfoServices = session.CurrentFlow.PnrInfoResponse.Services;
                            var pnrInfoResponseServies = session.CurrentFlow.GetIbsData<OrderViewRS>(IbsDataTypeEnum.OrderRetrieve)?.GetDataList()?.ServiceDefinitionList;

                            if (pnrInfoResponseServies != null)
                            {
                                services.AddRange(pnrInfoResponseServies.Where(w => pnrInfoServices.Baggages.Where(ww => ww.SegmentIds.Contains(segment)).Select(s => s.Id).Contains(w.ServiceDefinitionID)));
                                services.AddRange(pnrInfoResponseServies.Where(w => pnrInfoServices.Bundles.Where(ww => ww.SegmentIds.Contains(segment)).Select(s => s.Id).Contains(w.ServiceDefinitionID)));
                                services.AddRange(pnrInfoResponseServies.Where(w => pnrInfoServices.CoronaServices.Where(ww => ww.SegmentIds.Contains(segment)).Select(s => s.Id).Contains(w.ServiceDefinitionID)));
                                services.AddRange(pnrInfoResponseServies.Where(w => pnrInfoServices.FlexServices.Where(ww => ww.SegmentIds.Contains(segment)).Select(s => s.Id).Contains(w.ServiceDefinitionID)));
                                services.AddRange(pnrInfoResponseServies.Where(w => pnrInfoServices.GolfBundles.Where(ww => ww.SegmentIds.Contains(segment)).Select(s => s.Id).Contains(w.ServiceDefinitionID)));
                                services.AddRange(pnrInfoResponseServies.Where(w => pnrInfoServices.Meals.Where(ww => ww.SegmentIds.Contains(segment)).Select(s => s.Id).Contains(w.ServiceDefinitionID)));
                                services.AddRange(pnrInfoResponseServies.Where(w => pnrInfoServices.Others.Where(ww => ww.SegmentIds.Contains(segment)).Select(s => s.Id).Contains(w.ServiceDefinitionID)));
                                services.AddRange(pnrInfoResponseServies.Where(w => pnrInfoServices.Seats.Where(ww => ww.SegmentIds.Contains(segment)).Select(s => s.Id).Contains(w.ServiceDefinitionID)));
                                services.AddRange(pnrInfoResponseServies.Where(w => pnrInfoServices.SportEquipments.Where(ww => ww.SegmentIds.Contains(segment)).Select(s => s.Id).Contains(w.ServiceDefinitionID)));
                            }
                        }


                        var serviceCategoryList = CachedData.ServiceCodeCategories;

                        var groupedServices = services.GroupBy(g => serviceCategoryList.ContainsKey(g.Encoding?.Code?.Value) ? serviceCategoryList[g.Encoding?.Code?.Value] : ServiceCategoryEnum.OTHERS)
                            .ToDictionary(k => k.Key, v => v.ToList());

                        var cmstext = "";
                        ServiceCategoryEnum segmentBundle = ServiceCategoryEnum.SUN_ECO_BUNDLE;

                        var responseOrderView = new OrderViewRS();
                        if (session.CurrentFlow.PNR.IsOptionalPNR)
                        {
                            responseOrderView = session.CurrentFlow.GetIbsData<OrderViewRS>(IbsDataTypeEnum.OrderRetrieve);
                        }
                        #region Additional Credit Card Fee For Installment
                        decimal creditCardFeePrice = 0;
                        if (groupedServices.ContainsKey(ServiceCategoryEnum.FEES))
                        {
                            creditCardFeePrice = response.GetTotalCreditCardFeePrice(segment);
                            cmstext = $"{CachedData.GetCMS(languageId, "credit_card_installment_fee")}";
                            priceFlightInfo.Prices.Add(new BasePriceDTO(cmstext, $"{creditCardFeePrice.ToString("0.00")} {currencyCode}"));
                        }
                        #endregion

                        #region Bundle Prices               

                        ServiceDefinitionTypeServiceBundle bundle = null;

                        decimal bundlePrice = 0;
                        if (groupedServices.ContainsKey(ServiceCategoryEnum.SUN_PREMIUM_BUNDLE))
                        {
                            bundlePrice = session.CurrentFlow.PNR.IsOptionalPNR ? responseOrderView.GetOrderRetrieveServiceListTotalPrice(groupedServices[ServiceCategoryEnum.SUN_PREMIUM_BUNDLE].Select(s => s.ServiceDefinitionID).ToArray())
                                                                                : response.GetServiceListTotalPrice(groupedServices[ServiceCategoryEnum.SUN_PREMIUM_BUNDLE].Select(s => s.ServiceDefinitionID).ToArray());
                            cmstext = $"{CachedData.GetCMS(languageId, "sun_fare_text")}: {CachedData.GetCMS(languageId, "sun_premium")}";

                            priceFlightInfo.Prices.Add(new BasePriceDTO(cmstext, bundlePrice > 0 ? $"{bundlePrice.ToString("0.00")} {currencyCode}" : CachedData.GetCMS(languageId, "sunfare_included"), true, "rgba(81, 186, 255, 1.0)"));
                            segmentBundle = ServiceCategoryEnum.SUN_PREMIUM_BUNDLE;
                            bundle = groupedServices[ServiceCategoryEnum.SUN_PREMIUM_BUNDLE].FirstOrDefault()?.Item as ServiceDefinitionTypeServiceBundle;
                        }
                        else if (groupedServices.ContainsKey(ServiceCategoryEnum.SUN_CLASSIC_BUNDLE))
                        {
                            bundlePrice = session.CurrentFlow.PNR.IsOptionalPNR ? responseOrderView.GetOrderRetrieveServiceListTotalPrice(groupedServices[ServiceCategoryEnum.SUN_CLASSIC_BUNDLE].Select(s => s.ServiceDefinitionID).ToArray())
                                                                                : response.GetServiceListTotalPrice(groupedServices[ServiceCategoryEnum.SUN_CLASSIC_BUNDLE].Select(s => s.ServiceDefinitionID).ToArray());
                            cmstext = $"{CachedData.GetCMS(languageId, "sun_fare_text")}: {CachedData.GetCMS(languageId, "sun_classic")}";

                            priceFlightInfo.Prices.Add(new BasePriceDTO(cmstext, bundlePrice > 0 ? $"{bundlePrice.ToString("0.00")} {currencyCode}" : CachedData.GetCMS(languageId, "sunfare_included"), true, "rgba(235, 105, 11, 1.0))"));
                            segmentBundle = ServiceCategoryEnum.SUN_CLASSIC_BUNDLE;
                            bundle = groupedServices[ServiceCategoryEnum.SUN_CLASSIC_BUNDLE].FirstOrDefault()?.Item as ServiceDefinitionTypeServiceBundle;

                        }
                        else
                        {
                            cmstext = $"{CachedData.GetCMS(languageId, "sun_fare_text")}: {CachedData.GetCMS(languageId, "sun_eco")}";

                            priceFlightInfo.Prices.Add(new BasePriceDTO(cmstext, CachedData.GetCMS(languageId, "sunfare_included"), true, "rgba(241, 172, 36, 1.0)"));

                            if (groupedServices.ContainsKey(ServiceCategoryEnum.GOLF_BUNDLE))
                            {
                                bundle = groupedServices[ServiceCategoryEnum.GOLF_BUNDLE].FirstOrDefault()?.Item as ServiceDefinitionTypeServiceBundle;
                            }
                        }
                        var selectedBundleEnum = session.CurrentFlow.PNR.Flights.FirstOrDefault(f => f.SegmentRefs == segment)?.SelectedBundle;
                        if (selectedBundleEnum == ServiceCategoryEnum.SUN_LIGHT_BUNDLE)
                        {
                            segmentBundle = selectedBundleEnum.Value;
                        }
                        priceFlightInfo.SelectedBundleCategoryId = segmentBundle;

                        if (bundle != null && flight != null)
                        {
                            flight.IncludedSeats = session.CurrentFlow.PNR.IsOptionalPNR ? responseOrderView.GetSeatTypeRetrieve(bundle.ServiceDefinitionRef.Select(s => s.Value).ToArray(), serviceCategoryList)
                                                                                         : response.GetSeatTypes(bundle.ServiceDefinitionRef.Select(s => s.Value).ToArray(), serviceCategoryList);
                            priceFlightInfo.IsSeatIncluded = flight.IncludedSeats?.Count > 0;
                        }

                        #endregion

                        #region Baggage

                        decimal baggagePrice = 0;
                        if (groupedServices.ContainsKey(ServiceCategoryEnum.BAGGAGE))
                        {
                            baggagePrice = session.CurrentFlow.PNR.IsOptionalPNR ? responseOrderView.GetOrderRetrieveServiceListTotalPrice(groupedServices[ServiceCategoryEnum.BAGGAGE].Select(s => s.ServiceDefinitionID).ToArray())
                                : response.GetBaggageServiceTotalPrice(groupedServices[ServiceCategoryEnum.BAGGAGE].Select(s => s.ServiceDefinitionID).ToArray());
                            //response.GetServiceListTotalPrice(groupedServices[ServiceCategoryEnum.BAGGAGE].Select(s => s.ServiceDefinitionID).ToArray());
                        }

                        cmstext = $"{CachedData.GetCMS(languageId, "luggage_price_detail_key")}";
                        priceFlightInfo.Prices.Add(new BasePriceDTO(cmstext, baggagePrice > 0 ? $"{baggagePrice.ToString("0.00")} {currencyCode}" : CachedData.GetCMS(languageId, "sunfare_included")));
                        #endregion

                        #region Meal
                        decimal mealPrice = 0;
                        if (groupedServices.ContainsKey(ServiceCategoryEnum.MEALS))
                        {
                            mealPrice = session.CurrentFlow.PNR.IsOptionalPNR ? responseOrderView.GetOrderRetrieveServiceListTotalPrice(groupedServices[ServiceCategoryEnum.MEALS].Select(s => s.ServiceDefinitionID).ToArray())
                                                            : response.GetServiceListTotalPrice(groupedServices[ServiceCategoryEnum.MEALS].Select(s => s.ServiceDefinitionID).ToArray());
                        }

                        cmstext = $"{CachedData.GetCMS(languageId, "meal")}";

                        if (mealPrice > 0)
                        {
                            priceFlightInfo.Prices.Add(new BasePriceDTO(cmstext, $"{mealPrice.ToString("0.00")} {currencyCode}"));
                        }
                        else if (segmentBundle != ServiceCategoryEnum.SUN_ECO_BUNDLE || (segmentBundle == ServiceCategoryEnum.SUN_ECO_BUNDLE && groupedServices.ContainsKey(ServiceCategoryEnum.GOLF_BUNDLE)))
                        {
                            priceFlightInfo.Prices.Add(new BasePriceDTO(cmstext, CachedData.GetCMS(languageId, "sunfare_included")));
                        }
                        #endregion

                        #region Seat
                        decimal seatPrice = 0;
                        if (groupedServices.ContainsKey(ServiceCategoryEnum.SEAT))
                        {
                            seatPrice = session.CurrentFlow.PNR.IsOptionalPNR ? responseOrderView.GetOrderRetrieveServiceListTotalPrice(groupedServices[ServiceCategoryEnum.SEAT].Select(s => s.ServiceDefinitionID).ToArray())
                                                                              : response.GetSeatServiceTotalPrice(groupedServices[ServiceCategoryEnum.SEAT].Select(s => s.ServiceDefinitionID).ToArray());
                            //: response.GetServiceListTotalPrice(groupedServices[ServiceCategoryEnum.SEAT].Select(s => s.ServiceDefinitionID).ToArray());
                        }

                        cmstext = session.CurrentFlow.PNR.Passengers.Count > 1 ? $"{CachedData.GetCMS(languageId, "seats")}" : $"{CachedData.GetCMS(languageId, "seat")}";

                        if (seatPrice > 0)
                        {
                            priceFlightInfo.Prices.Add(new BasePriceDTO(cmstext, $"{seatPrice.ToString("0.00")} {currencyCode}"));
                        }
                        else if (segmentBundle != ServiceCategoryEnum.SUN_ECO_BUNDLE || (segmentBundle == ServiceCategoryEnum.SUN_ECO_BUNDLE && groupedServices.ContainsKey(ServiceCategoryEnum.GOLF_BUNDLE)))
                        {
                            priceFlightInfo.Prices.Add(new BasePriceDTO(cmstext, CachedData.GetCMS(languageId, "sunfare_included")));
                        }

                        #endregion

                        #region EquipmentCost

                        decimal eqPrice = 0;
                        if (groupedServices.ContainsKey(ServiceCategoryEnum.SPORTS_EQUIPMENTS))
                        {
                            eqPrice = session.CurrentFlow.PNR.IsOptionalPNR ? responseOrderView.GetOrderRetrieveServiceListTotalPrice(groupedServices[ServiceCategoryEnum.SPORTS_EQUIPMENTS].Select(s => s.ServiceDefinitionID).ToArray())
                                                                            : response.GetServiceListTotalPrice(groupedServices[ServiceCategoryEnum.SPORTS_EQUIPMENTS].Select(s => s.ServiceDefinitionID).ToArray());


                            cmstext = $"{CachedData.GetCMS(languageId, "sports_equipment")}";

                            if (eqPrice > 0)
                            {
                                priceFlightInfo.Prices.Add(new BasePriceDTO(cmstext, $"{eqPrice.ToString("0.00")} {currencyCode}"));
                            }
                            else
                            {
                                if (segmentBundle == ServiceCategoryEnum.SUN_ECO_BUNDLE && groupedServices.ContainsKey(ServiceCategoryEnum.GOLF_BUNDLE))
                                {
                                    priceFlightInfo.Prices.Add(new BasePriceDTO(cmstext, CachedData.GetCMS(languageId, "sunfare_included")));
                                }
                            }
                        }

                        #endregion

                        #region Flex

                        if (groupedServices.ContainsKey(ServiceCategoryEnum.FLEX))
                        {
                            switch (groupedServices[ServiceCategoryEnum.FLEX].FirstOrDefault()?.Encoding?.Code?.Value)
                            {
                                case "FLXX15":
                                    cmstext = $"{CachedData.GetCMS(languageId, "sunflex_bundle_classic")}";
                                    break;
                                case "FLXX30":
                                    cmstext = $"{CachedData.GetCMS(languageId, "sunflex_bundle_classic_summer")}";
                                    break;
                                case "FLXX7":
                                    cmstext = $"{CachedData.GetCMS(languageId, "sunflex_bundle_classic_summer")}";
                                    break;
                                case "FLXX3":
                                    cmstext = $"{CachedData.GetCMS(languageId, "sunflex_bundle_premium")}";
                                    break;
                                default:
                                    break;
                            }

                            priceFlightInfo.Prices.Add(new BasePriceDTO(cmstext, CachedData.GetCMS(languageId, "sunfare_included")));
                        }
                        #endregion

                        #region Corona

                        decimal coronaPrice = 0;
                        if (groupedServices.ContainsKey(ServiceCategoryEnum.CORONA))
                        {
                            coronaPrice = session.CurrentFlow.PNR.IsOptionalPNR ? responseOrderView.GetOrderRetrieveServiceListTotalPrice(groupedServices[ServiceCategoryEnum.CORONA].Select(s => s.ServiceDefinitionID).ToArray())
                                                                                : response.GetServiceListTotalPrice(groupedServices[ServiceCategoryEnum.CORONA].Select(s => s.ServiceDefinitionID).ToArray());

                            cmstext = $"{CachedData.GetCMS(languageId, "corona_care_guarantee")}";

                            if (coronaPrice > 0)
                            {
                                priceFlightInfo.Prices.Add(new BasePriceDTO(cmstext, $"{coronaPrice.ToString("0.00")} {currencyCode}"));
                            }
                        }
                        #endregion

                        #region IFE
                        decimal ifePrice = 0;
                        if (groupedServices.ContainsKey(ServiceCategoryEnum.IFE))
                        {
                            ifePrice = session.CurrentFlow.PNR.IsOptionalPNR ? responseOrderView.GetOrderRetrieveServiceListTotalPrice(groupedServices[ServiceCategoryEnum.IFE].Select(s => s.ServiceDefinitionID).ToArray())
                                                                             : response.GetServiceListTotalPrice(groupedServices[ServiceCategoryEnum.IFE].Select(s => s.ServiceDefinitionID).ToArray());

                            cmstext = $"{CachedData.GetCMS(languageId, "ancillary_inflight_entertainment")}";

                            if (segmentBundle == ServiceCategoryEnum.SUN_PREMIUM_BUNDLE)
                            {
                                if (ifePrice > 0)
                                {
                                    priceFlightInfo.Prices.Add(new BasePriceDTO(cmstext, $"{ifePrice.ToString("0.00")} {currencyCode}"));
                                }
                                else
                                {
                                    priceFlightInfo.Prices.Add(new BasePriceDTO(cmstext, CachedData.GetCMS(languageId, "sunfare_included")));
                                }
                            }
                            else
                            {
                                if (ifePrice > 0)
                                {
                                    priceFlightInfo.Prices.Add(new BasePriceDTO(cmstext, $"{ifePrice.ToString("0.00")} {currencyCode}"));
                                }
                            }

                        }
                        #endregion

                        #region Golf Bundle
                        decimal golfBundlePrice = 0;
                        if (groupedServices.ContainsKey(ServiceCategoryEnum.GOLF_BUNDLE))
                        {
                            golfBundlePrice = session.CurrentFlow.PNR.IsOptionalPNR ? responseOrderView.GetOrderRetrieveServiceListTotalPrice(groupedServices[ServiceCategoryEnum.GOLF_BUNDLE].Select(s => s.ServiceDefinitionID).ToArray())
                                                                                    : response.GetServiceListTotalPrice(groupedServices[ServiceCategoryEnum.GOLF_BUNDLE].Select(s => s.ServiceDefinitionID).ToArray());

                            cmstext = $"{CachedData.GetCMS(languageId, "golf_bundle_package")}";

                            if (golfBundlePrice > 0)
                            {
                                priceFlightInfo.Prices.Add(new BasePriceDTO(cmstext, $"{golfBundlePrice.ToString("0.00")} {currencyCode}"));
                            }

                        }
                        #endregion                        

                        #endregion


                        result.Flights.Add(priceFlightInfo);
                    }
                }
                //Rebook Flight Case
                else
                {
                    for (int i = 0; i < IbsUtility.SplitIds(segment).Length; i++)
                    {
                        flight.Segments[i].LocalId = IbsUtility.SplitIds(segment)[i];
                    }

                    flight.IncludedSeats = new List<SeatTypeEnum>();

                    var adultPricedCount = session.CurrentFlow.PNR.Passengers.Count(c => c.PassengerType == PassengerTypeEnum.ADT);
                    var childPricedCount = session.CurrentFlow.PNR.Passengers.Count(c => c.PassengerType == PassengerTypeEnum.CHD);
                    var infantPricedCount = session.CurrentFlow.PNR.Passengers.Count(c => c.PassengerType == PassengerTypeEnum.INF);
                    string currencyCode = string.Empty;
                    if (session.CurrentFlow.PNR.IsOptionalPNR)
                    {
                        currencyCode = response.GetCurrenyCodeOptionalPNR() ?? session.CurrentFlow.PNR.Currency;
                    }
                    else
                    {
                        currencyCode = response.GetCurrencyCode() ?? session.CurrentFlow.PNR.Currency;
                    }

                    var segmentItems = response.GetSegments(segment).OrderBy(o => o.Departure.Date).ThenBy(t => t.Departure.Time);


                    var priceFlightInfo = new PriceFlightDTO
                    {
                        Prices = new List<BasePriceDTO>(),
                        Id = segment,
                        Segments = segmentItems.Select(s => s.GetSegmentDTO((LanguageEnum)languageId, response.GetDataList()?.DisclosureList)).ToList(),
                        TotalPrice = response.GetTotalPrice(segment, session),
                        BasePrice = session.CurrentFlow.PNR.Flights.FirstOrDefault(f => f.SegmentRefs == segment)?.BasePrice ?? 0,
                    };
                    for (int i = 0; i < priceFlightInfo.Segments.Count; i++)
                    {
                        priceFlightInfo.Segments[i].Id = flight.Segments[i].Id;
                        priceFlightInfo.Segments[i].LocalId = flight.Segments[i].LocalId;
                    }

                    priceFlightInfo.BasePriceDescription = priceFlightInfo.BasePrice + " " + session.CurrentFlow.PNR.Currency;
                    priceFlightInfo.TotalPriceDescription = priceFlightInfo.TotalPrice + " " + session.CurrentFlow.PNR.Currency;
                    priceFlightInfo.JourneyTime = GetFlightJourneyTime(session, priceFlightInfo.TmobId);

                    priceFlightInfo.FormattedFirstDepartureDate = IbsUtility.FormatDate(session.Language, priceFlightInfo.Segments.FirstOrDefault().DepartureDate);

                    foreach (var segmentDTO in priceFlightInfo.Segments)
                    {
                        segmentDTO.CarrierName = "SUNEXPRESS";

                        if (segmentDTO.AirlineId == "SM")
                        {
                            segmentDTO.CarrierName = "Air Cairo";

                        }
                    }




                    #region Prices

                    #region PassengerPrices

                    if (adultPricedCount > 0 && response.GetPassengerTypeTotalPrice(PassengerTypeEnum.ADT.ToString(), segment) > 0)
                    {
                        priceFlightInfo.Prices.Add(new BasePriceDTO(
                                    $"{CachedData.GetCMS(languageId, "price_detail_fare")} ({adultPricedCount} {(adultPricedCount > 1 ? CachedData.GetCMS(languageId, "adult_plural") : CachedData.GetCMS(languageId, "adult"))})",
                                    response.GetPassengerTypeTotalPriceDescription(PassengerTypeEnum.ADT.ToString(), segment)));
                    }

                    if (childPricedCount > 0 && response.GetPassengerTypeTotalPrice(PassengerTypeEnum.CHD.ToString(), segment) > 0)
                    {
                        priceFlightInfo.Prices.Add(new BasePriceDTO(
                                    $"{CachedData.GetCMS(languageId, "price_detail_fare")} ({childPricedCount} {(childPricedCount > 1 ? CachedData.GetCMS(languageId, "child_plural") : CachedData.GetCMS(languageId, "child"))})",
                                    response.GetPassengerTypeTotalPriceDescription(PassengerTypeEnum.CHD.ToString(), segment)));
                    }

                    if (infantPricedCount > 0 && response.GetPassengerTypeTotalPrice(PassengerTypeEnum.INF.ToString(), segment) > 0)
                    {
                        priceFlightInfo.Prices.Add(new BasePriceDTO(
                                    $"{CachedData.GetCMS(languageId, "price_detail_fare")} ({infantPricedCount} {(infantPricedCount > 1 ? CachedData.GetCMS(languageId, "infant_plural") : CachedData.GetCMS(languageId, "infant"))})",
                                    response.GetPassengerTypeTotalPriceDescription(PassengerTypeEnum.INF.ToString(), segment)));
                    }

                    #endregion

                    #region Tax Prices

                    var taxes = response.GetTotalTaxPrice(segment);
                    var totalSurcaharge = response.GetTotalSurCharge(segment);

                    foreach (var item in taxes)
                    {
                        priceFlightInfo.Prices.Add(new BasePriceDTO(CachedData.GetCMS(languageId, item.Key), $"{item.Value.ToString("0.00")} {currencyCode}"));
                    }

                    if (totalSurcaharge > 0)
                    {
                        priceFlightInfo.Prices.Add(new BasePriceDTO(CachedData.GetCMS(languageId, "price_detail_fuel_surcharge"), $"{totalSurcaharge.ToString("0.00")} {currencyCode}"));
                    }

                    #endregion


                    var services = response.GetServices(segment);


                    //TODO: Bu kısım kaldırılabilir çünkü buraya hiç bir zaman girmeyecek
                    if (session.CurrentFlow.RebookingType == Ubimecs.Infrastructure.Models.Flow.RebookingTypeEnum.AddExtras)
                    {
                        var pnrInfoServices = session.CurrentFlow.PnrInfoResponse.Services;
                        var pnrInfoResponseServies = session.CurrentFlow.GetIbsData<OrderViewRS>(IbsDataTypeEnum.OrderRetrieve)?.GetDataList()?.ServiceDefinitionList;

                        if (pnrInfoResponseServies != null)
                        {
                            services.AddRange(pnrInfoResponseServies.Where(w => pnrInfoServices.Baggages.Where(ww => ww.SegmentIds.Contains(segment)).Select(s => s.Id).Contains(w.ServiceDefinitionID)));
                            services.AddRange(pnrInfoResponseServies.Where(w => pnrInfoServices.Bundles.Where(ww => ww.SegmentIds.Contains(segment)).Select(s => s.Id).Contains(w.ServiceDefinitionID)));
                            services.AddRange(pnrInfoResponseServies.Where(w => pnrInfoServices.CoronaServices.Where(ww => ww.SegmentIds.Contains(segment)).Select(s => s.Id).Contains(w.ServiceDefinitionID)));
                            services.AddRange(pnrInfoResponseServies.Where(w => pnrInfoServices.FlexServices.Where(ww => ww.SegmentIds.Contains(segment)).Select(s => s.Id).Contains(w.ServiceDefinitionID)));
                            services.AddRange(pnrInfoResponseServies.Where(w => pnrInfoServices.GolfBundles.Where(ww => ww.SegmentIds.Contains(segment)).Select(s => s.Id).Contains(w.ServiceDefinitionID)));
                            services.AddRange(pnrInfoResponseServies.Where(w => pnrInfoServices.Meals.Where(ww => ww.SegmentIds.Contains(segment)).Select(s => s.Id).Contains(w.ServiceDefinitionID)));
                            services.AddRange(pnrInfoResponseServies.Where(w => pnrInfoServices.Others.Where(ww => ww.SegmentIds.Contains(segment)).Select(s => s.Id).Contains(w.ServiceDefinitionID)));
                            services.AddRange(pnrInfoResponseServies.Where(w => pnrInfoServices.Seats.Where(ww => ww.SegmentIds.Contains(segment)).Select(s => s.Id).Contains(w.ServiceDefinitionID)));
                            services.AddRange(pnrInfoResponseServies.Where(w => pnrInfoServices.SportEquipments.Where(ww => ww.SegmentIds.Contains(segment)).Select(s => s.Id).Contains(w.ServiceDefinitionID)));
                        }
                    }

                    var serviceCategoryList = CachedData.ServiceCodeCategories;

                    var groupedServices = services.GroupBy(g => serviceCategoryList.ContainsKey(g.Encoding?.Code?.Value) ? serviceCategoryList[g.Encoding?.Code?.Value] : ServiceCategoryEnum.OTHERS)
                        .ToDictionary(k => k.Key, v => v.ToList());



                    var cmstext = "";
                    ServiceCategoryEnum segmentBundle = ServiceCategoryEnum.SUN_ECO_BUNDLE;

                    if (groupedServices.Count == 0)
                    {
                        var pnrInfoServices = session.CurrentFlow.PnrInfoResponse.Services;
                        var pnrInfoResponseServies = session.CurrentFlow.GetIbsData<OrderViewRS>(IbsDataTypeEnum.OrderRetrieve)?.GetDataList()?.ServiceDefinitionList;

                        if (pnrInfoResponseServies != null)
                        {
                            services.AddRange(pnrInfoResponseServies.Where(w => pnrInfoServices.Baggages.Where(ww => segment.Split(' ').Any(t => ww.SegmentIds.Contains(t))).Select(s => s.Id).Contains(w.ServiceDefinitionID)));
                            services.AddRange(pnrInfoResponseServies.Where(w => pnrInfoServices.Bundles.Where(ww => segment.Split(' ').Any(t => ww.SegmentIds.Contains(t))).Select(s => s.Id).Contains(w.ServiceDefinitionID)));
                            services.AddRange(pnrInfoResponseServies.Where(w => pnrInfoServices.CoronaServices.Where(ww => segment.Split(' ').Any(t => ww.SegmentIds.Contains(t))).Select(s => s.Id).Contains(w.ServiceDefinitionID)));
                            services.AddRange(pnrInfoResponseServies.Where(w => pnrInfoServices.FlexServices.Where(ww => segment.Split(' ').Any(t => ww.SegmentIds.Contains(t))).Select(s => s.Id).Contains(w.ServiceDefinitionID)));
                            services.AddRange(pnrInfoResponseServies.Where(w => pnrInfoServices.GolfBundles.Where(ww => segment.Split(' ').Any(t => ww.SegmentIds.Contains(t))).Select(s => s.Id).Contains(w.ServiceDefinitionID)));
                            services.AddRange(pnrInfoResponseServies.Where(w => pnrInfoServices.Meals.Where(ww => segment.Split(' ').Any(t => ww.SegmentIds.Contains(t))).Select(s => s.Id).Contains(w.ServiceDefinitionID)));
                            services.AddRange(pnrInfoResponseServies.Where(w => pnrInfoServices.Others.Where(ww => segment.Split(' ').Any(t => ww.SegmentIds.Contains(t))).Select(s => s.Id).Contains(w.ServiceDefinitionID)));
                            services.AddRange(pnrInfoResponseServies.Where(w => pnrInfoServices.Seats.Where(ww => segment.Split(' ').Any(t => ww.SegmentIds.Contains(t))).Select(s => s.Id).Contains(w.ServiceDefinitionID)));
                            services.AddRange(pnrInfoResponseServies.Where(w => pnrInfoServices.SportEquipments.Where(ww => segment.Split(' ').Any(t => ww.SegmentIds.Contains(t))).Select(s => s.Id).Contains(w.ServiceDefinitionID)));
                        }
                        groupedServices = services.GroupBy(g => serviceCategoryList.ContainsKey(g.Encoding?.Code?.Value) ? serviceCategoryList[g.Encoding?.Code?.Value] : ServiceCategoryEnum.OTHERS)
                        .ToDictionary(k => k.Key, v => v.ToList());

                        var retrieveResponse = session.CurrentFlow.GetIbsData<OrderViewRS>(IbsDataTypeEnum.OrderRetrieve);

                        #region Additional Credit Card Fee For Installment
                        decimal creditCardFeePrice = 0;
                        if (groupedServices.ContainsKey(ServiceCategoryEnum.FEES))
                        {
                            creditCardFeePrice = response.GetTotalCreditCardFeePrice(segment);
                            cmstext = $"{CachedData.GetCMS(languageId, "credit_card_installment_fee")}";
                            priceFlightInfo.Prices.Add(new BasePriceDTO(cmstext, $"{creditCardFeePrice.ToString("0.00")} {currencyCode}"));
                        }
                        #endregion

                        #region Bundle Prices               

                        ServiceDefinitionTypeServiceBundle bundle = null;

                        decimal bundlePrice = 0;
                        if (groupedServices.ContainsKey(ServiceCategoryEnum.SUN_PREMIUM_BUNDLE))
                        {
                            bundlePrice = retrieveResponse.GetOrderRetrieveServiceListTotalPrice(groupedServices[ServiceCategoryEnum.SUN_PREMIUM_BUNDLE].Select(s => s.ServiceDefinitionID).ToArray());
                            cmstext = $"{CachedData.GetCMS(languageId, "sun_fare_text")}: {CachedData.GetCMS(languageId, "sun_premium")}";

                            priceFlightInfo.Prices.Add(new BasePriceDTO(cmstext, bundlePrice > 0 ? $"{bundlePrice.ToString("0.00")} {currencyCode}" : CachedData.GetCMS(languageId, "sunfare_included"), true, "rgba(81, 186, 255, 1.0)"));
                            segmentBundle = ServiceCategoryEnum.SUN_PREMIUM_BUNDLE;
                            bundle = groupedServices[ServiceCategoryEnum.SUN_PREMIUM_BUNDLE].FirstOrDefault()?.Item as ServiceDefinitionTypeServiceBundle;
                        }
                        else if (groupedServices.ContainsKey(ServiceCategoryEnum.SUN_CLASSIC_BUNDLE))
                        {
                            bundlePrice = retrieveResponse.GetOrderRetrieveServiceListTotalPrice(groupedServices[ServiceCategoryEnum.SUN_CLASSIC_BUNDLE].Select(s => s.ServiceDefinitionID).ToArray());
                            cmstext = $"{CachedData.GetCMS(languageId, "sun_fare_text")}: {CachedData.GetCMS(languageId, "sun_classic")}";

                            priceFlightInfo.Prices.Add(new BasePriceDTO(cmstext, bundlePrice > 0 ? $"{bundlePrice.ToString("0.00")} {currencyCode}" : CachedData.GetCMS(languageId, "sunfare_included"), true, "rgba(235, 105, 11, 1.0))"));
                            segmentBundle = ServiceCategoryEnum.SUN_CLASSIC_BUNDLE;
                            bundle = groupedServices[ServiceCategoryEnum.SUN_CLASSIC_BUNDLE].FirstOrDefault()?.Item as ServiceDefinitionTypeServiceBundle;

                        }
                        else
                        {
                            cmstext = $"{CachedData.GetCMS(languageId, "sun_fare_text")}: {CachedData.GetCMS(languageId, "sun_eco")}";

                            priceFlightInfo.Prices.Add(new BasePriceDTO(cmstext, CachedData.GetCMS(languageId, "sunfare_included"), true, "rgba(241, 172, 36, 1.0)"));

                            if (groupedServices.ContainsKey(ServiceCategoryEnum.GOLF_BUNDLE))
                            {
                                bundle = groupedServices[ServiceCategoryEnum.GOLF_BUNDLE].FirstOrDefault()?.Item as ServiceDefinitionTypeServiceBundle;
                            }
                        }
                        var selectedBundleEnum = session.CurrentFlow.PNR.Flights.FirstOrDefault(f => f.SegmentRefs == segment)?.SelectedBundle;
                        if (selectedBundleEnum == ServiceCategoryEnum.SUN_LIGHT_BUNDLE)
                        {
                            segmentBundle = selectedBundleEnum.Value;
                        }
                        priceFlightInfo.SelectedBundleCategoryId = segmentBundle;


                        if (bundle != null && flight != null)
                        {
                            flight.IncludedSeats = response.GetSeatTypes(bundle.ServiceDefinitionRef.Select(s => s.Value).ToArray(), serviceCategoryList);
                            priceFlightInfo.IsSeatIncluded = flight.IncludedSeats?.Count > 0;
                        }

                        #endregion

                        #region Baggage

                        decimal baggagePrice = 0;
                        if (groupedServices.ContainsKey(ServiceCategoryEnum.BAGGAGE))
                        {
                            baggagePrice = retrieveResponse.GetOrderRetrieveServiceListTotalPrice(groupedServices[ServiceCategoryEnum.BAGGAGE].Select(s => s.ServiceDefinitionID).ToArray());
                        }

                        cmstext = $"{CachedData.GetCMS(languageId, "luggage_price_detail_key")}";
                        priceFlightInfo.Prices.Add(new BasePriceDTO(cmstext, baggagePrice > 0 ? $"{baggagePrice.ToString("0.00")} {currencyCode}" : CachedData.GetCMS(languageId, "sunfare_included")));
                        #endregion

                        #region Meal
                        decimal mealPrice = 0;
                        if (groupedServices.ContainsKey(ServiceCategoryEnum.MEALS))
                        {
                            mealPrice = retrieveResponse.GetOrderRetrieveServiceListTotalPrice(groupedServices[ServiceCategoryEnum.MEALS].Select(s => s.ServiceDefinitionID).ToArray());
                        }

                        cmstext = $"{CachedData.GetCMS(languageId, "meal")}";

                        if (mealPrice > 0)
                        {
                            priceFlightInfo.Prices.Add(new BasePriceDTO(cmstext, $"{mealPrice.ToString("0.00")} {currencyCode}"));
                        }
                        else if (segmentBundle != ServiceCategoryEnum.SUN_ECO_BUNDLE || (segmentBundle == ServiceCategoryEnum.SUN_ECO_BUNDLE && groupedServices.ContainsKey(ServiceCategoryEnum.GOLF_BUNDLE)))
                        {
                            priceFlightInfo.Prices.Add(new BasePriceDTO(cmstext, CachedData.GetCMS(languageId, "sunfare_included")));
                        }
                        #endregion

                        #region Seat
                        decimal seatPrice = 0;
                        if (groupedServices.ContainsKey(ServiceCategoryEnum.SEAT))
                        {
                            seatPrice = retrieveResponse.GetOrderRetrieveServiceListTotalPrice(groupedServices[ServiceCategoryEnum.SEAT].Select(s => s.ServiceDefinitionID).ToArray());
                        }

                        cmstext = session.CurrentFlow.PNR.Passengers.Count > 1 ? $"{CachedData.GetCMS(languageId, "seats")}" : $"{CachedData.GetCMS(languageId, "seat")}";

                        if (seatPrice > 0)
                        {
                            priceFlightInfo.Prices.Add(new BasePriceDTO(cmstext, $"{seatPrice.ToString("0.00")} {currencyCode}"));
                        }
                        else if (segmentBundle != ServiceCategoryEnum.SUN_ECO_BUNDLE || (segmentBundle == ServiceCategoryEnum.SUN_ECO_BUNDLE && groupedServices.ContainsKey(ServiceCategoryEnum.GOLF_BUNDLE)))
                        {
                            priceFlightInfo.Prices.Add(new BasePriceDTO(cmstext, CachedData.GetCMS(languageId, "sunfare_included")));
                        }

                        #endregion

                        #region EquipmentCost

                        decimal eqPrice = 0;
                        if (groupedServices.ContainsKey(ServiceCategoryEnum.SPORTS_EQUIPMENTS))
                        {
                            eqPrice = retrieveResponse.GetOrderRetrieveServiceListTotalPrice(groupedServices[ServiceCategoryEnum.SPORTS_EQUIPMENTS].Select(s => s.ServiceDefinitionID).ToArray());


                            cmstext = $"{CachedData.GetCMS(languageId, "sports_equipment")}";

                            if (eqPrice > 0)
                            {
                                priceFlightInfo.Prices.Add(new BasePriceDTO(cmstext, $"{eqPrice.ToString("0.00")} {currencyCode}"));
                            }
                            else
                            {
                                if (segmentBundle == ServiceCategoryEnum.SUN_ECO_BUNDLE && groupedServices.ContainsKey(ServiceCategoryEnum.GOLF_BUNDLE))
                                {
                                    priceFlightInfo.Prices.Add(new BasePriceDTO(cmstext, CachedData.GetCMS(languageId, "sunfare_included")));
                                }
                            }
                        }

                        #endregion

                        #region Flex

                        if (groupedServices.ContainsKey(ServiceCategoryEnum.FLEX))
                        {
                            switch (groupedServices[ServiceCategoryEnum.FLEX].FirstOrDefault()?.Encoding?.Code?.Value)
                            {
                                case "FLXX15":
                                    cmstext = $"{CachedData.GetCMS(languageId, "sunflex_bundle_classic")}";
                                    break;
                                case "FLXX30":
                                    cmstext = $"{CachedData.GetCMS(languageId, "sunflex_bundle_classic_summer")}";
                                    break;
                                case "FLXX7":
                                    cmstext = $"{CachedData.GetCMS(languageId, "sunflex_bundle_classic_summer")}";
                                    break;
                                case "FLXX3":
                                    cmstext = $"{CachedData.GetCMS(languageId, "sunflex_bundle_premium")}";
                                    break;
                                default:
                                    break;
                            }

                            priceFlightInfo.Prices.Add(new BasePriceDTO(cmstext, CachedData.GetCMS(languageId, "sunfare_included")));
                        }
                        #endregion

                        #region Corona

                        decimal coronaPrice = 0;
                        if (groupedServices.ContainsKey(ServiceCategoryEnum.CORONA))
                        {
                            coronaPrice = retrieveResponse.GetOrderRetrieveServiceListTotalPrice(groupedServices[ServiceCategoryEnum.CORONA].Select(s => s.ServiceDefinitionID).ToArray());

                            cmstext = $"{CachedData.GetCMS(languageId, "corona_care_guarantee")}";

                            if (coronaPrice > 0)
                            {
                                priceFlightInfo.Prices.Add(new BasePriceDTO(cmstext, $"{coronaPrice.ToString("0.00")} {currencyCode}"));
                            }
                        }
                        #endregion

                        #region IFE
                        decimal ifePrice = 0;
                        if (groupedServices.ContainsKey(ServiceCategoryEnum.IFE))
                        {
                            ifePrice = retrieveResponse.GetOrderRetrieveServiceListTotalPrice(groupedServices[ServiceCategoryEnum.IFE].Select(s => s.ServiceDefinitionID).ToArray());

                            cmstext = $"{CachedData.GetCMS(languageId, "ancillary_inflight_entertainment")}";

                            if (segmentBundle == ServiceCategoryEnum.SUN_PREMIUM_BUNDLE)
                            {
                                if (ifePrice > 0)
                                {
                                    priceFlightInfo.Prices.Add(new BasePriceDTO(cmstext, $"{ifePrice.ToString("0.00")} {currencyCode}"));
                                }
                                else
                                {
                                    priceFlightInfo.Prices.Add(new BasePriceDTO(cmstext, CachedData.GetCMS(languageId, "sunfare_included")));
                                }
                            }
                            else
                            {
                                if (ifePrice > 0)
                                {
                                    priceFlightInfo.Prices.Add(new BasePriceDTO(cmstext, $"{ifePrice.ToString("0.00")} {currencyCode}"));
                                }
                            }

                        }
                        #endregion

                        #region Golf Bundle
                        decimal golfBundlePrice = 0;
                        if (groupedServices.ContainsKey(ServiceCategoryEnum.GOLF_BUNDLE))
                        {
                            golfBundlePrice = retrieveResponse.GetOrderRetrieveServiceListTotalPrice(groupedServices[ServiceCategoryEnum.GOLF_BUNDLE].Select(s => s.ServiceDefinitionID).ToArray());

                            cmstext = $"{CachedData.GetCMS(languageId, "golf_bundle_package")}";

                            if (golfBundlePrice > 0)
                            {
                                priceFlightInfo.Prices.Add(new BasePriceDTO(cmstext, $"{golfBundlePrice.ToString("0.00")} {currencyCode}"));
                            }

                        }
                        #endregion




                    }
                    else
                    {
                        #region Additional Credit Card Fee For Installment
                        decimal creditCardFeePrice = 0;
                        if (groupedServices.ContainsKey(ServiceCategoryEnum.FEES))
                        {
                            creditCardFeePrice = response.GetTotalCreditCardFeePrice(segment);
                            cmstext = $"{CachedData.GetCMS(languageId, "credit_card_installment_fee")}";
                            priceFlightInfo.Prices.Add(new BasePriceDTO(cmstext, $"{creditCardFeePrice.ToString("0.00")} {currencyCode}"));
                        }
                        #endregion

                        #region Bundle Prices               

                        ServiceDefinitionTypeServiceBundle bundle = null;

                        decimal bundlePrice = 0;
                        if (groupedServices.ContainsKey(ServiceCategoryEnum.SUN_PREMIUM_BUNDLE))
                        {
                            bundlePrice = response.GetServiceListTotalPrice(groupedServices[ServiceCategoryEnum.SUN_PREMIUM_BUNDLE].Select(s => s.ServiceDefinitionID).ToArray());
                            cmstext = $"{CachedData.GetCMS(languageId, "sun_fare_text")}: {CachedData.GetCMS(languageId, "sun_premium")}";

                            priceFlightInfo.Prices.Add(new BasePriceDTO(cmstext, bundlePrice > 0 ? $"{bundlePrice.ToString("0.00")} {currencyCode}" : CachedData.GetCMS(languageId, "sunfare_included"), true, "rgba(81, 186, 255, 1.0)"));
                            segmentBundle = ServiceCategoryEnum.SUN_PREMIUM_BUNDLE;
                            bundle = groupedServices[ServiceCategoryEnum.SUN_PREMIUM_BUNDLE].FirstOrDefault()?.Item as ServiceDefinitionTypeServiceBundle;
                        }
                        else if (groupedServices.ContainsKey(ServiceCategoryEnum.SUN_CLASSIC_BUNDLE))
                        {
                            bundlePrice = response.GetServiceListTotalPrice(groupedServices[ServiceCategoryEnum.SUN_CLASSIC_BUNDLE].Select(s => s.ServiceDefinitionID).ToArray());
                            cmstext = $"{CachedData.GetCMS(languageId, "sun_fare_text")}: {CachedData.GetCMS(languageId, "sun_classic")}";

                            priceFlightInfo.Prices.Add(new BasePriceDTO(cmstext, bundlePrice > 0 ? $"{bundlePrice.ToString("0.00")} {currencyCode}" : CachedData.GetCMS(languageId, "sunfare_included"), true, "rgba(235, 105, 11, 1.0))"));
                            segmentBundle = ServiceCategoryEnum.SUN_CLASSIC_BUNDLE;
                            bundle = groupedServices[ServiceCategoryEnum.SUN_CLASSIC_BUNDLE].FirstOrDefault()?.Item as ServiceDefinitionTypeServiceBundle;

                        }
                        else
                        {
                            cmstext = $"{CachedData.GetCMS(languageId, "sun_fare_text")}: {CachedData.GetCMS(languageId, "sun_eco")}";

                            priceFlightInfo.Prices.Add(new BasePriceDTO(cmstext, CachedData.GetCMS(languageId, "sunfare_included"), true, "rgba(241, 172, 36, 1.0)"));

                            if (groupedServices.ContainsKey(ServiceCategoryEnum.GOLF_BUNDLE))
                            {
                                bundle = groupedServices[ServiceCategoryEnum.GOLF_BUNDLE].FirstOrDefault()?.Item as ServiceDefinitionTypeServiceBundle;
                            }
                        }

                        var selectedBundleEnum = session.CurrentFlow.PNR.Flights.FirstOrDefault(f => f.SegmentRefs == segment)?.SelectedBundle;
                        if (selectedBundleEnum == ServiceCategoryEnum.SUN_LIGHT_BUNDLE)
                        {
                            segmentBundle = selectedBundleEnum.Value;
                        }
                        priceFlightInfo.SelectedBundleCategoryId = segmentBundle;


                        if (bundle != null && flight != null)
                        {
                            flight.IncludedSeats = response.GetSeatTypes(bundle.ServiceDefinitionRef.Select(s => s.Value).ToArray(), serviceCategoryList);
                            priceFlightInfo.IsSeatIncluded = flight.IncludedSeats?.Count > 0;
                        }

                        #endregion

                        #region Baggage

                        decimal baggagePrice = 0;
                        if (groupedServices.ContainsKey(ServiceCategoryEnum.BAGGAGE))
                        {
                            baggagePrice = response.GetServiceListTotalPrice(groupedServices[ServiceCategoryEnum.BAGGAGE].Select(s => s.ServiceDefinitionID).ToArray());
                        }

                        cmstext = $"{CachedData.GetCMS(languageId, "luggage_price_detail_key")}";
                        priceFlightInfo.Prices.Add(new BasePriceDTO(cmstext, baggagePrice > 0 ? $"{baggagePrice.ToString("0.00")} {currencyCode}" : CachedData.GetCMS(languageId, "sunfare_included")));
                        #endregion

                        #region Meal
                        decimal mealPrice = 0;
                        if (groupedServices.ContainsKey(ServiceCategoryEnum.MEALS))
                        {
                            mealPrice = response.GetServiceListTotalPrice(groupedServices[ServiceCategoryEnum.MEALS].Select(s => s.ServiceDefinitionID).ToArray());
                        }

                        cmstext = $"{CachedData.GetCMS(languageId, "meal")}";

                        if (mealPrice > 0)
                        {
                            priceFlightInfo.Prices.Add(new BasePriceDTO(cmstext, $"{mealPrice.ToString("0.00")} {currencyCode}"));
                        }
                        else if (segmentBundle != ServiceCategoryEnum.SUN_ECO_BUNDLE || (segmentBundle == ServiceCategoryEnum.SUN_ECO_BUNDLE && groupedServices.ContainsKey(ServiceCategoryEnum.GOLF_BUNDLE)))
                        {
                            priceFlightInfo.Prices.Add(new BasePriceDTO(cmstext, CachedData.GetCMS(languageId, "sunfare_included")));
                        }
                        #endregion

                        #region Seat
                        decimal seatPrice = 0;
                        if (groupedServices.ContainsKey(ServiceCategoryEnum.SEAT))
                        {
                            seatPrice = response.GetServiceListTotalPrice(groupedServices[ServiceCategoryEnum.SEAT].Select(s => s.ServiceDefinitionID).ToArray());
                        }

                        cmstext = session.CurrentFlow.PNR.Passengers.Count > 1 ? $"{CachedData.GetCMS(languageId, "seats")}" : $"{CachedData.GetCMS(languageId, "seat")}";

                        if (seatPrice > 0)
                        {
                            priceFlightInfo.Prices.Add(new BasePriceDTO(cmstext, $"{seatPrice.ToString("0.00")} {currencyCode}"));
                        }
                        else if (segmentBundle != ServiceCategoryEnum.SUN_ECO_BUNDLE || (segmentBundle == ServiceCategoryEnum.SUN_ECO_BUNDLE && groupedServices.ContainsKey(ServiceCategoryEnum.GOLF_BUNDLE)))
                        {
                            priceFlightInfo.Prices.Add(new BasePriceDTO(cmstext, CachedData.GetCMS(languageId, "sunfare_included")));
                        }

                        #endregion

                        #region EquipmentCost

                        decimal eqPrice = 0;
                        if (groupedServices.ContainsKey(ServiceCategoryEnum.SPORTS_EQUIPMENTS))
                        {
                            eqPrice = response.GetServiceListTotalPrice(groupedServices[ServiceCategoryEnum.SPORTS_EQUIPMENTS].Select(s => s.ServiceDefinitionID).ToArray());


                            cmstext = $"{CachedData.GetCMS(languageId, "sports_equipment")}";

                            if (eqPrice > 0)
                            {
                                priceFlightInfo.Prices.Add(new BasePriceDTO(cmstext, $"{eqPrice.ToString("0.00")} {currencyCode}"));
                            }
                            else
                            {
                                if (segmentBundle == ServiceCategoryEnum.SUN_ECO_BUNDLE && groupedServices.ContainsKey(ServiceCategoryEnum.GOLF_BUNDLE))
                                {
                                    priceFlightInfo.Prices.Add(new BasePriceDTO(cmstext, CachedData.GetCMS(languageId, "sunfare_included")));
                                }
                            }
                        }

                        #endregion

                        #region Flex

                        if (groupedServices.ContainsKey(ServiceCategoryEnum.FLEX))
                        {
                            switch (groupedServices[ServiceCategoryEnum.FLEX].FirstOrDefault()?.Encoding?.Code?.Value)
                            {
                                case "FLXX15":
                                    cmstext = $"{CachedData.GetCMS(languageId, "sunflex_bundle_classic")}";
                                    break;
                                case "FLXX30":
                                    cmstext = $"{CachedData.GetCMS(languageId, "sunflex_bundle_classic_summer")}";
                                    break;
                                case "FLXX7":
                                    cmstext = $"{CachedData.GetCMS(languageId, "sunflex_bundle_classic_summer")}";
                                    break;
                                case "FLXX3":
                                    cmstext = $"{CachedData.GetCMS(languageId, "sunflex_bundle_premium")}";
                                    break;
                                default:
                                    break;
                            }

                            priceFlightInfo.Prices.Add(new BasePriceDTO(cmstext, CachedData.GetCMS(languageId, "sunfare_included")));
                        }
                        #endregion

                        #region Corona

                        decimal coronaPrice = 0;
                        if (groupedServices.ContainsKey(ServiceCategoryEnum.CORONA))
                        {
                            coronaPrice = response.GetServiceListTotalPrice(groupedServices[ServiceCategoryEnum.CORONA].Select(s => s.ServiceDefinitionID).ToArray());

                            cmstext = $"{CachedData.GetCMS(languageId, "corona_care_guarantee")}";

                            if (coronaPrice > 0)
                            {
                                priceFlightInfo.Prices.Add(new BasePriceDTO(cmstext, $"{coronaPrice.ToString("0.00")} {currencyCode}"));
                            }
                        }
                        #endregion

                        #region IFE
                        decimal ifePrice = 0;
                        if (groupedServices.ContainsKey(ServiceCategoryEnum.IFE))
                        {
                            ifePrice = response.GetServiceListTotalPrice(groupedServices[ServiceCategoryEnum.IFE].Select(s => s.ServiceDefinitionID).ToArray());

                            cmstext = $"{CachedData.GetCMS(languageId, "ancillary_inflight_entertainment")}";

                            if (segmentBundle == ServiceCategoryEnum.SUN_PREMIUM_BUNDLE)
                            {
                                if (ifePrice > 0)
                                {
                                    priceFlightInfo.Prices.Add(new BasePriceDTO(cmstext, $"{ifePrice.ToString("0.00")} {currencyCode}"));
                                }
                                else
                                {
                                    priceFlightInfo.Prices.Add(new BasePriceDTO(cmstext, CachedData.GetCMS(languageId, "sunfare_included")));
                                }
                            }
                            else
                            {
                                if (ifePrice > 0)
                                {
                                    priceFlightInfo.Prices.Add(new BasePriceDTO(cmstext, $"{ifePrice.ToString("0.00")} {currencyCode}"));
                                }
                            }

                        }
                        #endregion

                        #region Golf Bundle
                        decimal golfBundlePrice = 0;
                        if (groupedServices.ContainsKey(ServiceCategoryEnum.GOLF_BUNDLE))
                        {
                            golfBundlePrice = response.GetServiceListTotalPrice(groupedServices[ServiceCategoryEnum.GOLF_BUNDLE].Select(s => s.ServiceDefinitionID).ToArray());

                            cmstext = $"{CachedData.GetCMS(languageId, "golf_bundle_package")}";

                            if (golfBundlePrice > 0)
                            {
                                priceFlightInfo.Prices.Add(new BasePriceDTO(cmstext, $"{golfBundlePrice.ToString("0.00")} {currencyCode}"));
                            }

                        }
                        #endregion


                    }

                    #endregion

                    result.Flights.Add(priceFlightInfo);

                }

            }

            foreach (var flight in result.Flights)
            {
                if (result.Flights.Count == 1)
                {
                    flight.Title = CachedData.GetCMS(languageId, "departure_flight");
                }
                else if (session.CurrentFlow.PNR.Flights.Count == 2 && session.CurrentFlow.PNR.Flights[0].DepartureCode == session.CurrentFlow.PNR.Flights[1].ArrivalCode)
                {
                    if (session.CurrentFlow.PNR.Flights[0].DepartureCode == flight.DepartureCode)
                    {
                        flight.Title = CachedData.GetCMS(languageId, "departure_flight");

                    }
                    else
                    {
                        flight.Title = CachedData.GetCMS(languageId, "return_flight");

                    }
                }
                else
                {
                    flight.Title = CachedData.GetCMS(languageId, "flight") + " " + (result.Flights.OrderBy(o => o.Segments.First().CompleteDepartureTime).ToList().IndexOf(flight) + 1);
                }
            }

            result.Flights = result.Flights.OrderBy(o => o.Segments.FirstOrDefault().CompleteDepartureTime).ToList();

            result.Services = GeneralMappers.GetAllServices(response, session);

            foreach (var serviceData in session.CurrentFlow.ManageBookingServiceData)
            {
                var localSegmentId = result.Flights.SelectMany(s => s.Segments).Where(w => IbsUtility.SplitIds(serviceData.SegmentKey).Contains(w.Id)).Select(s => s.LocalId).ToArray();
                serviceData.LocalSegmentKey = IbsUtility.ConcatIds(localSegmentId);

                var newService = result.Services.Meals.FirstOrDefault(a => a.PassengerIds.Contains(serviceData.Pax) && a.Code.Equals(serviceData.EncodingCode) && a.SegmentIds.Contains(serviceData.LocalSegmentKey));
                if (newService != null)
                {
                    serviceData.LocalServiceId = newService.Id;
                }
            }

            if (session.CurrentFlow.RebookingType == Ubimecs.Infrastructure.Models.Flow.RebookingTypeEnum.AddExtras || session.CurrentFlow.RebookingType == Ubimecs.Infrastructure.Models.Flow.RebookingTypeEnum.UpgradeBundle)
            {
                var services = session.CurrentFlow.PnrInfoResponse.Services;

                if ((result.Services.Bundles?.Count ?? 0) == 0) result.Services.Bundles = services.Bundles;
                if ((result.Services.FlexServices?.Count ?? 0) == 0) result.Services.FlexServices = services.FlexServices;
                if ((result.Services.CoronaServices?.Count ?? 0) == 0) result.Services.CoronaServices = services.CoronaServices;
                if ((result.Services.GolfBundles?.Count ?? 0) == 0) result.Services.GolfBundles = services.GolfBundles;
                if ((result.Services.IfeServices?.Count ?? 0) == 0) result.Services.IfeServices = services.IfeServices;
                if ((result.Services.Others?.Count ?? 0) == 0) result.Services.Others = services.Others;

                foreach (var item in services.Meals)
                {
                    if (!result.Services.Meals.Any(a => IbsUtility.CheckIdsEqual(a.PassengerIds, item.PassengerIds) && IbsUtility.CheckIdsEqual(a.SegmentIds, item.SegmentIds)))
                    {
                        result.Services.Meals.Add(item);
                    }
                }

                foreach (var item in services.CoronaServices)
                {
                    if (!result.Services.CoronaServices.Any(a => IbsUtility.CheckIdsEqual(a.PassengerIds, item.PassengerIds) && IbsUtility.CheckIdsEqual(a.SegmentIds, item.SegmentIds)))
                    {
                        result.Services.CoronaServices.Add(item);
                    }
                }

                foreach (var item in services.Baggages)
                {
                    if (!result.Services.Baggages.Any(a => IbsUtility.CheckIdsEqual(a.PassengerIds, item.PassengerIds) && IbsUtility.CheckIdsEqual(a.SegmentIds, item.SegmentIds)))
                    {
                        result.Services.Baggages.Add(item);
                    }
                }

                foreach (var item in services.Seats)
                {
                    if (!result.Services.Seats.Any(a => IbsUtility.CheckIdsEqual(a.PassengerIds, item.PassengerIds) && IbsUtility.CheckIdsEqual(a.SegmentIds, item.SegmentIds)) && session.CurrentFlow.RebookingType != Ubimecs.Infrastructure.Models.Flow.RebookingTypeEnum.UpgradeBundle)
                    {
                        result.Services.Seats.Add(item);
                    }
                }

                foreach (var item in services.SportEquipments)
                {
                    if (!result.Services.SportEquipments.Any(a => IbsUtility.CheckIdsEqual(a.PassengerIds, item.PassengerIds) && IbsUtility.CheckIdsEqual(a.SegmentIds, item.SegmentIds) && a.Id == item.Id))
                    {
                        result.Services.SportEquipments.Add(item);
                    }
                }
                foreach (var item in services.IfeServices)
                {
                    if (!result.Services.IfeServices.Any(a => IbsUtility.CheckIdsEqual(a.PassengerIds, item.PassengerIds) && IbsUtility.CheckIdsEqual(a.SegmentIds, item.SegmentIds) && a.Id == item.Id))
                    {
                        result.Services.IfeServices.Add(item);
                    }
                }
                foreach (var item in services.GolfBundles)
                {
                    if (!result.Services.GolfBundles.Any(a => IbsUtility.CheckIdsEqual(a.PassengerIds, item.PassengerIds) && IbsUtility.CheckIdsEqual(a.SegmentIds, item.SegmentIds) && a.Id == item.Id))
                    {
                        result.Services.GolfBundles.Add(item);
                    }
                }
            }

            result.SetTotalPriceManually(response.GetTotalPrice(result));

            result.PriceInfo = response.GetPriceInfo();
            if (session.CurrentFlow?.PNR?.GiftVoucherAmount != 0)
            {
                result.PriceInfo.ShownGiftVoucherAmount = result.TotalPrice > session.CurrentFlow?.PNR?.GiftVoucherAmount ? session.CurrentFlow.PNR.GiftVoucherAmount : result.TotalPrice;
            }
            session.CurrentFlow.LatestPriceResponse = result;
            return result;
        }

        public static void LoadBundles(SessionCache session,
            ref List<ServiceDefinitionType> services,
            ref List<OrderReshopRQQueryReshopOrderServicingAddQualifier> offers,
            bool isbundleTypeChanging)
        {
            var sessionServices = session.CurrentFlow.Services?.Services;

            if (session.CurrentFlow.PNR.Flights != null && sessionServices != null)
            {
                foreach (var item in session.CurrentFlow.PNR.Flights)
                {
                    if (((item.State == OfferRebookingStateEnum.Added || item.State == OfferRebookingStateEnum.Upgraded) && IbsUtility.BundleCategories.Contains(item.SelectedBundle)) || (session.CurrentFlow.RebookingType == Ubimecs.Infrastructure.Models.Flow.RebookingTypeEnum.TourOperator && item.State == OfferRebookingStateEnum.ServiceChanging && IbsUtility.BundleCategories.Contains(item.SelectedBundle)))
                    {
                        //LoadBundleFromServiceList(session, ref services, ref offers, item);

                        //TO DO: Premium yemek hatasında kontrol et...
                        if (isbundleTypeChanging)
                        {
                            if (sessionServices.ContainsKey(item.SelectedBundle))
                            {
                                LoadBundleFromServiceList(session, ref services, ref offers, item);
                            }
                        }
                        else
                        {
                            if (!LoadBundleFromLastOrderReshop(session, ref services, ref offers, item))
                            {
                                if (sessionServices.ContainsKey(item.SelectedBundle))
                                {
                                    LoadBundleFromServiceList(session, ref services, ref offers, item);
                                }
                            }
                        }
                    }
                }
            }
        }

        private static void FindMisingSegmentsWhileUpgrade(SessionCache session, ref List<Ubimecs.Infrastructure.Models.DTO.Seat.SegmentPassengerSeat> pnrSeats)
        {
            var operationalFlight = session.CurrentFlow.PNR?.Flights?.FirstOrDefault(t => t.State == OfferRebookingStateEnum.Upgraded);
            if (operationalFlight != null
             && pnrSeats != null
             && pnrSeats.Count > 0)
            {
                var pnrSeatsInOperation = pnrSeats;
                if (operationalFlight.Segments.Count > 1)
                {
                    //Connection Flight olduğunda
                    foreach (var segment in operationalFlight.Segments)
                    {
                        foreach (var pass in session.CurrentFlow.PNR.Passengers)
                        {
                            if (!pnrSeatsInOperation.Any(k => k.PassengerId == pass.Id && k.SegmentTmobId == segment.TmobId)
                                && session.CurrentFlow?
                                          .PnrInfoResponse?
                                          .Services?
                                          .Seats?
                                          .FirstOrDefault(t => t.PassengerIds.Contains(pass.Id) && t.SegmentIds.Contains(segment.Id)) is var seat)
                            {
                                if (seat != null)
                                {
                                    pnrSeats.Add(new Ubimecs.Infrastructure.Models.DTO.Seat.SegmentPassengerSeat
                                    {
                                        Column = seat.Column,
                                        Number = seat.SeatNumber,
                                        PassengerId = pass.Id,
                                        SegmentTmobId = segment.TmobId
                                    });
                                }
                            }
                        }
                    }
                }
                else
                {
                    foreach (var pass in session.CurrentFlow.PNR.Passengers)
                    {
                        if (!pnrSeatsInOperation.Any(k => k.PassengerId == pass.Id && k.SegmentTmobId == operationalFlight.Segments.FirstOrDefault().TmobId)
                            && session.CurrentFlow.PnrInfoResponse.Services.Seats.FirstOrDefault(k => k.PassengerIds.Contains(pass.Id) && k.SegmentIds.Contains(operationalFlight.Segments.FirstOrDefault().Id)) is var seat)
                        {
                            if (seat != null)
                            {
                                pnrSeats.Add(new Ubimecs.Infrastructure.Models.DTO.Seat.SegmentPassengerSeat
                                {
                                    Column = seat.Column,
                                    Number = seat.SeatNumber,
                                    PassengerId = pass.Id,
                                    SegmentTmobId = operationalFlight.Segments.FirstOrDefault().TmobId
                                });
                            }
                        }
                    }
                }
            }

        }

        public static void LoadSeats(SessionCache session, ref List<OrderReshopRQQueryReshopOrderServicingAddQualifier> offers)
        {
            var pnSeats = session.CurrentFlow.PNR.Seats;
            FindMisingSegmentsWhileUpgrade(session, ref pnSeats);
            if (pnSeats != null)
            {
                foreach (var seat in pnSeats)
                {
                    if (!string.IsNullOrEmpty(seat.Column) && !string.IsNullOrEmpty(seat.Number))
                    {
                        var seatRespone = session.CurrentFlow.GetIbsData<SeatAvailabilityRS>(IbsDataTypeEnum.Seats + "_" + seat.SegmentTmobId);

                        var offerItem = seatRespone.GetOfferItem(seat.Number, seat.Column,session,seat.SegmentTmobId);

                        var segment = session.CurrentFlow.PNR.Flights.SelectMany(s => s.Segments).FirstOrDefault(f => f.TmobId == seat.SegmentTmobId);

                        OrderReshopRQQueryReshopOrderServicingAddQualifier addQualifier = new OrderReshopRQQueryReshopOrderServicingAddQualifier()
                        {
                            ExistingOrderQualifier = new ExistingOrderQualifier
                            {
                                PassengerReferences = seat.PassengerId,
                                OrderKeys = new OrderKeysType
                                {
                                    refs = seat.PassengerId,
                                    OrderID = new OrderID_Type()
                                    {
                                        Value = session.CurrentFlow.PNR.Number,
                                        Owner = GeneralConstants.BOOKING_OWNER
                                    },
                                    AssociatedIDs = new OrderKeysTypeAssociatedID[]
                                    {
                                        new OrderKeysTypeAssociatedID
                                        {
                                            OfferItemID = new ItemID_Type()
                                            {
                                                refs = session.CurrentFlow.RebookingType == Ubimecs.Infrastructure.Models.Flow.RebookingTypeEnum.AddFlight ?
                                                        IbsUtility.ConcatIds(new string[]{ seat.PassengerId, segment.Id })
                                                        :IbsUtility.ConcatIds(new string[]{ seat.PassengerId, (!string.IsNullOrEmpty(segment.LocalId) ? segment.LocalId : segment.Id)}),
                                                Value = offerItem.OfferItemID,
                                                Owner = GeneralConstants.BOOKING_OWNER,
                                                ObjectKey = "V1_SEAT"
                                            }
                                        }
                                    }
                                }
                            },
                            SeatQualifier = new SeatQualifierAssignment[]
                            {
                                new SeatQualifierAssignment()
                                {
                                    refs = "V1_SEAT",
                                    Location = new SeatLocationType()
                                    {
                                        Column = seat.Column,
                                        Row = new SeatLocationTypeRow()
                                        {
                                            Number = new SeatMapRowNbrType()
                                            {
                                                Value = seat.Number
                                            }
                                        }
                                    },
                                    SeatAssociation = new SeatAssociation()
                                    {
                                        SegmentReferences = new SegmentReferences()
                                        {
                                            OnPoint = segment.DepartureCode,
                                            OffPoint = segment.ArrivalCode,
                                            Value = session.CurrentFlow.RebookingType == Ubimecs.Infrastructure.Models.Flow.RebookingTypeEnum.AddFlight ? segment.Id : (!string.IsNullOrEmpty(segment.LocalId) ? segment.LocalId : segment.Id)
                                        },
                                        PassengerReference = seat.PassengerId
                                    }
                            }
                            }
                        };

                        offers.Add(addQualifier);
                    }
                }
            }
        }
        public static void LoadBaggages(SessionCache session,
            ref List<ServiceDefinitionType> services,
            ref List<OrderReshopRQQueryReshopOrderServicingAddQualifier> offers,
            ref List<OrderReshopRQDataListsBaggageAllowance> baggageAllowances)
        {
            var baggages = session.CurrentFlow.PNR.ExtraBaggages;

            if (baggages != null)
            {
                List<BaggageItemType> BaggageList = new List<BaggageItemType>();

                foreach (var baggageInfo in baggages.Where(w => w.ExtraWeight > 0))
                {
                    var segmentRefs = session.CurrentFlow.RebookingType == Ubimecs.Infrastructure.Models.Flow.RebookingTypeEnum.AddFlight ?
                                        string.Join(" ", session.CurrentFlow.PNR.Flights.FirstOrDefault(f => f.TmobId == baggageInfo.TmobId).Segments.Select(t => t.Id)) :
                                        string.Join(" ",
                                                 session.CurrentFlow.PNR.Flights.FirstOrDefault(f => f.TmobId == baggageInfo.TmobId).Segments.Select(t => t.LocalId).All(t => !string.IsNullOrEmpty(t)) ?
                                                 session.CurrentFlow.PNR.Flights.FirstOrDefault(f => f.TmobId == baggageInfo.TmobId).Segments.Select(t => t.LocalId) : session.CurrentFlow.PNR.Flights.FirstOrDefault(f => f.TmobId == baggageInfo.TmobId).Segments.Select(t => t.Id));

                    var chargeResponse = session.CurrentFlow.GetIbsData<BaggageChargesRS>(IbsDataTypeEnum.BaggageCharge.ToString() + "_" + baggageInfo.TmobId);

                    var baggageServices = chargeResponse.GetServices();
                    var checkedBags = chargeResponse.GetGetCheckedBags();

                    var baggageService = baggageServices
                        .Where(f => (f.Associations?.FirstOrDefault()?.Passenger?.Item as string) == baggageInfo.PassengerId)
                        .FirstOrDefault(o => (checkedBags.FirstOrDefault(f => f.refs == o.ObjectKey)?.WeightAllowance?.MaximumWeight?.FirstOrDefault()?.Value ?? 0) == baggageInfo.ExtraWeight);

                    var checkedBag = checkedBags.FirstOrDefault(f => f.refs == baggageService.ObjectKey);

                    ServiceDefinitionType serviceDefinitionType = new ServiceDefinitionType()
                    {
                        ItemElementName = ItemChoiceType8.BaggageAllowanceRef,
                        Item = baggageService.ServiceID.Value.Replace("SSR.", "BAG."),
                        ServiceDefinitionID = baggageService.ServiceID.Value,
                        Name = new ServiceDefinitionTypeName()
                        {
                            Value = baggageService.Name.Value
                        },
                        Encoding = new ServiceEncodingType()
                        {
                            RFIC = baggageService.Encoding.RFIC,
                            Code = new ServiceEncodingTypeCode()
                            {
                                Value = baggageService.Encoding.Code.Value
                            },
                            SubCode = new ServiceEncodingTypeSubCode()
                            {
                                Value = baggageService.Encoding.SubCode.Value
                            }
                        },
                        Descriptions = new ServiceDescriptionType()
                        {
                            Description = new ServiceDescriptionTypeDescription[]
                            {
                                new ServiceDescriptionTypeDescription()
                                {
                                    Text = new DescriptionTypeText()
                                    {
                                        Value = baggageService.Descriptions.Description.FirstOrDefault().Text.Value
                                    },
                                    Application = baggageService.Descriptions.Description.FirstOrDefault().Application
                                }
                            }
                        }
                    };
                    services.Add(serviceDefinitionType);

                    OrderReshopRQQueryReshopOrderServicingAddQualifier addQualifier = new OrderReshopRQQueryReshopOrderServicingAddQualifier()
                    {
                        ExistingOrderQualifier = new ExistingOrderQualifier
                        {
                            PassengerReferences = baggageInfo.PassengerId,
                            OrderKeys = new OrderKeysType
                            {
                                OrderID = new OrderID_Type()
                                {
                                    Value = session.CurrentFlow.PNR.Number,
                                    Owner = GeneralConstants.BOOKING_OWNER
                                },
                                AssociatedIDs = new OrderKeysTypeAssociatedID[]
                                     {
                                        new OrderKeysTypeAssociatedID
                                        {
                                            OfferItemID = new ItemID_Type()
                                            {
                                                refs = IbsUtility.ConcatIds(new string[]{  baggageInfo.PassengerId, segmentRefs, baggageService.ServiceID.Value}),
                                                Value = baggageService.ServiceID.Value.Replace("V1_SSR","V1_OFFERITEM"),
                                                Owner = GeneralConstants.BOOKING_OWNER
                                            }
                                        }
                                     }
                            }
                        },
                    };

                    offers.Add(addQualifier);

                    baggageAllowances.Add(new OrderReshopRQDataListsBaggageAllowance()
                    {
                        BaggageAllowanceID = "V1_BAG." + baggageService.ServiceID.Value.Replace("V1_SSR.", ""),
                        BaggageCategory = BaggageCategoryListType.Checked,
                        WeightAllowance = checkedBag.WeightAllowance
                    });
                }
            }
        }
        public static void LoadSportEquipments(SessionCache session, ref List<ServiceDefinitionType> services, ref List<OrderReshopRQQueryReshopOrderServicingAddQualifier> offers)
        {
            var sportEquipments = session.CurrentFlow.PNR.SportEquipments;

            if (sportEquipments != null)
            {
                foreach (var sportEquipment in sportEquipments)
                {
                    LoadBaseService(session, ref services, ref offers, sportEquipment.PassengerId, sportEquipment.FlightTmobId, sportEquipment.ServiceId, sportEquipment.Count);
                }
            }
        }

        public static void LoadCorona(SessionCache session, ref List<ServiceDefinitionType> services, ref List<OrderReshopRQQueryReshopOrderServicingAddQualifier> offers)
        {
            var coronas = session.CurrentFlow.PNR.Coronas;

            if (coronas != null)
            {
                foreach (var corona in coronas)
                {
                    var sessionServices = session.CurrentFlow.Services.Services;
                    var segmentRefs = session.CurrentFlow.PNR.Flights.FirstOrDefault(f => f.TmobId == corona.TmobId).SegmentRefs;
                    foreach (var passengerId in corona.PassengerIds)
                    {
                        var coronaService = sessionServices[ServiceCategoryEnum.CORONA]
                                                .Select(s => Ubimecs.Infrastructure.Utilities.Utility.DeserializeObject<BaseServiceDTO>(s))
                                                .FirstOrDefault(f => f.PassengerIds.Contains(passengerId) && IbsUtility.CheckIdsEqual(f.SegmentIds, IbsUtility.SplitIds(segmentRefs)));

                        if (coronaService != null)
                        {
                            LoadBaseService(session, ref services, ref offers, passengerId, corona.TmobId, coronaService.Id, 1);
                        }
                    }
                }
            }
        }
        public static void LoadMeals(SessionCache session, ref List<ServiceDefinitionType> services, ref List<OrderReshopRQQueryReshopOrderServicingAddQualifier> offers)
        {
            var meals = session.CurrentFlow.PNR.Meals;

            if (meals != null)
            {
                foreach (var meal in meals)
                {
                    if (GeneralMappers.FindMealParentService(session, meal.ServiceId) == null && CheckIfItIsExistInServiceListForSegment(session, meal))
                    {
                        LoadBaseServiceBySegment(session, ref services, ref offers, meal.PassengerId, meal.SegmentTmobId, meal.ServiceId, 1);
                    }

                }
            }
        }

        private static bool CheckIfItIsExistInServiceListForSegment(SessionCache session, PassengerFlightMeal meal)
        {
            var services = session.CurrentFlow.Services.Services;
            var segmentRefs = session.CurrentFlow.PNR?.Flights?.SelectMany(s => s.Segments).FirstOrDefault(f => f.TmobId == meal.SegmentTmobId).Id;
            var mealCode = session.CurrentFlow.PnrInfoResponse?.Services?.Meals?.FirstOrDefault(t => t.Id == meal.ServiceId && t.SegmentIds.Contains(segmentRefs))?.Code;

            //Bu durumda önceden alınmamış henüz alınıyor demektir.
            if (mealCode == null) return true;

            var meals = services[ServiceCategoryEnum.MEALS]
                .Select(s => Ubimecs.Infrastructure.Utilities.Utility.DeserializeObject<BaseServiceDTO>(s))
                .ToList();
            var segmentMeals = meals.Where(t => t.SegmentIds.Contains(segmentRefs));
            foreach (var service in segmentMeals)
            {
                if (service.Code == mealCode)
                {
                    return true;
                }
            }
            return false;

        }

        public static void LoadIfeServices(SessionCache session, ref List<ServiceDefinitionType> services, ref List<OrderReshopRQQueryReshopOrderServicingAddQualifier> offers)
        {
            var ifes = session.CurrentFlow.PNR.IfeServices;

            if (ifes != null)
            {
                foreach (var ife in ifes)
                {
                    var sessionServices = session.CurrentFlow.Services.Services;
                    //var segmentRefs = session.CurrentFlow.PNR.Flights.FirstOrDefault(f => f.TmobId == ife.TmobId).SegmentRefs;
                    var segmentRefs = session.CurrentFlow.PNR.Flights.SelectMany(t => t.Segments).FirstOrDefault(k => k.TmobId == ife.TmobId).Id;
                    foreach (var passengerInfo in ife.Ifes)
                    {
                        if (sessionServices != null && sessionServices.Count > 0)
                        {
                            var ifeService = sessionServices[ServiceCategoryEnum.IFE]
                                            .Select(s => Ubimecs.Infrastructure.Utilities.Utility.DeserializeObject<BaseServiceDTO>(s))
                                            .FirstOrDefault(f => f.PassengerIds.Contains(passengerInfo.PassengerId) && f.SegmentIds.Any(t => segmentRefs.Split(' ').ToList().Contains(t)));
                            if (ifeService != null)
                            {
                                LoadBaseService(session, ref services, ref offers, passengerInfo.PassengerId, ife.TmobId, ifeService.Id, 1);
                            }
                        }
                    }
                }
            }
        }

        public static void LoadGolfBundles(SessionCache session, ref List<ServiceDefinitionType> services, ref List<OrderReshopRQQueryReshopOrderServicingAddQualifier> offers)
        {
            var golfBundles = session.CurrentFlow.PNR.GolfBundles;

            if (golfBundles != null)
            {
                foreach (var bundle in golfBundles)
                {
                    var sessionServices = session.CurrentFlow.Services.Services;
                    var segmentRefs = session.CurrentFlow.PNR.Flights.FirstOrDefault(f => f.TmobId == bundle.TmobId).SegmentRefs;
                    foreach (var passengerInfo in bundle.PassengerIds)
                    {
                        if (sessionServices != null && sessionServices.Count > 0)
                        {
                            var golfBundle = sessionServices[ServiceCategoryEnum.GOLF_BUNDLE]
                                            .Select(s => Ubimecs.Infrastructure.Utilities.Utility.DeserializeObject<BaseServiceDTO>(s))
                                            .FirstOrDefault(f => f.PassengerIds.Contains(passengerInfo) && f.SegmentIds.Any(t => segmentRefs.Split(' ').ToList().Contains(t)));
                            if (golfBundle != null)
                            {
                                LoadBaseService(session, ref services, ref offers, passengerInfo, bundle.TmobId, golfBundle.Id, 1);
                            }
                        }
                    }
                }
            }
        }


        public static void LoadBaseService(SessionCache session, ref List<ServiceDefinitionType> services, ref List<OrderReshopRQQueryReshopOrderServicingAddQualifier> offers, string passengerId, string flightTmobId, string serviceId, int count = 1, string refs = null)
        {
            var ibsBundleObject = GeneralMappers.GetIbsService(session, serviceId);
            var ibsOffer = GeneralMappers.GetIbsServiceOfferItem(session, serviceId);

            if (refs != null)
            {
                ibsBundleObject.Encoding.refs = refs;
            }

            if (CachedData.ServiceCodeCategories[ibsBundleObject.Encoding.Code.Value] == ServiceCategoryEnum.GOLF_BUNDLE)
            {
                ibsBundleObject.Item = null;
            }


            services.Add(ibsBundleObject);
            string segmentRefs = string.Empty;
            //Bu kısım direk olarak uçuş bazlı eklemelerde kullanılıyor...
            if (session.CurrentFlow.PNR.Flights.FirstOrDefault(f => f.TmobId == flightTmobId) != null)
            {
                segmentRefs = session.CurrentFlow.RebookingType == Ubimecs.Infrastructure.Models.Flow.RebookingTypeEnum.AddFlight ?
                     String.Join(" ", session.CurrentFlow.PNR.Flights.FirstOrDefault(f => f.TmobId == flightTmobId).Segments.Select(t => t.Id).ToList())
                     : (session.CurrentFlow.PNR.Flights.FirstOrDefault(f => f.TmobId == flightTmobId).Segments.Select(t => t.LocalId).ToList().Count > 0 && session.CurrentFlow.PNR.Flights.FirstOrDefault(f => f.TmobId == flightTmobId).Segments.Select(t => t.LocalId).ToList().All(a => !string.IsNullOrEmpty(a)) ? String.Join(" ", session.CurrentFlow.PNR.Flights.FirstOrDefault(f => f.TmobId == flightTmobId).Segments.Select(t => t.LocalId).ToList()) : String.Join(" ", session.CurrentFlow.PNR.Flights.FirstOrDefault(f => f.TmobId == flightTmobId).Segments.Select(t => t.Id).ToList()));
            }
            //Bu kısım segment bazlı eklemelerde kullanılıyor...
            else
            {
                //if (session.CurrentFlow.PNR.Flights.FirstOrDefault(t => t.Segments.Any(k => k.TmobId == flightTmobId)).State != OfferRebookingStateEnum.NotChanged)
                //{
                //    segmentRefs = session.CurrentFlow.PNR?.Flights?.SelectMany(t => t.Segments).FirstOrDefault(f => f.TmobId == flightTmobId).Id ??
                //                  session.CurrentFlow.PNR?.Flights?.SelectMany(t => t.Segments).FirstOrDefault(f => f.TmobId == flightTmobId).LocalId;
                //}
                //else
                //{
                segmentRefs = session.CurrentFlow.RebookingType == Ubimecs.Infrastructure.Models.Flow.RebookingTypeEnum.AddFlight ?
                    session.CurrentFlow.PNR?.Flights?.SelectMany(t => t.Segments).FirstOrDefault(f => f.TmobId == flightTmobId).Id
                    : (!string.IsNullOrEmpty(session.CurrentFlow.PNR?.Flights?.SelectMany(t => t.Segments).FirstOrDefault(f => f.TmobId == flightTmobId).LocalId) ?
                    session.CurrentFlow.PNR?.Flights?.SelectMany(t => t.Segments).FirstOrDefault(f => f.TmobId == flightTmobId).LocalId :
                    session.CurrentFlow.PNR?.Flights?.SelectMany(t => t.Segments).FirstOrDefault(f => f.TmobId == flightTmobId).Id);
                //}
            }

            offers.AddRange(new int[count].Select(s =>
                new OrderReshopRQQueryReshopOrderServicingAddQualifier()
                {
                    ExistingOrderQualifier = new ExistingOrderQualifier
                    {
                        PassengerReferences = passengerId,
                        OrderKeys = new OrderKeysType
                        {
                            OrderID = new OrderID_Type()
                            {
                                Value = session.CurrentFlow.PNR.Number,
                                Owner = GeneralConstants.BOOKING_OWNER
                            },
                            AssociatedIDs = new OrderKeysTypeAssociatedID[]
                                     {
                                        new OrderKeysTypeAssociatedID
                                        {
                                            OfferItemID = new ItemID_Type()
                                            {
                                                refs = IbsUtility.ConcatIds(new string[]{ passengerId, segmentRefs, serviceId}),
                                                Value = ibsOffer.OfferItemID,
                                                Owner = GeneralConstants.BOOKING_OWNER
                                            }
                                        }
                                     }
                        }
                    }
                })
            );


            session.CurrentFlow.ManageBookingServiceData.Add(new Ubimecs.Infrastructure.Models.Flow.ManageBookingServiceData
            {
                EncodingCode = ibsBundleObject.Encoding.Code.Value,
                ServiceId = ibsBundleObject.ServiceDefinitionID,
                Pax = passengerId,
                SegmentKey = segmentRefs
            });

        }
        public static void LoadBaseServiceBySegment(SessionCache session, ref List<ServiceDefinitionType> services, ref List<OrderReshopRQQueryReshopOrderServicingAddQualifier> offers, string passengerId, string segmentTmobId, string serviceId, int count = 1)
        {
            var ibsBundleObject = GeneralMappers.GetIbsService(session, serviceId);
            var ibsOffer = GeneralMappers.GetIbsServiceOfferItem(session, serviceId);


            services.Add(ibsBundleObject);

            var segmentRefs = session.CurrentFlow.RebookingType == Ubimecs.Infrastructure.Models.Flow.RebookingTypeEnum.AddFlight
                            ? session.CurrentFlow.PNR.Flights.SelectMany(s => s.Segments).FirstOrDefault(f => f.TmobId == segmentTmobId).Id
                            : (
                            !string.IsNullOrEmpty(session.CurrentFlow.PNR.Flights.SelectMany(s => s.Segments).FirstOrDefault(f => f.TmobId == segmentTmobId).LocalId)
                             ? session.CurrentFlow.PNR.Flights.SelectMany(s => s.Segments).FirstOrDefault(f => f.TmobId == segmentTmobId).LocalId
                             : session.CurrentFlow.PNR.Flights.SelectMany(s => s.Segments).FirstOrDefault(f => f.TmobId == segmentTmobId).Id);

            offers.AddRange(new int[count].Select(s =>
                new OrderReshopRQQueryReshopOrderServicingAddQualifier()
                {
                    ExistingOrderQualifier = new ExistingOrderQualifier
                    {
                        PassengerReferences = passengerId,
                        OrderKeys = new OrderKeysType
                        {
                            OrderID = new OrderID_Type()
                            {
                                Value = session.CurrentFlow.PNR.Number,
                                Owner = GeneralConstants.BOOKING_OWNER
                            },
                            AssociatedIDs = new OrderKeysTypeAssociatedID[]
                                {
                                new OrderKeysTypeAssociatedID
                                {
                                    OfferItemID = new ItemID_Type()
                                    {
                                        refs = IbsUtility.ConcatIds(new string[]{ passengerId, segmentRefs, serviceId}),
                                        Value = ibsOffer.OfferItemID,
                                        Owner = GeneralConstants.BOOKING_OWNER
                                    }
                                }
                                }
                        }
                    },
                })
            );

            session.CurrentFlow.ManageBookingServiceData.Add(new Ubimecs.Infrastructure.Models.Flow.ManageBookingServiceData
            {
                EncodingCode = ibsBundleObject.Encoding.Code.Value,
                ServiceId = ibsBundleObject.ServiceDefinitionID,
                Pax = passengerId,
                SegmentKey = segmentRefs
            });
        }


        public static void LoadBundleFromServiceList(SessionCache session, ref List<ServiceDefinitionType> services, ref List<OrderReshopRQQueryReshopOrderServicingAddQualifier> offers, SessionPnrFlightDTO flight)
        {
            var sessionServices = session.CurrentFlow.Services.Services;

            var bundleServices = sessionServices[flight.SelectedBundle]
                                 .Select(s => Ubimecs.Infrastructure.Utilities.Utility.DeserializeObject<BaseServiceDTO>(s)).ToList();
            List<BaseServiceDTO> baseServices = new List<BaseServiceDTO>();

            //var removedFlightBundle = session.CurrentFlow.PNR?.Flights?.FirstOrDefault(t => t.State == OfferRebookingStateEnum.Removed)?.SelectedBundle;

            foreach (var item in flight.Segments)
            {
                var bundles = bundleServices.Where(f => f.SegmentIds.Contains(item.Id) || f.SegmentIds.Contains(item.Id)).ToList();
                bundles.ForEach(t => t.SegmentIds = new string[] { session.CurrentFlow.RebookingType == Ubimecs.Infrastructure.Models.Flow.RebookingTypeEnum.AddFlight ? item.Id : (!string.IsNullOrEmpty(item.LocalId) ? item.LocalId : item.Id) });
                if (bundles.Count > 0)
                {
                    baseServices.AddRange(bundles);
                }
            }

            baseServices = baseServices.Distinct().ToList();

            foreach (var baseService in baseServices)
            {
                var ibsBundleObject = GeneralMappers.GetIbsService(session, baseService.Id);
                var ibsBundleOfferObject = GeneralMappers.GetIbsServiceOfferItem(session, baseService.Id);
                //var segmentTmobIds = baseService.SegmentIds.Select(s => session.CurrentFlow.PNR.Flights.SelectMany(sm => sm.Segments).FirstOrDefault(f => f.Id == s)?.TmobId).ToList();

                PassengerFlightMeal passMeal = null;
                ServiceDefinitionType parentMeal = null;

                if (GeneralMappers.LoadSelectedBundleMeals(session, ref passMeal, ref parentMeal, baseService.PassengerIds.ToList(), baseService.SegmentIds.ToList()))
                {
                    ibsBundleObject.Item = new ServiceDefinitionTypeServiceBundle()
                    {
                        ServiceDefinitionRef = new ServiceDefinitionTypeServiceBundleServiceDefinitionRef[]
                        {
                            new ServiceDefinitionTypeServiceBundleServiceDefinitionRef()
                            {
                                Value = passMeal.ServiceId
                            }
                        }
                    };

                    services.Add(parentMeal);

                }
                else
                {
                    ibsBundleObject.Item = null;
                }

                services.Add(ibsBundleObject);

                foreach (var passenger in baseService.PassengerIds)
                {
                    if (session.CurrentFlow.PNR.Passengers.FirstOrDefault(f => f.Id == passenger).PassengerType != PassengerTypeEnum.INF /*&& !retainableBundle*/)
                    {
                        offers.Add(new OrderReshopRQQueryReshopOrderServicingAddQualifier
                        {
                            ExistingOrderQualifier = new ExistingOrderQualifier()
                            {
                                OrderKeys = new OrderKeysType()
                                {
                                    OrderID = new OrderID_Type()
                                    {
                                        Value = session.CurrentFlow.PNR.Number,
                                        Owner = GeneralConstants.BOOKING_OWNER
                                    },
                                    AssociatedIDs = new OrderKeysTypeAssociatedID[]
                                    {
                                    new OrderKeysTypeAssociatedID
                                    {
                                        OfferItemID = new ItemID_Type()
                                        {
                                            refs = passenger + " " + IbsUtility.ConcatIds(baseService.SegmentIds) + " " + baseService.Id,
                                            Value = ibsBundleOfferObject.OfferItemID,
                                            Owner = GeneralConstants.BOOKING_OWNER
                                        }
                                    }
                                    }
                                },
                                PassengerReferences = session.CurrentFlow.PNR.PassengerRefs
                            }
                        });
                    }
                }

            }


        }
        public static bool LoadBundleFromLastOrderReshop(SessionCache session, ref List<ServiceDefinitionType> services, ref List<OrderReshopRQQueryReshopOrderServicingAddQualifier> offers, SessionPnrFlightDTO flight)
        {
            var latestOrderReshopResponse = session.CurrentFlow.GetIbsData<OrderReshopRS>(IbsDataTypeEnum.LatestReshopResponse);
            var passengerIds = session.CurrentFlow.PNR.Passengers.Where(w => w.PassengerType != PassengerTypeEnum.INF).Select(s => s.Id).ToList();
            var segmentIds = flight.Segments.Select(s => s.Id).ToList();
            var localSegmentIds = flight.Segments.Where(k => k.LocalId != null).Select(s => s.LocalId).ToList();


            int totalOfferCounter = 0;
            List<OrderReshopRSResponseReshopOffersReshopOfferAddOfferItem> bundleOffers = new List<OrderReshopRSResponseReshopOffersReshopOfferAddOfferItem>();

            foreach (var pass in passengerIds)
            {
                if (latestOrderReshopResponse.Items != null)
                {
                    var passBundleOffers = latestOrderReshopResponse.GetOffers().Where(w => w.AddOfferItem != null).SelectMany(s => s.AddOfferItem)
                    .Where(w => w.Service?.FirstOrDefault()?.PassengerRefs.Contains(pass) == true)
                    .ToList();
                    var serviceDataList = latestOrderReshopResponse.GetDataList().ServiceDefinitionList;
                    //var serviceCategories = CachedData.ServiceCodeCategories;
                    if (serviceDataList != null && localSegmentIds.Any(t => t != null) && localSegmentIds.Count != 0)
                    {
                        var segmentPassBundleOffers = new List<OrderReshopRSResponseReshopOffersReshopOfferAddOfferItem>();
                        if (session.CurrentFlow.RebookingType == Ubimecs.Infrastructure.Models.Flow.RebookingTypeEnum.AddFlight)
                        {
                            segmentPassBundleOffers = passBundleOffers
                        .Where(w =>
                            ((IbsUtility.CheckIdsEqual(IbsUtility.SplitIds((w.Service?.FirstOrDefault()?.Item as OfferItemTypeServiceServiceDefinitionRef)?.SegmentRefs), segmentIds.ToArray()) ||
                            segmentIds.Contains((w.Service?.FirstOrDefault()?.Item as OfferItemTypeServiceServiceDefinitionRef)?.SegmentRefs))) &&
                            (CachedData.ServiceCodeCategories.FirstOrDefault(t => t.Key == serviceDataList.FirstOrDefault(k => k.ServiceDefinitionID == (w.Service?.FirstOrDefault()?.Item as OfferItemTypeServiceServiceDefinitionRef).Value).Encoding.Code.Value).Value == ServiceCategoryEnum.SUN_PREMIUM_BUNDLE ||
                            CachedData.ServiceCodeCategories.FirstOrDefault(t => t.Key == serviceDataList.FirstOrDefault(k => k.ServiceDefinitionID == (w.Service?.FirstOrDefault()?.Item as OfferItemTypeServiceServiceDefinitionRef).Value).Encoding.Code.Value).Value == ServiceCategoryEnum.SUN_CLASSIC_BUNDLE)
                        ).ToList();
                        }
                        else
                        {
                            segmentPassBundleOffers = passBundleOffers
                        .Where(w =>
                            ((IbsUtility.CheckIdsEqual(IbsUtility.SplitIds((w.Service?.FirstOrDefault()?.Item as OfferItemTypeServiceServiceDefinitionRef)?.SegmentRefs), localSegmentIds.ToArray()) ||
                            localSegmentIds.Contains((w.Service?.FirstOrDefault()?.Item as OfferItemTypeServiceServiceDefinitionRef)?.SegmentRefs))) &&
                            (CachedData.ServiceCodeCategories.FirstOrDefault(t => t.Key == serviceDataList.FirstOrDefault(k => k.ServiceDefinitionID == (w.Service?.FirstOrDefault()?.Item as OfferItemTypeServiceServiceDefinitionRef).Value).Encoding.Code.Value).Value == ServiceCategoryEnum.SUN_PREMIUM_BUNDLE ||
                            CachedData.ServiceCodeCategories.FirstOrDefault(t => t.Key == serviceDataList.FirstOrDefault(k => k.ServiceDefinitionID == (w.Service?.FirstOrDefault()?.Item as OfferItemTypeServiceServiceDefinitionRef).Value).Encoding.Code.Value).Value == ServiceCategoryEnum.SUN_CLASSIC_BUNDLE)
                        ).ToList();
                        }

                        var removedFlightBundle = session.CurrentFlow.PNR?.Flights?.FirstOrDefault(t => t.State == OfferRebookingStateEnum.Removed);

                        totalOfferCounter += segmentPassBundleOffers.Sum(s => IbsUtility.SplitIds((s.Service?.FirstOrDefault()?.Item as OfferItemTypeServiceServiceDefinitionRef)?.SegmentRefs).Length);
                        bundleOffers.AddRange(segmentPassBundleOffers);
                    }
                }

            }

            if (totalOfferCounter == passengerIds.Count * segmentIds.Count)
            {
                foreach (var item in bundleOffers)
                {
                    var serviceId = (item.Service?.FirstOrDefault()?.Item as OfferItemTypeServiceServiceDefinitionRef)?.Value;
                    var segmentId = (item.Service?.FirstOrDefault()?.Item as OfferItemTypeServiceServiceDefinitionRef)?.SegmentRefs;
                    if (session.CurrentFlow.RebookingType == Ubimecs.Infrastructure.Models.Flow.RebookingTypeEnum.AddFlight)
                    {
                        segmentId = String.Join(" ", session.CurrentFlow.PNR.Flights.FirstOrDefault(t => t.Segments.Any(k => k.LocalId == segmentId)).Segments.Select(t => t.Id).ToList());
                    }


                    var passId = item.Service?.FirstOrDefault()?.PassengerRefs;
                    var serviceItem = latestOrderReshopResponse.GetDataList().ServiceDefinitionList.FirstOrDefault(f => f.ServiceDefinitionID == serviceId);

                    PassengerFlightMeal passMeal = null;
                    ServiceDefinitionType parentMeal = null;

                    if (GeneralMappers.LoadSelectedBundleMeals(session, ref passMeal, ref parentMeal, IbsUtility.SplitIds(passId).ToList(), IbsUtility.SplitIds(segmentId).ToList()))
                    {
                        serviceItem.Item = new ServiceDefinitionTypeServiceBundle()
                        {
                            ServiceDefinitionRef = new ServiceDefinitionTypeServiceBundleServiceDefinitionRef[]
                            {
                                new ServiceDefinitionTypeServiceBundleServiceDefinitionRef()
                                {
                                    Value = parentMeal.ServiceDefinitionID
                                }
                            }
                        };

                        services.Add(parentMeal);

                    }
                    else
                    {
                        serviceItem.Item = null;
                    }


                    services.Add(serviceItem);
                    offers.Add(new OrderReshopRQQueryReshopOrderServicingAddQualifier
                    {
                        ExistingOrderQualifier = new ExistingOrderQualifier()
                        {
                            OrderKeys = new OrderKeysType()
                            {
                                OrderID = new OrderID_Type()
                                {
                                    Value = session.CurrentFlow.PNR.Number,
                                    Owner = GeneralConstants.BOOKING_OWNER
                                },
                                AssociatedIDs = new OrderKeysTypeAssociatedID[]
                                {
                                    new OrderKeysTypeAssociatedID
                                    {
                                        OfferItemID = new ItemID_Type()
                                        {
                                            refs = IbsUtility.ConcatIds(new string[]{ serviceId, segmentId, passId }),
                                            Value = item.OfferItemID,
                                            Owner = GeneralConstants.BOOKING_OWNER
                                        }
                                    }
                                }
                            },
                            PassengerReferences = session.CurrentFlow.PNR.PassengerRefs
                        }
                    });
                }

                return true;
            }
            else
            {
                return false;
            }
        }


        public static OrderReshopReqMetadataTypeOtherMetadata GetOtherMetadata(SessionCache session)
        {
            var installment = session.CurrentFlow.PNR.PaymentInfo.Installment;
            var expiryDate = session.CurrentFlow.PNR.PaymentInfo.Expiration;
            var cardHolderName = session.CurrentFlow.PNR.PaymentInfo.CardHolderName;
            var securityCode = session.CurrentFlow.PNR.PaymentInfo.SeriesCode;
            var result = new OrderReshopReqMetadataTypeOtherMetadata();
            result.Item = IbsUtility.OrderReshopInstallmentMetaData(installment, expiryDate, cardHolderName, securityCode);
            return result;
        }
        public static Prices GetPriceInfo(this OrderReshopRS response)
        {
            Prices prices = new Prices();

            var priceXmlElement = XElement.Parse(response.GetReponseObject().Metadata.PassengerMetadata.Select(s => s.AugmentationPoint).SelectMany(s => s.AugPoint)
                .FirstOrDefault(f => f.Any.LocalName.Equals("ItineraryAmountDetailAugPoint")).Any.OuterXml);


            var offers = response.GetOffers();

            var changeFees = offers
               ?.Where(w => w.AddOfferItem != null)
               ?.SelectMany(sm => sm.AddOfferItem)
               ?.Select(s => s.TotalPriceDetail)
               ?.Where(w => w.Fees != null)
               ?.Select(s => s.Fees)
               ?.Where(w => w.Breakdown != null)
               ?.SelectMany(sm => sm.Breakdown)
               ?.Where(row => row?.Designator?.Equals("CHG") == true)
               ?.ToList();

            prices.TotalAmountToBePaid = Math.Abs(IbsUtility.ConvertToDecimal(priceXmlElement.Elements().First(x => x.Name == "TotalAmountToBePaid").Value));
            prices.TotalAmountPaid = IbsUtility.ConvertToDecimal(priceXmlElement.Elements().First(x => x.Name == "TotalAmountPaid").Value);
            prices.NonRefundableComponents = IbsUtility.ConvertToDecimal(priceXmlElement.Elements().First(x => x.Name == "TotalAmount").Value);
            prices.TotalRefund = Math.Abs(prices.TotalAmountToBePaid);
            prices.TotalRebookFeeAmount = changeFees?.Sum(s => s.Amount.Value) ?? 0;

            return prices;
        }
        public static List<OrderReshopRQQueryReshopOrderServicingOrderItem> GetFlightDeleteItems(SessionCache session)
        {
            var retains = new List<OrderReshopRQQueryReshopOrderServicingOrderItem>();
            var removedFlight = session.CurrentFlow.PNR.Flights.FirstOrDefault(fl => fl.State == OfferRebookingStateEnum.Removed);

            if (removedFlight != null)
            {
                var orderViewRS = session.CurrentFlow.GetIbsData<OrderViewRS>(IbsDataTypeEnum.OrderRetrieve);
                var flightOrderItems = orderViewRS.GetFlightOrderItems();

                var oldFlightSegments = session.CurrentFlow.PnrInfoResponse.FlightDestionationList.FirstOrDefault(f => f.TmobId == removedFlight.TmobId).Segments.Select(s => s.Id).ToList();

                foreach (var item in flightOrderItems)
                {
                    var serviceIdsToRetain = item
                      .Service
                      .Where(w => w.Item is string)
                      .Where(w => !oldFlightSegments.Contains(w.Item.ToString()))
                      .Select(s => s.ServiceID)
                      .ToList();

                    retains.Add(new OrderReshopRQQueryReshopOrderServicingOrderItem()
                    {
                        OrderItemID = item.OrderItemID,
                        ServiceRetainRequestIDs = serviceIdsToRetain.Count > 0 ? string.Join(" ", serviceIdsToRetain.ToArray()) : null
                    });
                }

                return retains;
            }
            else
            {
                return new List<OrderReshopRQQueryReshopOrderServicingOrderItem>();
            }
        }
        public static OrderReshopRQQueryReshop GetSearchFlightQuery(SessionCache session, RebookingSearchFlightRequest request, string departure, string arrival)
        {
            var deletedFlights = GetFlightDeleteItems(session);
            return new OrderReshopRQQueryReshop()
            {
                Item = new OrderReshopRQQueryReshopOrderServicing()
                {
                    Add = new OrderReshopRQQueryReshopOrderServicingAdd()
                    {
                        FlightQuery = new OrderReshopRQQueryReshopOrderServicingAddFlightQuery()
                        {
                            Item = new OrderReshopRQQueryReshopOrderServicingAddFlightQueryOriginDestinations()
                            {
                                OriginDestination = new OrderReshopRQQueryReshopOrderServicingAddFlightQueryOriginDestinationsOriginDestination[]
                                {
                                    new OrderReshopRQQueryReshopOrderServicingAddFlightQueryOriginDestinationsOriginDestination()
                                    {
                                        Departure = new Departure()
                                        {
                                            AirportCode = new FlightDepartureTypeAirportCode()
                                            {
                                                Value = departure
                                            },
                                            Date = request.DepartureDate
                                        },
                                        Arrival = new FlightArrivalType()
                                        {
                                            AirportCode = new FlightArrivalTypeAirportCode()
                                            {
                                                Value = arrival
                                            },
                                            Date = request.DepartureDate
                                        }
                                    }
                                }
                            }
                        }
                    },
                    Delete = deletedFlights?.Count > 0 ? deletedFlights.ToArray() : null
                }
            };
        }
        public static void GetPaymentQualifierAddQuery(SessionCache session, ref List<OrderReshopRQQueryReshopOrderServicingAddQualifier> offers)
        {
            if (session.CurrentFlow?.PNR?.PaymentInfo != null
             && session.CurrentFlow?.PNR?.PaymentInfo?.CardNumber != null
             && session.CurrentFlow?.PNR?.PaymentInfo?.Installment > 1)
            {
                string cardType = "";
                switch (session.CurrentFlow.PNR.PaymentInfo.CardType)
                {
                    case "Bonus":
                        cardType = "BNS";
                        break;
                    case "Axess":
                        cardType = "AC";
                        break;
                    case "World":
                        cardType = "WC";
                        break;
                    case "Maximum":
                        cardType = "MX";
                        break;
                    default:
                        break;
                }
                offers.Add(new OrderReshopRQQueryReshopOrderServicingAddQualifier
                {
                    PaymentCardQualifiers = new CardQualifierType[]
                        {
                            new CardQualifierType
                            {
                             refs = "V1_CRD.123456",
                             IIN_Number = session.CurrentFlow.PNR.PaymentInfo.CardNumber,
                             Type = cardType,
                             Amount = new CurrencyAmountOptType
                             {
                                  Code = session.CurrentFlow.PNR.Currency,
                                  Value = session.CurrentFlow.LatestPriceResponse.TotalPrice
                             }
                            }
                        }
                });
            }
        }
        public static List<OrderReshopRQQueryReshopOrderServicingAddQualifier> GetFlightAddQuery(SessionCache session, OrderReshopRS searchFlightResponse)
        {
            var addedFlight = session.CurrentFlow.PNR.Flights.FirstOrDefault(fl => fl.State == OfferRebookingStateEnum.Added);

            if (addedFlight != null)
            {
                var flightKey = session.CurrentFlow.RebookingSearchFlightResponse.Flights.FirstOrDefault().FlightOptions.FirstOrDefault(f => f.TmobId == addedFlight.TmobId).Id;

                var flight = searchFlightResponse.GetOffers().FirstOrDefault(f => f.AddOfferItem?.FirstOrDefault()?.Service?.FirstOrDefault().Item?.ToString() == flightKey);

                return new List<OrderReshopRQQueryReshopOrderServicingAddQualifier>
                {
                    new OrderReshopRQQueryReshopOrderServicingAddQualifier()
                    {
                        ExistingOrderQualifier = new ExistingOrderQualifier()
                        {
                            OrderKeys = new OrderKeysType()
                            {
                                OrderID = new OrderID_Type()
                                {
                                    Value = session.CurrentFlow.PNR.Number,
                                    Owner = GeneralConstants.BOOKING_OWNER
                                },
                                AssociatedIDs = flight.AddOfferItem.Select(s => new OrderKeysTypeAssociatedID
                                {
                                    OfferItemID = new ItemID_Type()
                                    {
                                        Value = s.OfferItemID,
                                        Owner = GeneralConstants.BOOKING_OWNER
                                    }
                                }
                                ).ToArray()
                            },
                            PassengerReferences = session.CurrentFlow.PNR.PassengerRefs
                        }
                    }
                };
            }
            else
            {
                return new List<OrderReshopRQQueryReshopOrderServicingAddQualifier>();
            }

        }
        public static List<OrderReshopRQQueryReshopOrderServicingOrderItem> GetServiceDeleteItems(SessionCache session)
        {
            var deletes = new List<OrderReshopRQQueryReshopOrderServicingOrderItem>();
            var removedFlight = session.CurrentFlow.PNR.Flights.FirstOrDefault(fl => fl.State == OfferRebookingStateEnum.Removed || fl.State == OfferRebookingStateEnum.Upgraded);
            var addedFlight = session.CurrentFlow.PNR.Flights?.FirstOrDefault(t => t.State == OfferRebookingStateEnum.Added);
            var extraWeightExist = session.CurrentFlow.PNR.ExtraBaggages.Where(t => t.ExtraWeight != decimal.Zero).ToList().Count > 0;
            var serviceChangingFlight = session.CurrentFlow.PNR.Flights?.FirstOrDefault(t => t.State == OfferRebookingStateEnum.ServiceChanging);
            var upgradeFlight = session.CurrentFlow.PNR.Flights?.FirstOrDefault(t => t.State == OfferRebookingStateEnum.Upgraded);
            var extraBaggageTaken = session.CurrentFlow.PnrInfoResponse?.Services?.Baggages?.Any(t => t.ExtraBaggage != decimal.Zero) ?? false;
            var alreadyTakenSeat = session.CurrentFlow.PnrInfoResponse?.Services?.Seats?.Any(t => t.Id != null) ?? false;

            if ((extraWeightExist && extraBaggageTaken) || (serviceChangingFlight != null && extraWeightExist))
            {
                var getPnrInfoResponse = session.CurrentFlow.PnrInfoResponse.Services.Baggages;

                var orderViewRS = session.CurrentFlow.GetIbsData<OrderViewRS>(IbsDataTypeEnum.OrderRetrieve);
                string[] extraBaggageTakenPassengerIds = default;

                if (extraBaggageTaken)
                {
                    if (serviceChangingFlight != null)
                    {
                        extraBaggageTakenPassengerIds = session.CurrentFlow?
                                                           .PNR
                                                           .ExtraBaggages?
                                                           .Where(t => t.TmobId == serviceChangingFlight.TmobId)
                                                           .Select(a => a.PassengerId)?
                                                           .ToArray();
                    }
                    if (upgradeFlight != null)
                    {
                        extraBaggageTakenPassengerIds = session.CurrentFlow?
                                                           .PNR
                                                           .ExtraBaggages?
                                                           .Where(t => t.TmobId == upgradeFlight.TmobId)
                                                           .Select(a => a.PassengerId)?
                                                           .ToArray();
                    }
                }
                if (extraBaggageTakenPassengerIds != null)
                {
                    foreach (var passId in extraBaggageTakenPassengerIds)
                    {
                        var baggageServiceId = getPnrInfoResponse.FirstOrDefault(t => t.PassengerIds.Contains(passId)
                                                                                   && t.ExtraBaggage != decimal.Zero)?.Id;
                        List<OrderItemTypeOrderItem> baggagesOfferForDelete = new List<OrderItemTypeOrderItem>();
                        if (!string.IsNullOrEmpty(baggageServiceId))
                        {
                            if (serviceChangingFlight != null)
                            {
                                baggagesOfferForDelete = orderViewRS.GetOrders()
                                               .Where(w => w.Service?.FirstOrDefault()?.Item is OrderItemTypeOrderItemServiceServiceDefinitionRef)
                                               .Where(w =>
                                                    serviceChangingFlight.SegmentRefs.Split(' ').Any(a => (w.Service?.FirstOrDefault()?.Item as OrderItemTypeOrderItemServiceServiceDefinitionRef).SegmentRef.Split(' ').Contains(a))
                                                    &&
                                                    baggageServiceId.Contains((w.Service?.FirstOrDefault()?.Item as OrderItemTypeOrderItemServiceServiceDefinitionRef).Value))
                                               .ToList();
                            }
                            else if (extraBaggageTaken)
                            {
                                baggagesOfferForDelete = orderViewRS.GetOrders()
                                               .Where(w => w.Service?.FirstOrDefault()?.Item is OrderItemTypeOrderItemServiceServiceDefinitionRef)
                                               .Where(w =>
                                                    removedFlight.SegmentRefs.Split(' ').Any(a => (w.Service?.FirstOrDefault()?.Item as OrderItemTypeOrderItemServiceServiceDefinitionRef).SegmentRef.Split(' ').Contains(a)) &&
                                                    baggageServiceId.Contains((w.Service?.FirstOrDefault()?.Item as OrderItemTypeOrderItemServiceServiceDefinitionRef).Value))
                                               .ToList();
                            }



                            foreach (var item in baggagesOfferForDelete)
                            {
                                deletes.Add(new OrderReshopRQQueryReshopOrderServicingOrderItem()
                                {
                                    OrderItemID = item.OrderItemID
                                });
                            }
                        }
                    }
                }
            }



            if (alreadyTakenSeat)
            {
                SessionPnrFlightDTO upgradedFlight = new SessionPnrFlightDTO();
                List<string> seatsOfferToDelete = new List<string>();
                //List<Model.DTO.Seat.SegmentPassengerSeat> newSeatTaken = new List<Model.DTO.Seat.SegmentPassengerSeat>();
                //if (serviceChangingFlight != null)
                //{
                //    newSeatTaken = session.CurrentFlow.PNR.Seats.Where(t => serviceChangingFlight.Segments.Select(k => k.TmobId).Contains(t.SegmentTmobId)).ToList();
                //}
                //else if (session.CurrentFlow.PNR.Flights.Any(t => t.State == OfferRebookingStateEnum.Upgraded))
                //{
                //    upgradedFlight = session.CurrentFlow.PNR.Flights.FirstOrDefault(t => t.State == OfferRebookingStateEnum.Upgraded);
                //    newSeatTaken = session.CurrentFlow.PNR.Seats.Where(t => session.CurrentFlow.PNR.Flights.FirstOrDefault(w => w.State == OfferRebookingStateEnum.Upgraded).Segments.Select(k => k.TmobId).Contains(t.SegmentTmobId)).ToList();
                //}
                foreach (var newSeatTaken in session.CurrentFlow.PNR.Seats)
                {
                    if (newSeatTaken != null)
                    {
                        var passengerIdsNewSeat = newSeatTaken.PassengerId;
                        var getPnrInfoResponseSeats = session.CurrentFlow.PnrInfoResponse.Services.Seats;
                        var orderViewRS = session.CurrentFlow.GetIbsData<OrderViewRS>(IbsDataTypeEnum.OrderRetrieve);
                        var theFlightSegment = session.CurrentFlow.PNR.Flights.SelectMany(t => t.Segments).FirstOrDefault(k => k.TmobId == newSeatTaken.SegmentTmobId);
                        if (getPnrInfoResponseSeats != null)
                        {

                            var seatServiceId = getPnrInfoResponseSeats.FirstOrDefault(t => t.PassengerIds.Contains(passengerIdsNewSeat) && t.SegmentIds.Contains(theFlightSegment.Id))?.Id ?? string.Empty;
                            if (!string.IsNullOrEmpty(seatServiceId))
                            {
                                seatsOfferToDelete.Add(orderViewRS.GetOrders()?
                                                                  .Where(w => w.Service?.FirstOrDefault()?.Item is OrderItemTypeOrderItemServiceSelectedSeat)
                                                                  .FirstOrDefault(w =>
                                                                    (w.Service?.FirstOrDefault()?.Item as OrderItemTypeOrderItemServiceSelectedSeat)
                                                                                                 .SegmentRef
                                                                                                 .ToString()
                                                                                                 .Split(' ').Contains(theFlightSegment.Id)
                                                               && seatServiceId.Contains(((w.Service?.FirstOrDefault()?.Item as OrderItemTypeOrderItemServiceSelectedSeat).ServiceDefinitionRef.ToString()))
                                           && w.Service?.FirstOrDefault().PassengerRef.Split(' ').Any(p => passengerIdsNewSeat.Contains(p)) == true).OrderItemID);
                            }
                        }
                    }
                }
                foreach (var item in seatsOfferToDelete)
                {
                    deletes.Add(new OrderReshopRQQueryReshopOrderServicingOrderItem()
                    {
                        OrderItemID = item
                    });
                }
            }

            if (removedFlight != null)
            {
                var removedFlightSegments = session.CurrentFlow.PNR.Flights.FirstOrDefault(t => t.TmobId == removedFlight.TmobId).Segments.Select(t => t.Id).ToList();
                if (removedFlight.SelectedBundle != ServiceCategoryEnum.SUN_ECO_BUNDLE)
                {
                    var orderViewRS = session.CurrentFlow.GetIbsData<OrderViewRS>(IbsDataTypeEnum.OrderRetrieve);
                    var getPnrInfoResponse = session.CurrentFlow.PnrInfoResponse;

                    //if (!Common.Statics.RetainableSSRs.Contains(removedFlight.SelectedBundle))
                    //{
                    var bundleServiceIds = getPnrInfoResponse.Services.Bundles.Where(w => removedFlight.Segments.Select(t => t.Id).ToList().Any(k => w.SegmentIds.Contains(k)) /*IbsUtility.CheckIdsEqual(w.SegmentIds, removedFlight.Segments.Select(s => s.Id).ToArray())*/)
                    .Select(s => s.Id);
                    var bundlesForDeletedFlight = orderViewRS.GetOrders()
                                                             .Where(w => w.Service?.FirstOrDefault()?.Item is OrderItemTypeOrderItemServiceServiceDefinitionRef)
                                                             .Where(w =>
                                                                    removedFlight.SegmentRefs.Contains((w.Service?.FirstOrDefault()?.Item as OrderItemTypeOrderItemServiceServiceDefinitionRef).SegmentRef) &&
                                                                    bundleServiceIds.Contains((w.Service?.FirstOrDefault()?.Item as OrderItemTypeOrderItemServiceServiceDefinitionRef).Value))
                                                             .ToList();
                    foreach (var item in bundlesForDeletedFlight)
                    {
                        deletes.Add(new OrderReshopRQQueryReshopOrderServicingOrderItem()
                        {
                            OrderItemID = item.OrderItemID
                        });
                    }
                    //}
                    if (addedFlight != null)
                    {
                        var newServiceList = session.CurrentFlow.Services.Services;
                        var servicesCanBeDeleted = session.CurrentFlow.PnrInfoResponse.Services.Meals.Where(t => t.SegmentIds.Intersect(removedFlightSegments).ToList().Count > 0);
                        var newServices = newServiceList[ServiceCategoryEnum.MEALS]
                                                        .Select(t => Ubimecs.Infrastructure.Utilities.Utility.DeserializeObject<BaseServiceDTO>(t)).ToList();


                        if (newServices != null)
                        {
                            foreach (var meal in servicesCanBeDeleted)
                            {
                                if (!newServices.Any(t => t.Code == meal.Code))
                                {
                                    var mealForDeletedFlight = orderViewRS.GetOrders()
                                                                     .Where(w => w.Service?.FirstOrDefault()?.Item is OrderItemTypeOrderItemServiceServiceDefinitionRef)
                                                                     .FirstOrDefault(w =>
                                                                            removedFlight.SegmentRefs.Contains((w.Service?.FirstOrDefault()?.Item as OrderItemTypeOrderItemServiceServiceDefinitionRef).SegmentRef) &&
                                                                            meal.Id.Contains((w.Service?.FirstOrDefault()?.Item as OrderItemTypeOrderItemServiceServiceDefinitionRef).Value));

                                    if (mealForDeletedFlight != null)
                                    {
                                        deletes.Add(new OrderReshopRQQueryReshopOrderServicingOrderItem()
                                        {
                                            OrderItemID = mealForDeletedFlight.OrderItemID
                                        });
                                    }
                                }
                            }
                        }
                    }

                    //#region Meals
                    //var deletingMeals = getPnrInfoResponse.Services.Meals.Where(w => removedFlight.Segments.Select(t => t.Id).Any(k => w.SegmentIds.Contains(k))).Select(s => s.Id);
                    //var deletingMealsOfferIds = orderViewRS.GetOrders()
                    //                                         .Where(w => w.Service?.FirstOrDefault()?.Item is OrderItemTypeOrderItemServiceServiceDefinitionRef)
                    //                                         .Where(w =>
                    //                                                removedFlight.SegmentRefs.Contains((w.Service?.FirstOrDefault()?.Item as OrderItemTypeOrderItemServiceServiceDefinitionRef).SegmentRef) &&
                    //                                                deletingMeals.Contains((w.Service?.FirstOrDefault()?.Item as OrderItemTypeOrderItemServiceServiceDefinitionRef).Value))
                    //                                         .ToList();
                    //#endregion

                    //#region SportEquipment
                    //var deletingSportEquipments = getPnrInfoResponse.Services.SportEquipments.Where(w => removedFlight.Segments.Select(t => t.Id).Any(k => w.SegmentIds.Contains(k))).Select(s => s.Id);
                    //var deletingSportEquipmentsOfferIds = orderViewRS.GetOrders()
                    //                                         .Where(w => w.Service?.FirstOrDefault()?.Item is OrderItemTypeOrderItemServiceServiceDefinitionRef)
                    //                                         .Where(w =>
                    //                                                removedFlight.SegmentRefs.Contains((w.Service?.FirstOrDefault()?.Item as OrderItemTypeOrderItemServiceServiceDefinitionRef).SegmentRef) &&
                    //                                                deletingSportEquipments.Contains((w.Service?.FirstOrDefault()?.Item as OrderItemTypeOrderItemServiceServiceDefinitionRef).Value))
                    //                                         .ToList();
                    //#endregion

                    //#region Coronas
                    //var deletingCoronas = getPnrInfoResponse.Services.CoronaServices.Where(w => removedFlight.Segments.Select(t => t.Id).Any(k => w.SegmentIds.Contains(k))).Select(s => s.Id);
                    //var deletingCoronasOfferIds = orderViewRS.GetOrders()
                    //                                         .Where(w => w.Service?.FirstOrDefault()?.Item is OrderItemTypeOrderItemServiceServiceDefinitionRef)
                    //                                         .Where(w =>
                    //                                                removedFlight.SegmentRefs.Contains((w.Service?.FirstOrDefault()?.Item as OrderItemTypeOrderItemServiceServiceDefinitionRef).SegmentRef) &&
                    //                                                deletingCoronas.Contains((w.Service?.FirstOrDefault()?.Item as OrderItemTypeOrderItemServiceServiceDefinitionRef).Value))
                    //                                         .ToList();
                    //#endregion

                    //#region Seats
                    //var deletingSeats = getPnrInfoResponse.Services.Seats.Where(w => removedFlight.Segments.Select(t => t.Id).Any(k => w.SegmentIds.Contains(k))).Select(s => s.Id);
                    //var deletingSeatsOfferIds = orderViewRS.GetOrders()
                    //                                         .Where(w => w.Service?.FirstOrDefault()?.Item is OrderItemTypeOrderItemServiceServiceDefinitionRef)
                    //                                         .Where(w =>
                    //                                                removedFlight.SegmentRefs.Contains((w.Service?.FirstOrDefault()?.Item as OrderItemTypeOrderItemServiceServiceDefinitionRef).SegmentRef) &&
                    //                                                deletingSeats.Contains((w.Service?.FirstOrDefault()?.Item as OrderItemTypeOrderItemServiceServiceDefinitionRef).Value))
                    //                                         .ToList();
                    //#endregion

                    //#region Others
                    //var deletingOthers = getPnrInfoResponse.Services.Others.Where(w => removedFlight.Segments.Select(t => t.Id).Any(k => w.SegmentIds.Contains(k))).Select(s => s.Id);
                    //var deletingOthersOfferIds = orderViewRS.GetOrders()
                    //                                         .Where(w => w.Service?.FirstOrDefault()?.Item is OrderItemTypeOrderItemServiceServiceDefinitionRef)
                    //                                         .Where(w =>
                    //                                                removedFlight.SegmentRefs.Contains((w.Service?.FirstOrDefault()?.Item as OrderItemTypeOrderItemServiceServiceDefinitionRef).SegmentRef) &&
                    //                                                deletingOthers.Contains((w.Service?.FirstOrDefault()?.Item as OrderItemTypeOrderItemServiceServiceDefinitionRef).Value))
                    //                                         .ToList();
                    //#endregion  

                    //foreach (var item in deletingMealsOfferIds)
                    //{
                    //    deletes.Add(new OrderReshopRQQueryReshopOrderServicingOrderItem()
                    //    {
                    //        OrderItemID = item.OrderItemID
                    //    });
                    //}
                    //foreach (var item in deletingSportEquipmentsOfferIds)
                    //{
                    //    deletes.Add(new OrderReshopRQQueryReshopOrderServicingOrderItem()
                    //    {
                    //        OrderItemID = item.OrderItemID
                    //    });
                    //}
                    //foreach (var item in deletingCoronasOfferIds)
                    //{
                    //    deletes.Add(new OrderReshopRQQueryReshopOrderServicingOrderItem()
                    //    {
                    //        OrderItemID = item.OrderItemID
                    //    });
                    //}
                    //foreach (var item in deletingSeatsOfferIds)
                    //{
                    //    deletes.Add(new OrderReshopRQQueryReshopOrderServicingOrderItem()
                    //    {
                    //        OrderItemID = item.OrderItemID
                    //    });
                    //}
                    //foreach (var item in deletingOthersOfferIds)
                    //{
                    //    deletes.Add(new OrderReshopRQQueryReshopOrderServicingOrderItem()
                    //    {
                    //        OrderItemID = item.OrderItemID
                    //    });
                    //}


                }
                if (removedFlight.SelectedBundle == ServiceCategoryEnum.SUN_ECO_BUNDLE
                 && session.CurrentFlow.PnrInfoResponse.Services.GolfBundles.Any(t => removedFlightSegments.Any(k => t.SegmentIds.Contains(k))))
                {
                    var orderViewRS = session.CurrentFlow.GetIbsData<OrderViewRS>(IbsDataTypeEnum.OrderRetrieve);
                    var getPnrInfoResponse = session.CurrentFlow.PnrInfoResponse;

                    var golfBundleServiceIds = getPnrInfoResponse.Services.GolfBundles.Where(t => removedFlightSegments.Any(k => t.SegmentIds.Contains(k))).Select(a => a.Id);
                    var golfBundleOfferItems = orderViewRS.GetOrders()
                        .Where(w => w.Service?.FirstOrDefault()?.Item is OrderItemTypeOrderItemServiceServiceDefinitionRef)
                        .Where(w => removedFlight.SegmentRefs.Contains((w.Service?.FirstOrDefault()?.Item as OrderItemTypeOrderItemServiceServiceDefinitionRef).SegmentRef)
                                 && golfBundleServiceIds.Contains((w.Service?.FirstOrDefault()?.Item as OrderItemTypeOrderItemServiceServiceDefinitionRef).Value))
                        .ToList();

                    foreach (var item in golfBundleOfferItems)
                    {
                        deletes.Add(new OrderReshopRQQueryReshopOrderServicingOrderItem()
                        {
                            OrderItemID = item.OrderItemID
                        });
                    }

                }



                return deletes;

            }
            else
            {
                if (deletes.Count == 0 || deletes == null)
                {
                    return new List<OrderReshopRQQueryReshopOrderServicingOrderItem>();
                }
                else
                {
                    return deletes;
                }
            }
        }

        public static object ConvertList(List<object> value, Type type)
        {
            var containedType = type.GenericTypeArguments.First();
            return value.Select(item => Convert.ChangeType(item, containedType)).ToList();
        }

        public static FareListFareGroup[] GetFares(SessionCache session)
        {
            List<FareListFareGroup> fares = session.CurrentFlow.GetIbsData<List<FareListFareGroup>>(IbsDataTypeEnum.FareGroupList);
            if (fares == null) fares = new List<FareListFareGroup>();
            fares.ForEach(f =>
            {
                f.Fare.FareDetail.Price.TotalAmount = null;
                f.Fare.FareDetail.Price.Surcharges = null;
                f.Fare.FareDetail.Price.Taxes = null;
                f.Fare.FareDetail.FareComponent.ToList().ForEach(k =>
                    {
                        k.Price.TotalAmount = null;
                        k.Price.Surcharges = null;
                        k.Price.Taxes = null;
                        k.FareBasis.RBD = null;
                        k.FareRules = new FareRulesType
                        {
                            Ticketing = new FareRulesTypeTicketing
                            {
                                TicketlessInd = false,
                                TicketlessIndSpecified = true

                            }
                        };
                        k.SegmentRefs = new FareComponentTypeSegmentRefs
                        {
                            Value = session.CurrentFlow.RebookingType == Ubimecs.Infrastructure.Models.Flow.RebookingTypeEnum.AddFlight ?
                                        string.Join(" ", session.CurrentFlow.PNR.Flights
                                                                            .FirstOrDefault(t => t.Segments.Any(w => k.SegmentRefs.Value.Split(' ').ToList().Contains(w.Id)))
                                                                            .Segments.Select(a => a.Id)) :
                                       (session.CurrentFlow.PNR.Flights
                                                                            .FirstOrDefault(t => t.Segments.Any(w => k.SegmentRefs.Value.Split(' ').ToList().Contains(w.Id)))
                                                                            .Segments.Select(a => a.LocalId).All(t => !string.IsNullOrEmpty(t)) ?
                                                                string.Join(" ", session.CurrentFlow.PNR.Flights
                                                                            .FirstOrDefault(t => t.Segments.Any(w => k.SegmentRefs.Value.Split(' ').ToList().Contains(w.Id)))
                                                                            .Segments.Select(a => a.LocalId)) : string.Join(" ", session.CurrentFlow.PNR.Flights
                                                                            .FirstOrDefault(t => t.Segments.Any(w => k.SegmentRefs.Value.Split(' ').ToList().Contains(w.Id)))
                                                                            .Segments.Select(a => a.Id)))
                        };
                    });


            });

            return fares?.ToArray();
        }
        public static ListOfFlightSegmentType[] GetFlightSegments(SessionCache session, OrderReshopRS searchFlightResponse, OrderViewRS orderViewRS)
        {
            List<ListOfFlightSegmentType> segments = new List<ListOfFlightSegmentType>();

            if (session.CurrentFlow.RebookingType == Ubimecs.Infrastructure.Models.Flow.RebookingTypeEnum.AddFlight)
            {
                var addedFlightTmobId = session.CurrentFlow.PNR.Flights.FirstOrDefault(f => f.State == OfferRebookingStateEnum.Added)?.TmobId;
                var pnrFlightSegments = session.CurrentFlow.PNR.Flights.FirstOrDefault(f => f.State == OfferRebookingStateEnum.Added)?.Segments;
                var addedFlight = session.CurrentFlow.RebookingSearchFlightResponse.Flights.SelectMany(s => s.FlightOptions).FirstOrDefault(f => f.TmobId == addedFlightTmobId);
                segments.AddRange(searchFlightResponse.GetFlightSegments(addedFlight.Id));
                if (pnrFlightSegments.Count > 0 && pnrFlightSegments.All(t => t.LocalId != null && t.Id != null))
                {
                    List<ListOfFlightSegmentType> newSegments = new List<ListOfFlightSegmentType>();
                    foreach (var segment in segments)
                    {
                        var newSegment = new ListOfFlightSegmentType()
                        {
                            Arrival = segment.Arrival,
                            ClassOfService = segment.ClassOfService,
                            ConnectInd = segment.ConnectInd,
                            ConnectIndSpecified = segment.ConnectIndSpecified,
                            Departure = segment.Departure,
                            ElectronicTicketInd = segment.ElectronicTicketInd,
                            ElectronicTicketIndSpecified = segment.ElectronicTicketIndSpecified,
                            Equipment = segment.Equipment,
                            FlightDetail = segment.FlightDetail,
                            MarketingCarrier = segment.MarketingCarrier,
                            OnTimePerformance = segment.OnTimePerformance,
                            OperatingCarrier = segment.OperatingCarrier,
                            refs = segment.refs,
                            SecureFlight = segment.SecureFlight,
                            SecureFlightSpecified = segment.SecureFlightSpecified,
                            SegmentKey = pnrFlightSegments.FirstOrDefault(k => k.Id == segment.SegmentKey).Id,
                            Settlement = segment.Settlement,
                            TicketlessInd = segment.TicketlessInd,
                            TicketlessIndSpecified = segment.TicketlessIndSpecified
                        };
                        newSegments.Add(newSegment);
                    }
                    //newSegments.ForEach(t => t.SegmentKey = pnrFlightSegments.FirstOrDefault(k => k.Id == t.SegmentKey).LocalId);
                    segments.RemoveAll(t => t != null);
                    segments.AddRange(newSegments);
                }
            }
            else if (session.CurrentFlow.RebookingType == Ubimecs.Infrastructure.Models.Flow.RebookingTypeEnum.ChangeFlight)
            {
                var addedFlightTmobId = session.CurrentFlow.PNR.Flights.FirstOrDefault(f => f.State == OfferRebookingStateEnum.Added)?.TmobId;
                var pnrFlightSegments = session.CurrentFlow.PNR.Flights.FirstOrDefault(f => f.State == OfferRebookingStateEnum.Added)?.Segments;
                var addedFlight = session.CurrentFlow.RebookingSearchFlightResponse.Flights.SelectMany(s => s.FlightOptions).FirstOrDefault(f => f.TmobId == addedFlightTmobId);
                segments.AddRange(searchFlightResponse.GetFlightSegments(addedFlight.Id));
                if (pnrFlightSegments.Count > 0 && pnrFlightSegments.All(t => t.LocalId != null && t.Id != null))
                {
                    List<ListOfFlightSegmentType> newSegments = new List<ListOfFlightSegmentType>();
                    foreach (var segment in segments)
                    {
                        var newSegment = new ListOfFlightSegmentType()
                        {
                            Arrival = segment.Arrival,
                            ClassOfService = segment.ClassOfService,
                            ConnectInd = segment.ConnectInd,
                            ConnectIndSpecified = segment.ConnectIndSpecified,
                            Departure = segment.Departure,
                            ElectronicTicketInd = segment.ElectronicTicketInd,
                            ElectronicTicketIndSpecified = segment.ElectronicTicketIndSpecified,
                            Equipment = segment.Equipment,
                            FlightDetail = segment.FlightDetail,
                            MarketingCarrier = segment.MarketingCarrier,
                            OnTimePerformance = segment.OnTimePerformance,
                            OperatingCarrier = segment.OperatingCarrier,
                            refs = segment.refs,
                            SecureFlight = segment.SecureFlight,
                            SecureFlightSpecified = segment.SecureFlightSpecified,
                            SegmentKey = pnrFlightSegments.FirstOrDefault(k => k.Id == segment.SegmentKey).Id,
                            Settlement = segment.Settlement,
                            TicketlessInd = segment.TicketlessInd,
                            TicketlessIndSpecified = segment.TicketlessIndSpecified
                        };
                        newSegments.Add(newSegment);
                    }
                    newSegments.ForEach(t => t.SegmentKey = pnrFlightSegments.FirstOrDefault(k => k.Id == t.SegmentKey).LocalId);
                    segments.RemoveAll(t => t != null);
                    segments.AddRange(newSegments);
                }
            }
            else if (session.CurrentFlow.RebookingType == Ubimecs.Infrastructure.Models.Flow.RebookingTypeEnum.AddExtras)
            {
                foreach (var item in session.CurrentFlow.PNR.Flights)
                {
                    segments.AddRange(orderViewRS.GetFlightSegments(item.Id));
                }
            }
            else if (session.CurrentFlow.RebookingType == Ubimecs.Infrastructure.Models.Flow.RebookingTypeEnum.UpgradeBundle)
            {
                foreach (var item in session.CurrentFlow.PNR.Flights.Where(w => w.State == OfferRebookingStateEnum.Upgraded))
                {
                    segments.AddRange(orderViewRS.GetFlightSegments(item.Id));
                }
            }
            else
            {

            }

            return segments.ToArray();
        }
        public static List<ListOfFlightSegmentType> GetSegments(this OrderReshopRS response, string segmentRefs)
        {
            return response.GetDataList()?.FlightSegmentList?
                .Where(f => IbsUtility.SplitIds(segmentRefs).Contains(f.SegmentKey))?
                .ToList()
                .GroupBy(g => g.SegmentKey)
                .Select(s => s.FirstOrDefault()) // groupBy yapıp ilk elemanların alınmasının sebebi, IBS'in response'ta aynı segment için duplicate FlightSegment objesi atıyor olması.
                .ToList();
        }
        public static OrderReshopRQDataListsBaggageAllowance[] GetBaggageAllowances()
        {
            return null;
        }
        public static List<OriginDestination> GetOriginDestinations(this OrderReshopRS response)
        {
            return response.GetDataList()?
                .OriginDestinationList?
                .ToList();
        }
        public static OrderReshopRSResponseDataLists GetDataList(this OrderReshopRS response)
        {
            return response.GetReponseObject()?.DataLists;
        }
        public static string GetFareBasisCode(this OrderReshopRS response, string segmentId, string passengerRefs)
        {
            var result = response.GetOffers()?
                .SelectMany(s => s.AddOfferItem)?
                .SelectMany(s => s.FareDetail)?
                .Where(w => w.PassengerRefs.Value == passengerRefs)?
                .SelectMany(s => s.FareComponent)?
                .Where(w => w.SegmentRefs.Value.Split(' ').Any(k => segmentId.Split(' ').Contains(k)))?
                .FirstOrDefault()?
                .FareBasis?
                .FareBasisCode?
                .Code?.ToString();

            return result;
        }
        public static string FlexHeader(this OrderReshopRS response, string flightId, List<string> flexTypes, LanguageEnum languageId)
        {
            var flexHeader = CachedData.GetCMS((int)languageId, "flex_header");

            var fareBasisCode = response.GetFareBasisCode(String.Join(" ", response.GetFlightSegments(flightId).Select(t => t.SegmentKey))
                                     , String.Join(" ", response.GetPassengers()?.Select(p => p.PassengerID))) ?? string.Empty;
            if (!string.IsNullOrEmpty(fareBasisCode))
            {
                return flexTypes.Contains(fareBasisCode) ? flexHeader : string.Empty;
            }
            else
            {
                return string.Empty;
            }
        }

        public static string[] FlexMessage(this OrderReshopRS response, string flightId, LanguageEnum languageId, List<string> flexMessages, List<string> flexTypes)
        {
            var fareBasisCode = response.GetFareBasisCode(String.Join(" ", response.GetFlightSegments(flightId).Select(t => t.SegmentKey))
                                     , String.Join(" ", response.GetPassengers()?.Select(p => p.PassengerID))) ?? string.Empty;
            if (!string.IsNullOrEmpty(fareBasisCode))
            {
                var flexTypeIndex = flexTypes.IndexOf(fareBasisCode);
                if (flexTypeIndex != -1)
                {
                    return flexMessages.ToArray();
                }
                else
                {
                    return default;
                }
            }
            else
            {
                return default;
            }

        }
        public static SearchFlightInfoDTO GetFlightInfoDTO(this OrderReshopRS response, OriginDestination odItem, int pricedPasengerCount, LanguageEnum languageId, List<string> flexTypes, List<string> flexMessages)
        {
            var result = new SearchFlightInfoDTO
            {
                Id = odItem.OriginDestinationKey,
                DepartureCode = odItem.DepartureCode.Value,
                ArrivalCode = odItem.ArrivalCode.Value,
                FlightOptions = odItem.FlightReferences?.Value?.Split(' ')?.Select(s => new SearchFlightFlightDTO
                {
                    Id = s,
                    Price = response.GetTotalAmountOfOffer(s)?.Value ?? 0,
                    Currency = response.GetTotalAmountOfOffer(s)?.Code,
                    JourneyTime = IbsUtility.GetTimeSpan(response.GetDataList().FlightList?.FirstOrDefault(f => f.FlightKey == s)?.Journey?.Time),
                    IsSoldOut = !response.GetFlightSegments(s).All(a => a.ClassOfService?.Code?.SeatsLeft >= pricedPasengerCount),
                    Segments = response.GetFlightSegments(s).Select(sl => sl.GetSegmentDTO(languageId)).ToList(),
                    BasePrice = response.GetTotalAmountOfOffer(s)?.Value ?? 0,
                    IsFlown = response.GetFlightSegments(s).Select(sl => sl.GetSegmentDTO(languageId)).Select(t => t.CompleteDepartureTime).Min() <= Ubimecs.Infrastructure.Utilities.Utility.GetLocalTimeByAirportCode(odItem.DepartureCode.Value),
                    FlexType = response.FlexHeader(s, flexTypes, languageId),
                    FlexMessage = response.FlexMessage(s, languageId, flexMessages, flexTypes)
                })?.ToList()
            };

            if (result.FlightOptions != null)
            {
                foreach (var item in result.FlightOptions)
                {
                    foreach (var segment in item.Segments)
                    {
                        segment.CarrierName = "SUNEXPRESS";

                        if (segment.AirlineId == "SM")
                        {
                            segment.CarrierName = "Air Cairo";

                        }
                        else if (response.GetDataList()?.DisclosureList?.Count() > 0)
                        {
                            segment.CarrierName =
                                response.GetDataList()?.DisclosureList?.FirstOrDefault(f => f.ListKey == item.Id)?.Description?.FirstOrDefault(row => row.Text.Value.Contains("Carrier Name"))?.Text?.Value?.Split(':')?.LastOrDefault() ?? "default_operated_by";
                        }
                    }
                }

                foreach (var item in result.FlightOptions.GroupBy(g => g.Segments?.FirstOrDefault()?.DepartureDate.Date).Select(s => s.Key))
                {
                    var minId = result.FlightOptions.Where(w => w.Segments.Min(m => m.DepartureDate.Date).Equals(item) && !w.IsSoldOut && !w.IsFlown).OrderBy(o => o.Price).ThenBy(t => t.Segments.FirstOrDefault()?.DepartureTime).FirstOrDefault().Id;

                    result.FlightOptions.FirstOrDefault(f => f.Id == minId).IsBestPrice = true;
                }

                result.FlightOptions = result.FlightOptions
                    .OrderBy(o => o.IsSoldOut ? 1 : 0)
                    .ThenBy(t => t.IsConnectedFlight ? 1 : 0)
                    .ThenBy(t => t.Price)
                    .ToList();
            }

            return result;
        }
        public static CurrencyAmountOptType GetTotalAmountOfOffer(this OrderReshopRS response, string flightKey)
        {
            var adtPassengers = IbsUtility.ConcatIds(response.GetPassengers().Where(p => p.PTC == PassengerTypeEnum.ADT.ToString()).Select(s => s.PassengerID).ToArray());

            var fareComponent = response.GetOfferItem(flightKey, true)?
                .AddOfferItem?
                .SelectMany(s => s.FareDetail)?
                .Where(w => w.PassengerRefs.Value == adtPassengers)
                .FirstOrDefault()?
                .FareComponent?
                .FirstOrDefault();

            return (fareComponent?.Price?.TotalAmount?.Item as DetailCurrencyPriceType)?.Item as CurrencyAmountOptType;
        }
        public static List<PassengerType> GetPassengers(this OrderReshopRS response)
        {
            return response.GetDataList()?.PassengerList?.ToList() ?? new List<PassengerType>();
        }
        public static OrderReshopRSResponseReshopOffersReshopOffer GetOfferItem(this OrderReshopRS response, string flightKey, bool returnFirstOffer = false)
        {
            var offers = response.GetOffers()?.Where(f => f.AddOfferItem?.FirstOrDefault()?.Service?.FirstOrDefault()?.Item is string offerItem && offerItem.Contains(flightKey));
            if (offers.Count() != 1)
            {
                return returnFirstOffer ? offers.FirstOrDefault() : null;
            }
            else
            {
                return offers.FirstOrDefault();
            }
        }
        public static List<OrderReshopRSResponseReshopOffersReshopOffer> GetOffers(this OrderReshopRS response)
        {
            return (response.GetReponseObject().Item as OrderReshopRSResponseReshopOffers)?.ReshopOffer?.ToList();
        }

        private static OrderReshopRQQueryReshopOrderServicingAddQualifier GetAddQualifierForOptionalPNR(SessionCache session, OrderViewRS orderViewRS)
        {
            string PassangerIdList = null;
            if (orderViewRS.GetDataList()?.PassengerList != null && orderViewRS.GetDataList().PassengerList.ToList().Count > 0)
                orderViewRS.GetDataList()?.PassengerList.ToList().ForEach(x => PassangerIdList += x.PassengerID + " ");
            return new OrderReshopRQQueryReshopOrderServicingAddQualifier()
            {
                ExistingOrderQualifier = new ExistingOrderQualifier()
                {
                    OrderKeys = new OrderKeysType()
                    {
                        OrderID = new OrderID_Type()
                        {
                            Value = session.CurrentFlow.PNR.Number,
                            Owner = GeneralConstants.BOOKING_OWNER
                        },
                    },
                    PassengerReferences = PassangerIdList != null ? PassangerIdList.Trim() : null
                }
            };
        }

        private static OrderReshopRQQueryReshopOrderServicingAddFlightQuery GetFlightAddForTourOperator(SessionCache session, OrderViewRS orderViewRS)
        {
            return new OrderReshopRQQueryReshopOrderServicingAddFlightQuery();
        }

        public static OrderReshopRSResponse GetReponseObject(this OrderReshopRS response)
        {
            return (response.Items?.Where(w => w is OrderReshopRSResponse)?.FirstOrDefault() as OrderReshopRSResponse);
        }
        public static List<ListOfFlightSegmentType> GetFlightSegments(this OrderReshopRS response, string flightKey)
        {
            var flightList = response.GetDataList()?.FlightList?.ToList();
            var segmentList = response.GetDataList()?.FlightSegmentList?.ToList();

            return segmentList.Where(f => flightList?.FirstOrDefault(fl => fl.FlightKey.Equals(flightKey))?.SegmentReferences?.Value?.Split(' ')?.Contains(f.SegmentKey) == true).ToList();
        }
        public static ListOfFlightSegmentType GetFlightSegment(this OrderReshopRS response, string segmentKey)
        {
            return response.GetDataList()?.FlightSegmentList?.FirstOrDefault(f => f.SegmentKey == segmentKey);
        }

        public static decimal GetTotalPrice(this OrderReshopRS response, string segment, SessionCache session)
        {
            if (session.CurrentFlow.PNR.IsOptionalPNR)
            {
                var totalPriceItem = (SimpleCurrencyPriceType)response.GetOffers()?.FirstOrDefault()?.TotalPrice.Item;
                var totalPriceValue = totalPriceItem.Value;
                var totalPriceCurrency = totalPriceItem.Code;
                return totalPriceValue;
            }
            else
            {
                var fareComponentSum = response.GetSegmentFareComponents(segment)
                .Sum(s => ((s.Price?.TotalAmount?.Item as DetailCurrencyPriceType)?.Item as CurrencyAmountOptType)?.Value ?? 0);

                var serviceSum = response.GetOffers()
                    .SelectMany(s => s.AddOfferItem)
                    .Where(w => w.Service != null)
                    .Where(w => w.Service.FirstOrDefault()?.Item is OfferItemTypeServiceServiceDefinitionRef)
                    .Where(w => (w.Service.FirstOrDefault()?.Item as OfferItemTypeServiceServiceDefinitionRef).SegmentRefs != null)
                    .Where(w => segment.Contains((w.Service.FirstOrDefault()?.Item as OfferItemTypeServiceServiceDefinitionRef)?.SegmentRefs))
                    .Sum(s => ((s.TotalPriceDetail?.TotalAmount?.Item as DetailCurrencyPriceType)?.Item as CurrencyAmountOptType)?.Value ?? 0);

                var seatSum = response.GetOffers()
                    .SelectMany(s => s.AddOfferItem)
                    .Where(w => w.Service != null)
                    .Where(w => w.Service.FirstOrDefault()?.Item is OfferItemTypeServiceSelectedSeat)
                     .Where(w => (w.Service.FirstOrDefault()?.Item as OfferItemTypeServiceSelectedSeat).SegmentRef != null)
                    .Where(w => segment.Contains((w.Service.FirstOrDefault()?.Item as OfferItemTypeServiceSelectedSeat)?.SegmentRef?.ToString()))
                    .Sum(s => ((s.TotalPriceDetail?.TotalAmount?.Item as DetailCurrencyPriceType)?.Item as CurrencyAmountOptType)?.Value ?? 0);

                decimal alreadyBoughtBaggagePrice = decimal.Zero;
                decimal alreadyBoughtSeatPrice = decimal.Zero;

                var deleteOfferItems = response.GetOffers()?
                                               .Where(l => l.DeleteOfferItem != null)?
                                               .SelectMany(k => k.DeleteOfferItem)?
                                               .Select(q => q.OfferItemID)?
                                               .Where(a => a.Contains("SSR"))?
                                               .Select(z => z?.Substring(z.LastIndexOf("SSR.")))?
                                               .ToList();
                if (deleteOfferItems != null)
                {
                    var serviceCategories = CachedData.ServiceCodeCategories;
                    var baggageServicesDeleted = response.GetDataList()?.ServiceDefinitionList?
                                                    .Where(k => deleteOfferItems.Select(t => "V1_" + t).ToList().Contains(k.ServiceDefinitionID)
                                                                 && (IbsUtility.GetCategory(serviceCategories, k.Encoding?.Code?.Value) == ServiceCategoryEnum.BAGGAGE || k.Encoding?.Code?.Value == "XBAG")).ToList();
                    if (baggageServicesDeleted != null && baggageServicesDeleted.Count > 0)
                    {
                        var deletedBaggageOfferItems = response.GetOffers()?
                                           .SelectMany(w => w.DeleteOfferItem)?
                                           .Where(k => baggageServicesDeleted.Select(q => q.ServiceDefinitionID.Replace("V1_", "")).Any(a => k.OfferItemID.Contains(a))).ToList();
                        if (deletedBaggageOfferItems != null && deletedBaggageOfferItems.Count > 0)
                        {
                            var originalTotalAmount = deletedBaggageOfferItems.Sum(k => k.ReshopDifferential.OriginalOrderItem.Total.Amount.Value);
                            var newOfferItemTotalAmount = deletedBaggageOfferItems.Sum(k => k.ReshopDifferential.NewOfferItem.Total.Amount.Value);
                            if (originalTotalAmount != decimal.Zero /*&& originalTotalAmount != newOfferItemTotalAmount*/)
                            {
                                alreadyBoughtBaggagePrice = originalTotalAmount;
                            }
                        }
                    }

                    var seatServicesDeleted = response.GetDataList()?.ServiceDefinitionList?
                                                    .Where(k => deleteOfferItems.Select(t => "V1_" + t).ToList().Contains(k.ServiceDefinitionID)
                                                                 && (IbsUtility.GetCategory(serviceCategories, k.Encoding?.Code?.Value) == ServiceCategoryEnum.SEAT
                                                                 || Ubimecs.Infrastructure.Utilities.Statics.SeatTypes.Contains(k.Encoding?.Code?.Value) == true)).ToList();
                    if (seatServicesDeleted != null && seatServicesDeleted.Count > 0)
                    {
                        var deletedSeatOfferItems = response.GetOffers()?
                                           .SelectMany(w => w.DeleteOfferItem)?
                                           .Where(k => seatServicesDeleted.Select(q => q.ServiceDefinitionID.Replace("V1_", "")).Any(a => k.OfferItemID.Contains(a))).ToList();
                        if (deletedSeatOfferItems != null && deletedSeatOfferItems.Count > 0)
                        {
                            var originalTotalAmount = deletedSeatOfferItems.Sum(k => k.ReshopDifferential.OriginalOrderItem.Total.Amount.Value);
                            var newOfferItemTotalAmount = deletedSeatOfferItems.Sum(k => k.ReshopDifferential.NewOfferItem.Total.Amount.Value);
                            if (originalTotalAmount != decimal.Zero /*&& originalTotalAmount != newOfferItemTotalAmount*/)
                            {
                                alreadyBoughtSeatPrice = originalTotalAmount;
                            }
                        }
                    }
                }



                return fareComponentSum + serviceSum + seatSum - (alreadyBoughtBaggagePrice + alreadyBoughtSeatPrice);
            }

        }
        public static List<FareComponentType> GetSegmentFareComponents(this OrderReshopRS response, string segment)
        {

            var result = new List<FareComponentType>();
            result = response
                .GetOffers()?
                .SelectMany(s => s?.AddOfferItem)?
                .Where(w => w.FareDetail != null)?
                .SelectMany(s => s.FareDetail)?
                .SelectMany(s => s.FareComponent)?
                .Where(w => w?.SegmentRefs?.Value?.Equals(segment) == true)?
                .ToList();

            return result;
        }
        public static string GetPassengerTypeTotalPriceDescription(this OrderReshopRS response, string ptc, string segment)
        {
            var priceTypes = response.GetPassengerTypePriceItems(ptc, segment);

            var sum = priceTypes.Sum(s => s.Details.Detail.FirstOrDefault()?.SubTotal.Value ?? 0);

            return sum.ToString() + " " + priceTypes.FirstOrDefault()?.Details?.Detail?.FirstOrDefault()?.SubTotal.Code ?? "TRY";
        }
        public static List<OrderReshopRSResponseDataListsBaggageAllowance> GetBaggageAllowenceList(this OrderReshopRS response)
        {
            return response
                ?.GetDataList()
                ?.BaggageAllowanceList
                ?.ToList() ?? new List<OrderReshopRSResponseDataListsBaggageAllowance>();

        }


        public static decimal GetPassengerTypeTotalPrice(this OrderReshopRS response, string ptc, string segment)
        {
            var priceTypes = response.GetPassengerTypePriceItems(ptc, segment);

            var sum = priceTypes.Sum(s => s.Details.Detail.FirstOrDefault()?.SubTotal.Value ?? 0);

            return sum;
        }
        public static List<DetailCurrencyPriceType> GetPassengerTypePriceItems(this OrderReshopRS response, string ptc, string segment)
        {
            var ptcOffers = response
                .GetOffers()
                .SelectMany(s => s.AddOfferItem)
                .Where(w => response.GetPTC(w.Service.FirstOrDefault()?.PassengerRefs) == ptc);

            return ptcOffers
                .Where(w => w.FareDetail != null)
                .SelectMany(s => s.FareDetail)
                .SelectMany(sm => sm.FareComponent)
                .Where(w => w.SegmentRefs?.Value?.Equals(segment) == true)
                .Select(s => s.Price.TotalAmount.Item as DetailCurrencyPriceType)
                .ToList();
        }
        public static string GetPTC(this OrderReshopRS response, string passengerRef)
        {
            return response.GetDataList()
                .PassengerList?.Where(x => x.PassengerID == passengerRef)?
                .FirstOrDefault()?
                .PTC;
        }
        public static Dictionary<string, decimal> GetTotalTaxPrice(this OrderReshopRS response, string segment)
        {
            var dict = response.GetSegmentFareComponents(segment)
                .Select(s => s.Price
                    ?.Taxes
                    ?.Breakdown)
                ?.Where(w => w != null)
                ?.SelectMany(s => s.Tax)
                ?.GroupBy(g => g.TaxCode)
                ?.ToDictionary(k => k.Key, v => v.Sum(vs => vs.Amount?.Value ?? 0));

            return dict;
        }
        public static decimal GetTotalSurCharge(this OrderReshopRS response, string segment)
        {
            var sum = response.GetSegmentFareComponents(segment)
                .Sum(s =>
                    s.Price
                    ?.Surcharges
                    ?.FirstOrDefault()
                    ?.Breakdown
                    ?.Where(w => w.Designator?.Equals("FUEL") == true)
                    ?.Select(ss => ss.Amount?.Value ?? 0)
                    ?.Sum() ?? 0
                );

            return sum;
        }
        public static decimal GetServiceListTotalPrice(this OrderReshopRS response, string[] serviceIds)
        {
            var sum = response.GetOffers()
            .SelectMany(s => s.AddOfferItem)
            .Where(w => w.Service != null)
            .Where(w => w.Service.FirstOrDefault()?.Item is OfferItemTypeServiceServiceDefinitionRef)
            .Where(w => serviceIds.Contains((w.Service.FirstOrDefault()?.Item as OfferItemTypeServiceServiceDefinitionRef)?.Value))
            .Sum(s => ((s.TotalPriceDetail?.TotalAmount?.Item as DetailCurrencyPriceType)?.Item as CurrencyAmountOptType)?.Value ?? 0);

            var sumSeat = response.GetOffers()
                .SelectMany(s => s.AddOfferItem)
                .Where(w => w.Service != null)
                .Where(w => w.Service.FirstOrDefault()?.Item is OfferItemTypeServiceSelectedSeat)
                .Where(w => serviceIds.Contains((w.Service.FirstOrDefault()?.Item as OfferItemTypeServiceSelectedSeat)?.ServiceDefinitionRef.ToString()))
                .Sum(s => ((s.TotalPriceDetail?.TotalAmount?.Item as DetailCurrencyPriceType)?.Item as CurrencyAmountOptType)?.Value ?? 0);

            return sum + sumSeat;

        }
        public static decimal GetSeatServiceTotalPrice(this OrderReshopRS response, string[] serviceIds)
        {
            if (serviceIds.Length > 0)
            {
                var zeroValue = response.GetServiceListTotalPrice(serviceIds);
                if (zeroValue != decimal.Zero)
                {
                    var serviceCategories = CachedData.ServiceCodeCategories;
                    var deleteOfferItems = response.GetOffers()?
                                                    .Where(s => s.DeleteOfferItem != null)?
                                                    .SelectMany(k => k.DeleteOfferItem)?
                                                    .Select(q => q.OfferItemID)?
                                                    .Where(a => a.Contains("SSR"))?
                                                    .Select(z => z.Substring(z.LastIndexOf("SSR.")))
                                                    .ToList();
                    if (deleteOfferItems != null)
                    {
                        var baggageServicesDeleted = response.GetDataList()?.ServiceDefinitionList?
                                                        .Where(k => deleteOfferItems.Select(t => "V1_" + t).ToList().Contains(k.ServiceDefinitionID)
                                                                     && (IbsUtility.GetCategory(serviceCategories, k.Encoding?.Code?.Value) == ServiceCategoryEnum.SEAT
                                                                      || Ubimecs.Infrastructure.Utilities.Statics.SeatTypes.Contains(k.Encoding?.Code?.Value) == true)).ToList();
                        if (baggageServicesDeleted != null && baggageServicesDeleted.Count > 0)
                        {
                            var deletedBaggageOfferItems = response.GetOffers()?
                                               .SelectMany(w => w.DeleteOfferItem)?
                                               .Where(k => baggageServicesDeleted.Select(q => q.ServiceDefinitionID.Replace("V1_", "")).Any(a => k.OfferItemID.Contains(a))).ToList();
                            if (deletedBaggageOfferItems != null && deletedBaggageOfferItems.Count > 0)
                            {
                                var originalTotalAmount = deletedBaggageOfferItems.Sum(k => k.ReshopDifferential.OriginalOrderItem.Total.Amount.Value);
                                var newOfferTotalAmount = deletedBaggageOfferItems.Sum(k => k.ReshopDifferential.NewOfferItem.Total.Amount.Value);
                                if (originalTotalAmount != decimal.Zero /*&& newOfferTotalAmount != originalTotalAmount*/)
                                {
                                    return zeroValue - originalTotalAmount;
                                }
                                else
                                {
                                    return zeroValue;
                                }
                            }
                            else
                            {
                                return zeroValue;
                            }
                        }
                        else
                        {
                            return zeroValue;
                        }
                    }
                    else
                    {
                        return zeroValue;
                    }

                }
                return decimal.Zero;
            }
            return decimal.Zero;
        }

        public static decimal GetBaggageServiceTotalPrice(this OrderReshopRS response, string[] serviceIds)
        {
            if (serviceIds.Length > 0)
            {
                var zeroValue = response.GetServiceListTotalPrice(serviceIds);
                if (zeroValue != decimal.Zero)
                {
                    var serviceCategories = CachedData.ServiceCodeCategories;
                    var deleteOfferItems = response.GetOffers()?
                                                    .Where(s => s.DeleteOfferItem != null)?
                                                    .SelectMany(k => k.DeleteOfferItem)?
                                                    .Select(q => q.OfferItemID)?
                                                    .Where(a => a.Contains("SSR"))?
                                                    .Select(z => z.Substring(z.LastIndexOf("SSR.")))
                                                    .ToList();
                    if (deleteOfferItems != null)
                    {
                        var baggageServicesDeleted = response.GetDataList()?.ServiceDefinitionList?
                                                        .Where(k => deleteOfferItems.Select(t => "V1_" + t).ToList().Contains(k.ServiceDefinitionID)
                                                                     && (IbsUtility.GetCategory(serviceCategories, k.Encoding?.Code?.Value) == ServiceCategoryEnum.BAGGAGE || k.Encoding?.Code?.Value == "XBAG")).ToList();
                        if (baggageServicesDeleted != null && baggageServicesDeleted.Count > 0)
                        {
                            var deletedBaggageOfferItems = response.GetOffers()?
                                               .SelectMany(w => w.DeleteOfferItem)?
                                               .Where(k => baggageServicesDeleted.Select(q => q.ServiceDefinitionID.Replace("V1_", "")).Any(a => k.OfferItemID.Contains(a))).ToList();
                            if (deletedBaggageOfferItems != null && deletedBaggageOfferItems.Count > 0)
                            {
                                var originalTotalAmount = deletedBaggageOfferItems.Sum(k => k.ReshopDifferential.OriginalOrderItem.Total.Amount.Value);
                                var newOfferTotalAmount = deletedBaggageOfferItems.Sum(k => k.ReshopDifferential.NewOfferItem.Total.Amount.Value);
                                if (originalTotalAmount != decimal.Zero /*&& newOfferTotalAmount != originalTotalAmount*/)
                                {
                                    return zeroValue - originalTotalAmount;
                                }
                                else
                                {
                                    return zeroValue;
                                }
                            }
                            else
                            {
                                return zeroValue;
                            }
                        }
                        else
                        {
                            return zeroValue;
                        }
                    }
                    else
                    {
                        return zeroValue;
                    }

                }
                return decimal.Zero;
            }
            return decimal.Zero;
        }
        public static decimal GetTotalCreditCardFeePrice(this OrderReshopRS response, string segment)
        {
            decimal price = decimal.Zero;
            var ccfeeServiceId = response.GetServices(segment).FirstOrDefault(t => t.Encoding.Code.Value == "CCFEE").ServiceDefinitionID;
            price = response.GetServiceListTotalPrice(new string[] { ccfeeServiceId });
            return price;
        }
        public static List<ServiceDefinitionType> GetServices(this OrderReshopRS response, string segments)
        {
            var services = response.GetDataList()?.ServiceDefinitionList;

            var segmentServiceIds = response.GetOffers()?
                .SelectMany(s => s.AddOfferItem)?
                .Where(w => w.Service != null)?
                .SelectMany(s => s.Service)?
                .Where(w => w.Item is OfferItemTypeServiceServiceDefinitionRef)?
                .Select(s => s.Item as OfferItemTypeServiceServiceDefinitionRef)?
                .Where(w => IbsUtility.CheckIdsEqual(segments, w.SegmentRefs) || IbsUtility.SplitIds(segments).Contains(w.SegmentRefs))?
                .Select(s => s.Value)?
                .ToList();

            var segmentSeatServiceIds = response.GetOffers()?
                .SelectMany(s => s.AddOfferItem)?
                .Where(w => w.Service != null)?
                .SelectMany(s => s.Service)?
                .Where(w => w.Item is OfferItemTypeServiceSelectedSeat)?
                .Select(s => s.Item as OfferItemTypeServiceSelectedSeat)?
                .Where(w => IbsUtility.SplitIds(segments).Contains(w.SegmentRef))?
                .Select(s => s.ServiceDefinitionRef.ToString())?
                .ToList();

            segmentServiceIds.AddRange(segmentSeatServiceIds);


            if (services != null)
            {
                var segmentServices = services?.Where(w => segmentServiceIds.Contains(w.ServiceDefinitionID))?.ToList() ?? new List<ServiceDefinitionType>();

                var bundleServices = segmentServices.Where(w => w.Item is ServiceDefinitionTypeServiceBundle).Select(s => s.Item as ServiceDefinitionTypeServiceBundle).ToList();
                var bundleSubServiceIds = bundleServices.SelectMany(s => s.ServiceDefinitionRef.Select(sm => sm.Value));
                var bundleSubServices = services.Where(w => bundleSubServiceIds.Contains(w.ServiceDefinitionID));

                segmentServices.AddRange(bundleSubServices);

                return segmentServices;
            }
            else
            {
                return new List<ServiceDefinitionType>();
            }
        }
        public static List<SeatTypeEnum> GetSeatTypes(this OrderReshopRS response, string[] serviceIds, Dictionary<string, ServiceCategoryEnum> serviceCodeCategories)
        {
            var services = response.GetDataList().ServiceDefinitionList
               ?.Where(w => serviceIds.Contains(w.ServiceDefinitionID));

            SeatTypeEnum seatType = services
                .Where(w => IbsUtility.GetCategory(serviceCodeCategories, w.Encoding?.Code?.Value) == ServiceCategoryEnum.SEAT)
                .Select(w => IbsUtility.XlegSeatCodes.Contains(w.Encoding?.Code?.Value) ? SeatTypeEnum.Xleg : SeatTypeEnum.Normal)
                .FirstOrDefault();

            return seatType == SeatTypeEnum.Xleg ? new List<SeatTypeEnum> { SeatTypeEnum.Xleg, SeatTypeEnum.Normal } : new List<SeatTypeEnum> { SeatTypeEnum.Normal };
        }
        public static string GetCurrencyCode(this OrderReshopRS response)
        {
            var offers = response.GetOffers();
            if (offers != null)
            {
                var fareDetail = offers.SelectMany(s => s.AddOfferItem)?.FirstOrDefault(t => t.FareDetail != null);
                if (fareDetail != null)
                {
                    var currencyPriceType = fareDetail.FareDetail?.FirstOrDefault()?.Price?.TotalAmount?.Item as DetailCurrencyPriceType;
                    return !string.IsNullOrEmpty(currencyPriceType.Details?.Detail?.FirstOrDefault()?.SubTotal?.Code)
                          ? currencyPriceType.Details?.Detail?.FirstOrDefault()?.SubTotal?.Code
                          : null;
                }
                else
                {
                    var detailCurrencyPriceItem = offers.Where(t => t.AddOfferItem != null).SelectMany(s => s.AddOfferItem)?.FirstOrDefault().TotalPriceDetail?.TotalAmount?.Item as DetailCurrencyPriceType;
                    if (detailCurrencyPriceItem != null)
                    {
                        var currencyAmountOptType = detailCurrencyPriceItem.Item as CurrencyAmountOptType;
                        return !string.IsNullOrEmpty(currencyAmountOptType.Code)
                             ? currencyAmountOptType.Code
                             : null;
                    }
                    else
                    {
                        return null;
                    }

                }
            }
            return null;
        }

        public static string GetCurrenyCodeOptionalPNR(this OrderReshopRS response)
        {
            var currency = (SimpleCurrencyPriceType)response.GetOffers()?.FirstOrDefault(t => t.TotalPrice.Item.GetType() == typeof(SimpleCurrencyPriceType)).TotalPrice.Item;

            if (currency == null) return string.Empty;

            var code = currency.Code;
            return code;
        }
        public static string GetOriginDestinationId(this OrderReshopRS response, string flightKey)
        {
            return response.GetOriginDestinations()?.FirstOrDefault(f => f.FlightReferences.Value.Contains(flightKey))?.OriginDestinationKey;
        }
        public static FareComponentTypeFareBasisCabinType GetCabinType(this OrderReshopRS response, string segmentId)
        {
            var emptyFare = new FareComponentTypeFareBasisCabinType
            {
                CabinTypeCode = null,
                CabinTypeName = null
            };

            var cabin = response.GetOffers()
                ?.SelectMany(s => s.AddOfferItem)
                ?.SelectMany(s => s.FareDetail)
                ?.SelectMany(s => s.FareComponent)
                ?.FirstOrDefault(f => f.SegmentRefs.Value.Split(' ').Any(k => segmentId.Split(' ').ToList().Contains(k)))
                ?.FareBasis
                ?.CabinType;

            return cabin ?? emptyFare;
        }
        public static string GetFlightSegmentRef(this OrderReshopRS response, string flightKey)
        {
            var segmentrefs = response.GetDataList()?.FlightList?.FirstOrDefault(f => f.FlightKey == flightKey)?.SegmentReferences?.Value;
            return segmentrefs;
        }
        public static string GetFMD(this OrderReshopRS response, string flightKey)
        {
            var offer = response.GetOfferItem(flightKey);

            return offer?
                .AddOfferItem?.FirstOrDefault()?
                .FareDetail?.FirstOrDefault()?
                .FareComponent?.FirstOrDefault()?
                .FareBasis?
                .FareBasisCode?
                .refs?.Split(' ')?.FirstOrDefault(f => f.Contains("FMD")) ?? "ST";
        }
        public static FareComponentType GetFareComponent(this OrderReshopRS response, string segmentId, string passengerRefs)
        {
            var result = response.GetOffers()
                .SelectMany(s => s.AddOfferItem)
                .SelectMany(s => s.FareDetail)
                .Where(w => w.PassengerRefs.Value == passengerRefs)
                .SelectMany(s => s.FareComponent)
                .Where(w => w.SegmentRefs.Value == segmentId)
                .FirstOrDefault();

            return result;
        }
        public static List<FareComponentAugPointInformation> GetFareComponents(this OrderReshopRS response)
        {
            var FareComponentInformationList = new List<FareComponentAugPointInformation>();

            if (response?.GetOffers()?.Count > 0)
            {
                var FareAugPointList = response.GetReponseObject().Metadata.PassengerMetadata
                    .Select(x => x.AugmentationPoint)
                    .SelectMany(y => y.AugPoint)
                    .ToList();


                foreach (var item in FareAugPointList)
                {
                    if (item.Key.Contains("FARECOMPREFS"))
                    {
                        var xml = XElement.Parse(item.Any.OuterXml);
                        var BaseFare = xml.Elements().FirstOrDefault(x => x.Name == "BaseFare").Value;
                        var Discount = xml.Elements().FirstOrDefault(x => x.Name == "Discount").Value;
                        var DisplayFare = xml.Elements().FirstOrDefault(x => x.Name == "DisplayFare").Value;
                        var DisplayFareCurrencyType = xml.Elements().FirstOrDefault(x => x.Name == "DisplayFare").FirstAttribute.Value;

                        FareComponentAugPointInformation fareDetailAugPoints = new FareComponentAugPointInformation()
                        {
                            BaseFare = BaseFare,
                            Discount = Discount,
                            DisplayFare = DisplayFare,
                            Currency = DisplayFareCurrencyType,
                            Key = item.Key
                        };
                        FareComponentInformationList.Add(fareDetailAugPoints);
                    }
                }

            }

            return FareComponentInformationList;
        }
        public static List<FareDetailAugPointInformation> GetFareDetails(this OrderReshopRS response)
        {
            var FareDetailInformationList = new List<FareDetailAugPointInformation>();
            if (response?.GetOffers()?.Count > 0)
            {
                var FareAugPointList = response.GetReponseObject().Metadata.PassengerMetadata
                    .Select(x => x.AugmentationPoint)
                    .SelectMany(y => y.AugPoint)
                    .ToList();


                foreach (var item in FareAugPointList)
                {
                    if (item.Key.Contains("FMD"))
                    {
                        var xml = XElement.Parse(item.Any.OuterXml);
                        var FareID = xml.Elements().FirstOrDefault(x => x.Name == "FareId").Value;
                        var FareType = xml.Elements().FirstOrDefault(x => x.Name == "FareType").Value;
                        var FareLevel = xml.Elements().FirstOrDefault(x => x.Name == "FareLevel").Value;

                        FareDetailAugPointInformation fareDetailAugPoints = new FareDetailAugPointInformation()
                        {
                            FareId = FareID,
                            FareType = FareType,
                            FareLevel = FareLevel,
                            Key = item.Key
                        };
                        FareDetailInformationList.Add(fareDetailAugPoints);
                    }
                }
            }

            return FareDetailInformationList;
        }

        public static decimal GetTotalPrice(this OrderReshopRS response, BasePriceResponse result)
        {
            var metaData = response.GetReponseObject().Metadata?.PassengerMetadata?.Select(s => s.AugmentationPoint)?.SelectMany(s => s.AugPoint)?
                .FirstOrDefault(f => f.Any.LocalName.Equals("ItineraryAmountDetailAugPoint"));

            if (metaData != null)
            {
                return IbsUtility.ConvertToDecimal(XElement.Parse(metaData.Any.OuterXml).Elements().FirstOrDefault(f => f.Name == "TotalAmountToBePaid")?.Value);
            }
            else
            {
                if (result.Flights != null)
                {
                    return result.Flights.Sum(t => t.TotalPrice);
                }
                else
                {
                    return 0;
                }
            }
        }

        public static TimeSpan GetFlightJourneyTime(SessionCache session, string tmobId)
        {
            if (session.CurrentFlow.RebookingSearchFlightResponse != null)
            {
                return session.CurrentFlow.RebookingSearchFlightResponse.Flights.SelectMany(s => s.FlightOptions).FirstOrDefault(f => f.TmobId == tmobId)?.JourneyTime ?? TimeSpan.Zero;
            }
            else
            {
                return session.CurrentFlow.PnrInfoResponse.FlightDestionationList.FirstOrDefault(f => f.TmobId == tmobId)?.JourneyTime ?? TimeSpan.Zero;
            }
        }

        public static decimal GetOrderRetrieveServiceListTotalPrice(this OrderViewRS response, string[] serviceIds)
        {
            var sum = response.GetOrders()
                .Where(w => w.Service != null)
                .Where(w => w.Service.FirstOrDefault()?.Item is OrderItemTypeOrderItemServiceServiceDefinitionRef)
                .Where(w => serviceIds.Contains((w.Service.FirstOrDefault()?.Item as OrderItemTypeOrderItemServiceServiceDefinitionRef)?.Value))
                .Sum(s => ((s.PriceDetail?.TotalAmount?.Item as DetailCurrencyPriceType)?.Item as CurrencyAmountOptType)?.Value ?? 0);

            var sumSeat = response.GetOrders()
                .Where(w => w.Service != null)
                .Where(w => w.Service.FirstOrDefault()?.Item is OrderItemTypeOrderItemServiceSelectedSeat)
                .Where(w => serviceIds.Contains((w.Service.FirstOrDefault()?.Item as OrderItemTypeOrderItemServiceSelectedSeat).ServiceDefinitionRef.ToString()))
                .Sum(s => ((s.PriceDetail?.TotalAmount?.Item as DetailCurrencyPriceType)?.Item as CurrencyAmountOptType)?.Value ?? 0);

            return sum + sumSeat;
        }

        public static List<SeatTypeEnum> GetSeatTypeRetrieve(this OrderViewRS response, string[] serviceIds, Dictionary<string, ServiceCategoryEnum> serviceCodeCategories)
        {
            var services = response.GetDataList().ServiceDefinitionList
               ?.Where(w => serviceIds.Contains(w.ServiceDefinitionID));
            SeatTypeEnum seatType = services
                .Where(w => IbsUtility.GetCategory(serviceCodeCategories, w.Encoding?.Code?.Value) == ServiceCategoryEnum.SEAT)
                .Select(w => IbsUtility.XlegSeatCodes.Contains(w.Encoding?.Code?.Value) ? SeatTypeEnum.Xleg : SeatTypeEnum.Normal)
                .FirstOrDefault();

            return seatType == SeatTypeEnum.Xleg ? new List<SeatTypeEnum> { SeatTypeEnum.Xleg, SeatTypeEnum.Normal } : new List<SeatTypeEnum> { SeatTypeEnum.Normal };
        }
    }
}
