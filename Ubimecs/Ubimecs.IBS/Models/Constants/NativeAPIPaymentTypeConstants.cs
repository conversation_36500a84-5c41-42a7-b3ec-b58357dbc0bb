namespace Ubimecs.IBS.Models.Constants;

public class NativeAPIPaymentTypeConstants
{
    public const string AG = "Agency Credit";
    public const string CC = "Credit Card";
    public const string CF = "Credit File";
    public const string CA = "Cash";
    public const string CK = "Cheque";
    public const string DB = "Debit Card";
    public const string WO = "Write Off";
    public const string GC = "Gift Certificate";
    public const string TK = "Ticket";
    public const string DD = "Direct Debit";
    public const string CR = "Cash Receipt";
    public const string ET = "Bank Transfer";
    public const string ED = "Electronic Debit";
    public const string CI = "Corporate Invoice";
    public const string UNK = "Unknown Payment Type";
}
