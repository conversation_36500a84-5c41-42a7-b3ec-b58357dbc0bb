namespace Ubimecs.IBS.Models.Constants;

public static class NativeAPITravelDocumentConstants
{
    public static Dictionary<NativeDocumentType, string> DocumentType = new Dictionary<NativeDocumentType, string>()
    {
        { NativeDocumentType.F1, "IDENTITY CARD" },
        { NativeDocumentType.PT , "PASSPORT" },
        { NativeDocumentType.DP ,"DIPLOMATIC IDENTIFICATION"},
        { NativeDocumentType.MI ,"MILITARY IDENTIFICATION"}
    };
}
public enum NativeDocumentType
{
    F1,
    PT,
    DP,
    MI
}