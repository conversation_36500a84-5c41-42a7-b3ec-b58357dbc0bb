using Ubimecs.IBS.Services.IssueCertificateService;

namespace Ubimecs.IBS.Models.Loyalty
{
    public class LoyaltyRQIssueBonus
    {
        /// <remarks/>
        [System.SerializableAttribute()]
        [System.ComponentModel.DesignerCategoryAttribute("code")]
        [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://schemas.xmlsoap.org/soap/envelope/")]
        [System.Xml.Serialization.XmlRootAttribute(Namespace = "http://schemas.xmlsoap.org/soap/envelope/", IsNullable = false)]
        public partial class Envelope
        {
            private Header headerField;
            private EnvelopeBody bodyField;

            public Header Header
            {
                get
                {
                    return this.headerField;
                }
                set
                {
                    this.headerField = value;
                }
            }
            /// <remarks/>
            public EnvelopeBody Body
            {
                get
                {
                    return this.bodyField;
                }
                set
                {
                    this.bodyField = value;
                }
            }


        }


        /// <remarks/>
        [System.SerializableAttribute()]
        [System.ComponentModel.DesignerCategoryAttribute("code")]
        [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.ibsplc.com/iloyal/redemption/issuecertificate/type/")]
        public partial class EnvelopeBody
        {

            private object itemField;

            private System.Xml.XmlElement faultField;

            /// <remarks/>
            [System.Xml.Serialization.XmlElementAttribute("IssueCertificateRequest", typeof(IssueCertificateRequest))]
            [System.Xml.Serialization.XmlElementAttribute("IssueCertificateResponse", typeof(IssueCertificateResponse))]
            public object Item
            {
                get
                {
                    return this.itemField;
                }
                set
                {
                    this.itemField = value;
                }
            }

            /// <remarks/>
            [System.Xml.Serialization.XmlAnyElementAttribute()]
            public System.Xml.XmlElement Fault
            {
                get
                {
                    return this.faultField;
                }
                set
                {
                    this.faultField = value;
                }
            }
        }

        /// <remarks/>
        [System.SerializableAttribute()]
        [System.ComponentModel.DesignerCategoryAttribute("code")]
        [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.ibsplc.com/iloyal/redemption/issuecertificate/type/")]
        public partial class Header
        {
            private System.Xml.XmlElement anyField;

            /// <remarks/>
            [System.Xml.Serialization.XmlAnyElementAttribute()]
            public System.Xml.XmlElement Any
            {
                get
                {
                    return this.anyField;
                }
                set
                {
                    this.anyField = value;
                }
            }

        }
    }
}
