//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// 
// This source code was auto-generated by xsd, Version=4.6.1055.0.
// 
namespace Ubimecs.IBS.Models {
    using System.Xml.Serialization;
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace="http://www.iata.org/IATA/EDIST/2017.2", IsNullable=false)]
    public partial class OfferPriceRQ {
        
        private PointOfSaleType pointOfSaleField;
        
        private MsgDocumentType documentField;
        
        private MsgPartiesType partyField;
        
        private FltPriceReqParamsType parametersField;
        
        private OrderRequestType queryField;
        
        private Preference preferenceField;
        
        private Qualifier qualifierField;
        
        private ItineraryType journeyDataField;
        
        private TicketAutoExchangeType inExchangeForTicketField;
        
        private OfferPriceRQDataLists dataListsField;
        
        private OfferPriceRQMetadata metadataField;
        
        private PoliciesPolicy[] policiesField;
        
        private string echoTokenField;
        
        private System.DateTime timeStampField;
        
        private bool timeStampFieldSpecified;
        
        private OfferPriceRQTarget targetField;
        
        private string versionField;
        
        private string transactionIdentifierField;
        
        private string sequenceNmbrField;
        
        private OfferPriceRQTransactionStatusCode transactionStatusCodeField;
        
        private bool transactionStatusCodeFieldSpecified;
        
        private bool retransmissionIndicatorField;
        
        private bool retransmissionIndicatorFieldSpecified;
        
        private string correlationIDField;
        
        private bool asynchronousAllowedIndField;
        
        private bool asynchronousAllowedIndFieldSpecified;
        
        public OfferPriceRQ() {
            this.targetField = OfferPriceRQTarget.Production;
        }
        
        /// <remarks/>
        public PointOfSaleType PointOfSale {
            get {
                return this.pointOfSaleField;
            }
            set {
                this.pointOfSaleField = value;
            }
        }
        
        /// <remarks/>
        public MsgDocumentType Document {
            get {
                return this.documentField;
            }
            set {
                this.documentField = value;
            }
        }
        
        /// <remarks/>
        public MsgPartiesType Party {
            get {
                return this.partyField;
            }
            set {
                this.partyField = value;
            }
        }
        
        /// <remarks/>
        public FltPriceReqParamsType Parameters {
            get {
                return this.parametersField;
            }
            set {
                this.parametersField = value;
            }
        }
        
        /// <remarks/>
        public OrderRequestType Query {
            get {
                return this.queryField;
            }
            set {
                this.queryField = value;
            }
        }
        
        /// <remarks/>
        public Preference Preference {
            get {
                return this.preferenceField;
            }
            set {
                this.preferenceField = value;
            }
        }
        
        /// <remarks/>
        public Qualifier Qualifier {
            get {
                return this.qualifierField;
            }
            set {
                this.qualifierField = value;
            }
        }
        
        /// <remarks/>
        public ItineraryType JourneyData {
            get {
                return this.journeyDataField;
            }
            set {
                this.journeyDataField = value;
            }
        }
        
        /// <remarks/>
        public TicketAutoExchangeType InExchangeForTicket {
            get {
                return this.inExchangeForTicketField;
            }
            set {
                this.inExchangeForTicketField = value;
            }
        }
        
        /// <remarks/>
        public OfferPriceRQDataLists DataLists {
            get {
                return this.dataListsField;
            }
            set {
                this.dataListsField = value;
            }
        }
        
        /// <remarks/>
        public OfferPriceRQMetadata Metadata {
            get {
                return this.metadataField;
            }
            set {
                this.metadataField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Policy", IsNullable=false)]
        public PoliciesPolicy[] Policies {
            get {
                return this.policiesField;
            }
            set {
                this.policiesField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string EchoToken {
            get {
                return this.echoTokenField;
            }
            set {
                this.echoTokenField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public System.DateTime TimeStamp {
            get {
                return this.timeStampField;
            }
            set {
                this.timeStampField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool TimeStampSpecified {
            get {
                return this.timeStampFieldSpecified;
            }
            set {
                this.timeStampFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        [System.ComponentModel.DefaultValueAttribute(OfferPriceRQTarget.Production)]
        public OfferPriceRQTarget Target {
            get {
                return this.targetField;
            }
            set {
                this.targetField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Version {
            get {
                return this.versionField;
            }
            set {
                this.versionField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string TransactionIdentifier {
            get {
                return this.transactionIdentifierField;
            }
            set {
                this.transactionIdentifierField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="nonNegativeInteger")]
        public string SequenceNmbr {
            get {
                return this.sequenceNmbrField;
            }
            set {
                this.sequenceNmbrField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public OfferPriceRQTransactionStatusCode TransactionStatusCode {
            get {
                return this.transactionStatusCodeField;
            }
            set {
                this.transactionStatusCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool TransactionStatusCodeSpecified {
            get {
                return this.transactionStatusCodeFieldSpecified;
            }
            set {
                this.transactionStatusCodeFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public bool RetransmissionIndicator {
            get {
                return this.retransmissionIndicatorField;
            }
            set {
                this.retransmissionIndicatorField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool RetransmissionIndicatorSpecified {
            get {
                return this.retransmissionIndicatorFieldSpecified;
            }
            set {
                this.retransmissionIndicatorFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string CorrelationID {
            get {
                return this.correlationIDField;
            }
            set {
                this.correlationIDField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public bool AsynchronousAllowedInd {
            get {
                return this.asynchronousAllowedIndField;
            }
            set {
                this.asynchronousAllowedIndField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool AsynchronousAllowedIndSpecified {
            get {
                return this.asynchronousAllowedIndFieldSpecified;
            }
            set {
                this.asynchronousAllowedIndFieldSpecified = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class FltPriceReqParamsTypePricingFeeExemptionFeeCode
    {

        private string collectionPointField;

        private string valueField;

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string CollectionPoint
        {
            get
            {
                return this.collectionPointField;
            }
            set
            {
                this.collectionPointField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        public string Value
        {
            get
            {
                return this.valueField;
            }
            set
            {
                this.valueField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class FltPriceReqParamsTypePricingFeeExemptionFee
    {

        private FltPriceReqParamsTypePricingFeeExemptionFeeCode codeField;

        private string[] taxTypeField;

        private string refsField;

        /// <remarks/>
        public FltPriceReqParamsTypePricingFeeExemptionFeeCode Code
        {
            get
            {
                return this.codeField;
            }
            set
            {
                this.codeField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("TaxType")]
        public string[] TaxType
        {
            get
            {
                return this.taxTypeField;
            }
            set
            {
                this.taxTypeField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType = "IDREFS")]
        public string refs
        {
            get
            {
                return this.refsField;
            }
            set
            {
                this.refsField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class FltPriceReqParamsTypeServiceFilters : AssociatedObjectBaseType
    {

        private ServiceFilterType[] serviceFilterField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("ServiceFilter")]
        public ServiceFilterType[] ServiceFilter
        {
            get
            {
                return this.serviceFilterField;
            }
            set
            {
                this.serviceFilterField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class FltPriceReqParamsTypePricingFeeExemption : AssociatedObjectBaseType
    {

        private FltPriceReqParamsTypePricingFeeExemptionFee[] feeField;

        private CountryCode[] countryCodeField;

        private string[] provinceCodeField;

        private string governmentBodyField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("Fee")]
        public FltPriceReqParamsTypePricingFeeExemptionFee[] Fee
        {
            get
            {
                return this.feeField;
            }
            set
            {
                this.feeField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("CountryCode")]
        public CountryCode[] CountryCode
        {
            get
            {
                return this.countryCodeField;
            }
            set
            {
                this.countryCodeField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("ProvinceCode")]
        public string[] ProvinceCode
        {
            get
            {
                return this.provinceCodeField;
            }
            set
            {
                this.provinceCodeField = value;
            }
        }

        /// <remarks/>
        public string GovernmentBody
        {
            get
            {
                return this.governmentBodyField;
            }
            set
            {
                this.governmentBodyField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class FltPriceReqParamsTypePricing
    {

        private string overrideCurrencyField;

        private FltPriceReqParamsTypePricingFeeExemption feeExemptionField;

        private bool autoExchangeIndField;

        private bool autoExchangeIndFieldSpecified;

        private bool awardIncludedIndField;

        private bool awardIncludedIndFieldSpecified;

        private bool awardOnlyIndField;

        private bool awardOnlyIndFieldSpecified;

        private bool simpleIndField;

        private bool simpleIndFieldSpecified;

        /// <remarks/>
        public string OverrideCurrency
        {
            get
            {
                return this.overrideCurrencyField;
            }
            set
            {
                this.overrideCurrencyField = value;
            }
        }

        /// <remarks/>
        public FltPriceReqParamsTypePricingFeeExemption FeeExemption
        {
            get
            {
                return this.feeExemptionField;
            }
            set
            {
                this.feeExemptionField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public bool AutoExchangeInd
        {
            get
            {
                return this.autoExchangeIndField;
            }
            set
            {
                this.autoExchangeIndField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool AutoExchangeIndSpecified
        {
            get
            {
                return this.autoExchangeIndFieldSpecified;
            }
            set
            {
                this.autoExchangeIndFieldSpecified = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public bool AwardIncludedInd
        {
            get
            {
                return this.awardIncludedIndField;
            }
            set
            {
                this.awardIncludedIndField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool AwardIncludedIndSpecified
        {
            get
            {
                return this.awardIncludedIndFieldSpecified;
            }
            set
            {
                this.awardIncludedIndFieldSpecified = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public bool AwardOnlyInd
        {
            get
            {
                return this.awardOnlyIndField;
            }
            set
            {
                this.awardOnlyIndField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool AwardOnlyIndSpecified
        {
            get
            {
                return this.awardOnlyIndFieldSpecified;
            }
            set
            {
                this.awardOnlyIndFieldSpecified = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public bool SimpleInd
        {
            get
            {
                return this.simpleIndField;
            }
            set
            {
                this.simpleIndField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool SimpleIndSpecified
        {
            get
            {
                return this.simpleIndFieldSpecified;
            }
            set
            {
                this.simpleIndFieldSpecified = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class FltPriceReqParamsType : MessageParamsBaseType
    {

        private FltPriceReqParamsTypeServiceFilters serviceFiltersField;

        private FltPriceReqParamsTypePricing pricingField;

        /// <remarks/>
        public FltPriceReqParamsTypeServiceFilters ServiceFilters
        {
            get
            {
                return this.serviceFiltersField;
            }
            set
            {
                this.serviceFiltersField = value;
            }
        }

        /// <remarks/>
        public FltPriceReqParamsTypePricing Pricing
        {
            get
            {
                return this.pricingField;
            }
            set
            {
                this.pricingField = value;
            }
        }
    }
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OfferPriceRQDataLists {
        
        private PassengerType[] passengerListField;
        
        private ContactInformationType[] contactListField;
        
        private ListOfBagDisclosureTypeBagDisclosure[] bagDisclosureListField;
        
        private OfferPriceRQDataListsBaggageAllowance[] baggageAllowanceListField;
        
        private ListOfClassOfServiceTypeServiceClass[] classOfServiceListField;
        
        private ListOfContentSourceTypeContentSource[] contentSourceListField;
        
        private Description[] descriptionListField;
        
        private ListOfDisclosureTypeDisclosures[] disclosureListField;
        
        private FareListFareGroup[] fareListField;
        
        private ListOfFlightSegmentType[] flightSegmentListField;
        
        private OfferPriceRQDataListsFlight[] flightListField;
        
        private OriginDestination[] originDestinationListField;
        
        private ListOfOfferInstructionsTypeInstruction[] instructionsListField;
        
        private ListOfMediaTypeMedia[] mediaListField;
        
        private ListOfOfferPenaltyTypePenalty[] penaltyListField;
        
        private PriceClassType[] priceClassListField;
        
        private ServiceDefinitionType[] serviceDefinitionListField;
        
        private ListOfOfferTermsType termsListField;
        
        private OfferPriceRQDataListsSeatDefinition[] seatDefinitionListField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Passenger", IsNullable=false)]
        public PassengerType[] PassengerList {
            get {
                return this.passengerListField;
            }
            set {
                this.passengerListField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("ContactInformation", IsNullable=false)]
        public ContactInformationType[] ContactList {
            get {
                return this.contactListField;
            }
            set {
                this.contactListField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("BagDisclosure", IsNullable=false)]
        public ListOfBagDisclosureTypeBagDisclosure[] BagDisclosureList {
            get {
                return this.bagDisclosureListField;
            }
            set {
                this.bagDisclosureListField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("BaggageAllowance", IsNullable=false)]
        public OfferPriceRQDataListsBaggageAllowance[] BaggageAllowanceList {
            get {
                return this.baggageAllowanceListField;
            }
            set {
                this.baggageAllowanceListField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("ServiceClass", IsNullable=false)]
        public ListOfClassOfServiceTypeServiceClass[] ClassOfServiceList {
            get {
                return this.classOfServiceListField;
            }
            set {
                this.classOfServiceListField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("ContentSource", IsNullable=false)]
        public ListOfContentSourceTypeContentSource[] ContentSourceList {
            get {
                return this.contentSourceListField;
            }
            set {
                this.contentSourceListField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Description", IsNullable=false)]
        public Description[] DescriptionList {
            get {
                return this.descriptionListField;
            }
            set {
                this.descriptionListField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Disclosures", IsNullable=false)]
        public ListOfDisclosureTypeDisclosures[] DisclosureList {
            get {
                return this.disclosureListField;
            }
            set {
                this.disclosureListField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("FareGroup", IsNullable=false)]
        public FareListFareGroup[] FareList {
            get {
                return this.fareListField;
            }
            set {
                this.fareListField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("FlightSegment", IsNullable=false)]
        public ListOfFlightSegmentType[] FlightSegmentList {
            get {
                return this.flightSegmentListField;
            }
            set {
                this.flightSegmentListField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Flight", IsNullable=false)]
        public OfferPriceRQDataListsFlight[] FlightList {
            get {
                return this.flightListField;
            }
            set {
                this.flightListField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("OriginDestination", IsNullable=false)]
        public OriginDestination[] OriginDestinationList {
            get {
                return this.originDestinationListField;
            }
            set {
                this.originDestinationListField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Instruction", IsNullable=false)]
        public ListOfOfferInstructionsTypeInstruction[] InstructionsList {
            get {
                return this.instructionsListField;
            }
            set {
                this.instructionsListField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Media", IsNullable=false)]
        public ListOfMediaTypeMedia[] MediaList {
            get {
                return this.mediaListField;
            }
            set {
                this.mediaListField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Penalty", IsNullable=false)]
        public ListOfOfferPenaltyTypePenalty[] PenaltyList {
            get {
                return this.penaltyListField;
            }
            set {
                this.penaltyListField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("PriceClass", IsNullable=false)]
        public PriceClassType[] PriceClassList {
            get {
                return this.priceClassListField;
            }
            set {
                this.priceClassListField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("ServiceDefinition", IsNullable=false)]
        public ServiceDefinitionType[] ServiceDefinitionList {
            get {
                return this.serviceDefinitionListField;
            }
            set {
                this.serviceDefinitionListField = value;
            }
        }
        
        /// <remarks/>
        public ListOfOfferTermsType TermsList {
            get {
                return this.termsListField;
            }
            set {
                this.termsListField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("SeatDefinition", IsNullable=false)]
        public OfferPriceRQDataListsSeatDefinition[] SeatDefinitionList {
            get {
                return this.seatDefinitionListField;
            }
            set {
                this.seatDefinitionListField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OfferPriceRQDataListsBaggageAllowance {
        
        private BaggageCategoryListType baggageCategoryField;
        
        private BagAllowanceDescType allowanceDescriptionField;
        
        private BagAllowanceDimensionType dimensionAllowanceField;
        
        private PieceAllowance[] pieceAllowanceField;
        
        private WeightAllowance weightAllowanceField;
        
        private BaggageDeterminingCarrierType baggageDeterminingCarrierField;
        
        private string baggageAllowanceIDField;
        
        /// <remarks/>
        public BaggageCategoryListType BaggageCategory {
            get {
                return this.baggageCategoryField;
            }
            set {
                this.baggageCategoryField = value;
            }
        }
        
        /// <remarks/>
        public BagAllowanceDescType AllowanceDescription {
            get {
                return this.allowanceDescriptionField;
            }
            set {
                this.allowanceDescriptionField = value;
            }
        }
        
        /// <remarks/>
        public BagAllowanceDimensionType DimensionAllowance {
            get {
                return this.dimensionAllowanceField;
            }
            set {
                this.dimensionAllowanceField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("PieceAllowance")]
        public PieceAllowance[] PieceAllowance {
            get {
                return this.pieceAllowanceField;
            }
            set {
                this.pieceAllowanceField = value;
            }
        }
        
        /// <remarks/>
        public WeightAllowance WeightAllowance {
            get {
                return this.weightAllowanceField;
            }
            set {
                this.weightAllowanceField = value;
            }
        }
        
        /// <remarks/>
        public BaggageDeterminingCarrierType BaggageDeterminingCarrier {
            get {
                return this.baggageDeterminingCarrierField;
            }
            set {
                this.baggageDeterminingCarrierField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="ID")]
        public string BaggageAllowanceID {
            get {
                return this.baggageAllowanceIDField;
            }
            set {
                this.baggageAllowanceIDField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OfferPriceRQDataListsFlight {
        
        private TotalJourneyType journeyField;
        
        private SegmentReferences segmentReferencesField;
        
        private OfferPriceRQDataListsFlightSettlement settlementField;
        
        private string refsField;
        
        private string flightKeyField;
        
        /// <remarks/>
        public TotalJourneyType Journey {
            get {
                return this.journeyField;
            }
            set {
                this.journeyField = value;
            }
        }
        
        /// <remarks/>
        public SegmentReferences SegmentReferences {
            get {
                return this.segmentReferencesField;
            }
            set {
                this.segmentReferencesField = value;
            }
        }
        
        /// <remarks/>
        public OfferPriceRQDataListsFlightSettlement Settlement {
            get {
                return this.settlementField;
            }
            set {
                this.settlementField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="IDREFS")]
        public string refs {
            get {
                return this.refsField;
            }
            set {
                this.refsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="ID")]
        public string FlightKey {
            get {
                return this.flightKeyField;
            }
            set {
                this.flightKeyField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OfferPriceRQDataListsFlightSettlement {
        
        private string methodField;
        
        private CurrencyAmountOptType interlineSettlementValueField;
        
        /// <remarks/>
        public string Method {
            get {
                return this.methodField;
            }
            set {
                this.methodField = value;
            }
        }
        
        /// <remarks/>
        public CurrencyAmountOptType InterlineSettlementValue {
            get {
                return this.interlineSettlementValueField;
            }
            set {
                this.interlineSettlementValueField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OfferPriceRQDataListsSeatDefinition {
        
        private OfferPriceRQDataListsSeatDefinitionDescription[] descriptionField;
        
        private OfferPriceRQDataListsSeatDefinitionMarketing marketingField;
        
        private string[] seatCharacteristicCodeField;
        
        private SizeUnitSimpleType uOMField;
        
        private bool uOMFieldSpecified;
        
        private decimal seatWidthLowField;
        
        private bool seatWidthLowFieldSpecified;
        
        private decimal seatPitchLowField;
        
        private bool seatPitchLowFieldSpecified;
        
        private KeyWordType[] keywordsField;
        
        private string seatDefinitionIDField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("Description")]
        public OfferPriceRQDataListsSeatDefinitionDescription[] Description {
            get {
                return this.descriptionField;
            }
            set {
                this.descriptionField = value;
            }
        }
        
        /// <remarks/>
        public OfferPriceRQDataListsSeatDefinitionMarketing Marketing {
            get {
                return this.marketingField;
            }
            set {
                this.marketingField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("SeatCharacteristicCode")]
        public string[] SeatCharacteristicCode {
            get {
                return this.seatCharacteristicCodeField;
            }
            set {
                this.seatCharacteristicCodeField = value;
            }
        }
        
        /// <remarks/>
        public SizeUnitSimpleType UOM {
            get {
                return this.uOMField;
            }
            set {
                this.uOMField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool UOMSpecified {
            get {
                return this.uOMFieldSpecified;
            }
            set {
                this.uOMFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        public decimal SeatWidthLow {
            get {
                return this.seatWidthLowField;
            }
            set {
                this.seatWidthLowField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool SeatWidthLowSpecified {
            get {
                return this.seatWidthLowFieldSpecified;
            }
            set {
                this.seatWidthLowFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        public decimal SeatPitchLow {
            get {
                return this.seatPitchLowField;
            }
            set {
                this.seatPitchLowField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool SeatPitchLowSpecified {
            get {
                return this.seatPitchLowFieldSpecified;
            }
            set {
                this.seatPitchLowFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("KeyWord", IsNullable=false)]
        public KeyWordType[] Keywords {
            get {
                return this.keywordsField;
            }
            set {
                this.keywordsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="ID")]
        public string SeatDefinitionID {
            get {
                return this.seatDefinitionIDField;
            }
            set {
                this.seatDefinitionIDField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OfferPriceRQDataListsSeatDefinitionDescription {
        
        private OfferPriceRQDataListsSeatDefinitionDescriptionText textField;
        
        private string markupStyleField;
        
        private string linkField;
        
        private OfferPriceRQDataListsSeatDefinitionDescriptionMedia[] mediaField;
        
        /// <remarks/>
        public OfferPriceRQDataListsSeatDefinitionDescriptionText Text {
            get {
                return this.textField;
            }
            set {
                this.textField = value;
            }
        }
        
        /// <remarks/>
        public string MarkupStyle {
            get {
                return this.markupStyleField;
            }
            set {
                this.markupStyleField = value;
            }
        }
        
        /// <remarks/>
        public string Link {
            get {
                return this.linkField;
            }
            set {
                this.linkField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("Media")]
        public OfferPriceRQDataListsSeatDefinitionDescriptionMedia[] Media {
            get {
                return this.mediaField;
            }
            set {
                this.mediaField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OfferPriceRQDataListsSeatDefinitionDescriptionText {
        
        private string refsField;
        
        private string valueField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="IDREFS")]
        public string refs {
            get {
                return this.refsField;
            }
            set {
                this.refsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        public string Value {
            get {
                return this.valueField;
            }
            set {
                this.valueField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OfferPriceRQDataListsSeatDefinitionDescriptionMedia {
        
        private object itemField;
        
        private ItemChoiceType9 itemElementNameField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("AttachmentID", typeof(MediaID_Type))]
        [System.Xml.Serialization.XmlElementAttribute("MediaLink", typeof(MediaLink))]
        [System.Xml.Serialization.XmlElementAttribute("ObjectID", typeof(MediaID_Type))]
        [System.Xml.Serialization.XmlChoiceIdentifierAttribute("ItemElementName")]
        public object Item {
            get {
                return this.itemField;
            }
            set {
                this.itemField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public ItemChoiceType9 ItemElementName {
            get {
                return this.itemElementNameField;
            }
            set {
                this.itemElementNameField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class FltPriceReqMetadataTypePassenger
    {

        private PassengerMetadataType passengerMetadataField;

        /// <remarks/>
        public PassengerMetadataType PassengerMetadata
        {
            get
            {
                return this.passengerMetadataField;
            }
            set
            {
                this.passengerMetadataField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class FltPriceReqMetadataTypeShopping
    {

        private ShopMetadataGroup shopMetadataGroupField;

        /// <remarks/>
        public ShopMetadataGroup ShopMetadataGroup
        {
            get
            {
                return this.shopMetadataGroupField;
            }
            set
            {
                this.shopMetadataGroupField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class FltPriceReqMetadataTypeOtherMetadata
    {

        private object itemField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("AddressMetadatas", typeof(AddressMetadatas))]
        [System.Xml.Serialization.XmlElementAttribute("AircraftMetadatas", typeof(AircraftMetadatas))]
        [System.Xml.Serialization.XmlElementAttribute("AirportMetadatas", typeof(AirportMetadatas))]
        [System.Xml.Serialization.XmlElementAttribute("CityMetadatas", typeof(CityMetadatas))]
        [System.Xml.Serialization.XmlElementAttribute("CodesetMetadatas", typeof(CodesetMetadatas))]
        [System.Xml.Serialization.XmlElementAttribute("ContentMetadatas", typeof(ContentMetadatas))]
        [System.Xml.Serialization.XmlElementAttribute("CountryMetadatas", typeof(CountryMetadatas))]
        [System.Xml.Serialization.XmlElementAttribute("CurrencyMetadatas", typeof(CurrencyMetadatas))]
        [System.Xml.Serialization.XmlElementAttribute("DescriptionMetadatas", typeof(DescriptionMetadatas))]
        [System.Xml.Serialization.XmlElementAttribute("EquivalentID_Metadatas", typeof(EquivalentID_Metadatas))]
        [System.Xml.Serialization.XmlElementAttribute("LanguageMetadatas", typeof(LanguageMetadatas))]
        [System.Xml.Serialization.XmlElementAttribute("PaymentCardMetadatas", typeof(PaymentCardMetadatas))]
        [System.Xml.Serialization.XmlElementAttribute("PaymentFormMetadatas", typeof(PaymentFormMetadatas))]
        [System.Xml.Serialization.XmlElementAttribute("PriceMetadatas", typeof(PriceMetadatas))]
        [System.Xml.Serialization.XmlElementAttribute("RuleMetadatas", typeof(RuleMetadatas))]
        [System.Xml.Serialization.XmlElementAttribute("StateProvMetadatas", typeof(StateProvMetadatas))]
        [System.Xml.Serialization.XmlElementAttribute("ZoneMetadatas", typeof(ZoneMetadatas))]
        public object Item
        {
            get
            {
                return this.itemField;
            }
            set
            {
                this.itemField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class FltPriceReqMetadataType
    {

        private FltPriceReqMetadataTypeShopping shoppingField;

        private FltPriceReqMetadataTypePassenger passengerField;

        private FltPriceReqMetadataTypeOtherMetadata[] otherField;

        /// <remarks/>
        public FltPriceReqMetadataTypeShopping Shopping
        {
            get
            {
                return this.shoppingField;
            }
            set
            {
                this.shoppingField = value;
            }
        }

        /// <remarks/>
        public FltPriceReqMetadataTypePassenger Passenger
        {
            get
            {
                return this.passengerField;
            }
            set
            {
                this.passengerField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("OtherMetadata", IsNullable = false)]
        public FltPriceReqMetadataTypeOtherMetadata[] Other
        {
            get
            {
                return this.otherField;
            }
            set
            {
                this.otherField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OfferPriceRQDataListsSeatDefinitionMarketing : SeatMapMessageType {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OfferPriceRQMetadata : FltPriceReqMetadataType {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public enum OfferPriceRQTarget {
        
        /// <remarks/>
        Test,
        
        /// <remarks/>
        Production,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public enum OfferPriceRQTransactionStatusCode {
        
        /// <remarks/>
        Start,
        
        /// <remarks/>
        End,
        
        /// <remarks/>
        Rollback,
        
        /// <remarks/>
        InSeries,
        
        /// <remarks/>
        Continuation,
        
        /// <remarks/>
        Subsequent,
    }
}
