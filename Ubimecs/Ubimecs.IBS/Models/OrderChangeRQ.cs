//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// 
// This source code was auto-generated by xsd, Version=4.6.1055.0.
// 
namespace Ubimecs.IBS.Models {
    using System.Xml.Serialization;
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace="http://www.iata.org/IATA/EDIST/2017.2", IsNullable=false)]
    public partial class OrderChangeRQ {
        
        private PointOfSaleType pointOfSaleField;
        
        private MsgDocumentType documentField;
        
        private MsgPartiesType partyField;
        
        private OrderChangeParameters orderChangeParametersField;
        
        private OrderChangeRQQuery queryField;
        
        private OrderChangeRQDataLists dataListsField;
        
        private string echoTokenField;
        
        private System.DateTime timeStampField;
        
        private bool timeStampFieldSpecified;
        
        private OrderChangeRQTarget targetField;
        
        private string versionField;
        
        private string transactionIdentifierField;
        
        private string sequenceNmbrField;
        
        private OrderChangeRQTransactionStatusCode transactionStatusCodeField;
        
        private bool transactionStatusCodeFieldSpecified;
        
        private bool retransmissionIndicatorField;
        
        private bool retransmissionIndicatorFieldSpecified;
        
        private string correlationIDField;
        
        private bool asynchronousAllowedIndField;
        
        private bool asynchronousAllowedIndFieldSpecified;
        
        public OrderChangeRQ() {
            this.targetField = OrderChangeRQTarget.Production;
        }
        
        /// <remarks/>
        public PointOfSaleType PointOfSale {
            get {
                return this.pointOfSaleField;
            }
            set {
                this.pointOfSaleField = value;
            }
        }
        
        /// <remarks/>
        public MsgDocumentType Document {
            get {
                return this.documentField;
            }
            set {
                this.documentField = value;
            }
        }
        
        /// <remarks/>
        public MsgPartiesType Party {
            get {
                return this.partyField;
            }
            set {
                this.partyField = value;
            }
        }
        
        /// <remarks/>
        public OrderChangeParameters OrderChangeParameters {
            get {
                return this.orderChangeParametersField;
            }
            set {
                this.orderChangeParametersField = value;
            }
        }
        
        /// <remarks/>
        public OrderChangeRQQuery Query {
            get {
                return this.queryField;
            }
            set {
                this.queryField = value;
            }
        }
        
        /// <remarks/>
        public OrderChangeRQDataLists DataLists {
            get {
                return this.dataListsField;
            }
            set {
                this.dataListsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string EchoToken {
            get {
                return this.echoTokenField;
            }
            set {
                this.echoTokenField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public System.DateTime TimeStamp {
            get {
                return this.timeStampField;
            }
            set {
                this.timeStampField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool TimeStampSpecified {
            get {
                return this.timeStampFieldSpecified;
            }
            set {
                this.timeStampFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        [System.ComponentModel.DefaultValueAttribute(OrderChangeRQTarget.Production)]
        public OrderChangeRQTarget Target {
            get {
                return this.targetField;
            }
            set {
                this.targetField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Version {
            get {
                return this.versionField;
            }
            set {
                this.versionField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string TransactionIdentifier {
            get {
                return this.transactionIdentifierField;
            }
            set {
                this.transactionIdentifierField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="nonNegativeInteger")]
        public string SequenceNmbr {
            get {
                return this.sequenceNmbrField;
            }
            set {
                this.sequenceNmbrField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public OrderChangeRQTransactionStatusCode TransactionStatusCode {
            get {
                return this.transactionStatusCodeField;
            }
            set {
                this.transactionStatusCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool TransactionStatusCodeSpecified {
            get {
                return this.transactionStatusCodeFieldSpecified;
            }
            set {
                this.transactionStatusCodeFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public bool RetransmissionIndicator {
            get {
                return this.retransmissionIndicatorField;
            }
            set {
                this.retransmissionIndicatorField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool RetransmissionIndicatorSpecified {
            get {
                return this.retransmissionIndicatorFieldSpecified;
            }
            set {
                this.retransmissionIndicatorFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string CorrelationID {
            get {
                return this.correlationIDField;
            }
            set {
                this.correlationIDField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public bool AsynchronousAllowedInd {
            get {
                return this.asynchronousAllowedIndField;
            }
            set {
                this.asynchronousAllowedIndField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool AsynchronousAllowedIndSpecified {
            get {
                return this.asynchronousAllowedIndFieldSpecified;
            }
            set {
                this.asynchronousAllowedIndFieldSpecified = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace="http://www.iata.org/IATA/EDIST/2017.2", IsNullable=false)]
    public partial class OrderChangeParameters : OrdChangeParamsType {
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrdChangeParamsTypeRefundFOP_Preference
    {

        private object itemField;

        private bool originalFOP_IndField;

        private bool originalFOP_IndFieldSpecified;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("BankAccount", typeof(BankAccountType))]
        [System.Xml.Serialization.XmlElementAttribute("CarrierCredit", typeof(bool))]
        [System.Xml.Serialization.XmlElementAttribute("Cash", typeof(Cash))]
        [System.Xml.Serialization.XmlElementAttribute("Check", typeof(Check))]
        [System.Xml.Serialization.XmlElementAttribute("PaymentCard", typeof(PaymentCardType))]
        [System.Xml.Serialization.XmlElementAttribute("Voucher", typeof(Voucher))]
        public object Item
        {
            get
            {
                return this.itemField;
            }
            set
            {
                this.itemField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public bool OriginalFOP_Ind
        {
            get
            {
                return this.originalFOP_IndField;
            }
            set
            {
                this.originalFOP_IndField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool OriginalFOP_IndSpecified
        {
            get
            {
                return this.originalFOP_IndFieldSpecified;
            }
            set
            {
                this.originalFOP_IndFieldSpecified = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrdChangeParamsType
    {

        private AlertsTypeAlert[] alertsField;

        private NoticesNotice[] noticesField;

        private string reasonField;

        private OrdChangeParamsTypeRefundFOP_Preference refundFOP_PreferenceField;

        private string referencesField;

        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Alert", IsNullable = false)]
        public AlertsTypeAlert[] Alerts
        {
            get
            {
                return this.alertsField;
            }
            set
            {
                this.alertsField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Notice", IsNullable = false)]
        public NoticesNotice[] Notices
        {
            get
            {
                return this.noticesField;
            }
            set
            {
                this.noticesField = value;
            }
        }

        /// <remarks/>
        public string Reason
        {
            get
            {
                return this.reasonField;
            }
            set
            {
                this.reasonField = value;
            }
        }

        /// <remarks/>
        public OrdChangeParamsTypeRefundFOP_Preference RefundFOP_Preference
        {
            get
            {
                return this.refundFOP_PreferenceField;
            }
            set
            {
                this.refundFOP_PreferenceField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType = "IDREFS")]
        public string References
        {
            get
            {
                return this.referencesField;
            }
            set
            {
                this.referencesField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderChangeRQQuery {
        
        private OrderChangeRQQueryGroup groupField;
        
        private OrderChangeRQQueryOrderID orderIDField;
        
        private string actionContextField;
        
        private object[] itemsField;
        
        private OrderChangeRQQueryPayment[] paymentsField;
        
        private BookingReferenceType[] bookingReferencesField;
        
        private OrdChangeMetadataType orderChangeMetadataField;
        
        /// <remarks/>
        public OrderChangeRQQueryGroup Group {
            get {
                return this.groupField;
            }
            set {
                this.groupField = value;
            }
        }
        
        /// <remarks/>
        public OrderChangeRQQueryOrderID OrderID {
            get {
                return this.orderIDField;
            }
            set {
                this.orderIDField = value;
            }
        }
        
        /// <remarks/>
        public string ActionContext {
            get {
                return this.actionContextField;
            }
            set {
                this.actionContextField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("AcceptRepricedOrder", typeof(OrderChangeRQQueryAcceptRepricedOrder))]
        [System.Xml.Serialization.XmlElementAttribute("OrderServicing", typeof(OrderChangeRQQueryOrderServicing))]
        [System.Xml.Serialization.XmlElementAttribute("PassengerServicing", typeof(OrderChangeRQQueryPassengerServicing))]
        public object[] Items {
            get {
                return this.itemsField;
            }
            set {
                this.itemsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Payment", IsNullable=false)]
        public OrderChangeRQQueryPayment[] Payments {
            get {
                return this.paymentsField;
            }
            set {
                this.paymentsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("BookingReference", IsNullable=false)]
        public BookingReferenceType[] BookingReferences {
            get {
                return this.bookingReferencesField;
            }
            set {
                this.bookingReferencesField = value;
            }
        }
        
        /// <remarks/>
        public OrdChangeMetadataType OrderChangeMetadata {
            get {
                return this.orderChangeMetadataField;
            }
            set {
                this.orderChangeMetadataField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderChangeRQQueryGroup : GroupType {
        
        private OrderChangeRQQueryGroupActionType actionTypeField;
        
        /// <remarks/>
        public OrderChangeRQQueryGroupActionType ActionType {
            get {
                return this.actionTypeField;
            }
            set {
                this.actionTypeField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderChangeRQQueryGroupActionType {
        
        private string contextField;
        
        private string valueField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Context {
            get {
                return this.contextField;
            }
            set {
                this.contextField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        public string Value {
            get {
                return this.valueField;
            }
            set {
                this.valueField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderChangeRQQueryOrderID {
        
        private string ownerField;
        
        private OrderChangeRQQueryOrderIDOwnerType ownerTypeField;
        
        private bool ownerTypeFieldSpecified;
        
        private string valueField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Owner {
            get {
                return this.ownerField;
            }
            set {
                this.ownerField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public OrderChangeRQQueryOrderIDOwnerType OwnerType {
            get {
                return this.ownerTypeField;
            }
            set {
                this.ownerTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool OwnerTypeSpecified {
            get {
                return this.ownerTypeFieldSpecified;
            }
            set {
                this.ownerTypeFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        public string Value {
            get {
                return this.valueField;
            }
            set {
                this.valueField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public enum OrderChangeRQQueryOrderIDOwnerType {
        
        /// <remarks/>
        ORA,
        
        /// <remarks/>
        POA,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderChangeRQQueryAcceptRepricedOrder {
        
        private string responseIDField;
        
        /// <remarks/>
        public string ResponseID {
            get {
                return this.responseIDField;
            }
            set {
                this.responseIDField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderChangeRQQueryOrderServicing {
        
        private OrderChangeRQQueryOrderServicingAcceptOffer acceptOfferField;
        
        private string[] deleteField;
        
        /// <remarks/>
        public OrderChangeRQQueryOrderServicingAcceptOffer AcceptOffer {
            get {
                return this.acceptOfferField;
            }
            set {
                this.acceptOfferField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("OrderItemID", IsNullable=false)]
        public string[] Delete {
            get {
                return this.deleteField;
            }
            set {
                this.deleteField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderChangeRQQueryOrderServicingAcceptOffer : OrderRequestType {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderChangeRQQueryPassengerServicing {
        
        private OrderChangeRQQueryPassengerServicingNew newField;
        
        private OrderChangeRQQueryPassengerServicingPrevious previousField;
        
        /// <remarks/>
        public OrderChangeRQQueryPassengerServicingNew New {
            get {
                return this.newField;
            }
            set {
                this.newField = value;
            }
        }
        
        /// <remarks/>
        public OrderChangeRQQueryPassengerServicingPrevious Previous {
            get {
                return this.previousField;
            }
            set {
                this.previousField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderChangeRQQueryPassengerServicingNew {
        
        private string pTCField;
        
        private object itemField;
        
        private string residenceCountryCodeField;
        
        private string citizenshipCountryCodeField;
        
        private IndividualType individualField;
        
        private string profileIDField;
        
        private LoyaltyProgramAccountType[] loyaltyProgramAccountField;
        
        private FOID fOIDField;
        
        private IdentityDocumentType[] identityDocumentField;
        
        private string contactInfoRefField;
        
        private string infantRefField;
        
        private LanguageUsageType[] languageField;
        
        private OrderChangeRQQueryPassengerServicingNewRemark[] remarkField;
        
        private object actionTypeField;
        
        private string passengerIDField;
        
        private bool profileConsentIndicatorField;
        
        private bool profileConsentIndicatorFieldSpecified;
        
        /// <remarks/>
        public string PTC {
            get {
                return this.pTCField;
            }
            set {
                this.pTCField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("Age", typeof(MeasureType))]
        [System.Xml.Serialization.XmlElementAttribute("Birthdate", typeof(System.DateTime), DataType="date")]
        public object Item {
            get {
                return this.itemField;
            }
            set {
                this.itemField = value;
            }
        }
        
        /// <remarks/>
        public string ResidenceCountryCode {
            get {
                return this.residenceCountryCodeField;
            }
            set {
                this.residenceCountryCodeField = value;
            }
        }
        
        /// <remarks/>
        public string CitizenshipCountryCode {
            get {
                return this.citizenshipCountryCodeField;
            }
            set {
                this.citizenshipCountryCodeField = value;
            }
        }
        
        /// <remarks/>
        public IndividualType Individual {
            get {
                return this.individualField;
            }
            set {
                this.individualField = value;
            }
        }
        
        /// <remarks/>
        public string ProfileID {
            get {
                return this.profileIDField;
            }
            set {
                this.profileIDField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("LoyaltyProgramAccount")]
        public LoyaltyProgramAccountType[] LoyaltyProgramAccount {
            get {
                return this.loyaltyProgramAccountField;
            }
            set {
                this.loyaltyProgramAccountField = value;
            }
        }
        
        /// <remarks/>
        public FOID FOID {
            get {
                return this.fOIDField;
            }
            set {
                this.fOIDField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("IdentityDocument")]
        public IdentityDocumentType[] IdentityDocument {
            get {
                return this.identityDocumentField;
            }
            set {
                this.identityDocumentField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="IDREF")]
        public string ContactInfoRef {
            get {
                return this.contactInfoRefField;
            }
            set {
                this.contactInfoRefField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="IDREF")]
        public string InfantRef {
            get {
                return this.infantRefField;
            }
            set {
                this.infantRefField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("Language")]
        public LanguageUsageType[] Language {
            get {
                return this.languageField;
            }
            set {
                this.languageField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("Remark")]
        public OrderChangeRQQueryPassengerServicingNewRemark[] Remark {
            get {
                return this.remarkField;
            }
            set {
                this.remarkField = value;
            }
        }
        
        /// <remarks/>
        public object ActionType {
            get {
                return this.actionTypeField;
            }
            set {
                this.actionTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="token")]
        public string PassengerID {
            get {
                return this.passengerIDField;
            }
            set {
                this.passengerIDField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public bool ProfileConsentIndicator {
            get {
                return this.profileConsentIndicatorField;
            }
            set {
                this.profileConsentIndicatorField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool ProfileConsentIndicatorSpecified {
            get {
                return this.profileConsentIndicatorFieldSpecified;
            }
            set {
                this.profileConsentIndicatorFieldSpecified = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderChangeRQQueryPassengerServicingNewRemark : RemarkTypeAIDM {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderChangeRQQueryPassengerServicingPrevious {
        
        private string pTCField;
        
        private object itemField;
        
        private string residenceCountryCodeField;
        
        private string citizenshipCountryCodeField;
        
        private IndividualType individualField;
        
        private string profileIDField;
        
        private LoyaltyProgramAccountType[] loyaltyProgramAccountField;
        
        private FOID fOIDField;
        
        private IdentityDocumentType[] identityDocumentField;
        
        private string contactInfoRefField;
        
        private string infantRefField;
        
        private LanguageUsageType[] languageField;
        
        private OrderChangeRQQueryPassengerServicingPreviousRemark[] remarkField;
        
        private object actionTypeField;
        
        private string passengerIDField;
        
        private bool profileConsentIndicatorField;
        
        private bool profileConsentIndicatorFieldSpecified;
        
        /// <remarks/>
        public string PTC {
            get {
                return this.pTCField;
            }
            set {
                this.pTCField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("Age", typeof(MeasureType))]
        [System.Xml.Serialization.XmlElementAttribute("Birthdate", typeof(System.DateTime), DataType="date")]
        public object Item {
            get {
                return this.itemField;
            }
            set {
                this.itemField = value;
            }
        }
        
        /// <remarks/>
        public string ResidenceCountryCode {
            get {
                return this.residenceCountryCodeField;
            }
            set {
                this.residenceCountryCodeField = value;
            }
        }
        
        /// <remarks/>
        public string CitizenshipCountryCode {
            get {
                return this.citizenshipCountryCodeField;
            }
            set {
                this.citizenshipCountryCodeField = value;
            }
        }
        
        /// <remarks/>
        public IndividualType Individual {
            get {
                return this.individualField;
            }
            set {
                this.individualField = value;
            }
        }
        
        /// <remarks/>
        public string ProfileID {
            get {
                return this.profileIDField;
            }
            set {
                this.profileIDField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("LoyaltyProgramAccount")]
        public LoyaltyProgramAccountType[] LoyaltyProgramAccount {
            get {
                return this.loyaltyProgramAccountField;
            }
            set {
                this.loyaltyProgramAccountField = value;
            }
        }
        
        /// <remarks/>
        public FOID FOID {
            get {
                return this.fOIDField;
            }
            set {
                this.fOIDField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("IdentityDocument")]
        public IdentityDocumentType[] IdentityDocument {
            get {
                return this.identityDocumentField;
            }
            set {
                this.identityDocumentField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="IDREF")]
        public string ContactInfoRef {
            get {
                return this.contactInfoRefField;
            }
            set {
                this.contactInfoRefField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="IDREF")]
        public string InfantRef {
            get {
                return this.infantRefField;
            }
            set {
                this.infantRefField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("Language")]
        public LanguageUsageType[] Language {
            get {
                return this.languageField;
            }
            set {
                this.languageField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("Remark")]
        public OrderChangeRQQueryPassengerServicingPreviousRemark[] Remark {
            get {
                return this.remarkField;
            }
            set {
                this.remarkField = value;
            }
        }
        
        /// <remarks/>
        public object ActionType {
            get {
                return this.actionTypeField;
            }
            set {
                this.actionTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="token")]
        public string PassengerID {
            get {
                return this.passengerIDField;
            }
            set {
                this.passengerIDField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public bool ProfileConsentIndicator {
            get {
                return this.profileConsentIndicatorField;
            }
            set {
                this.profileConsentIndicatorField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool ProfileConsentIndicatorSpecified {
            get {
                return this.profileConsentIndicatorFieldSpecified;
            }
            set {
                this.profileConsentIndicatorFieldSpecified = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderChangeRQQueryPassengerServicingPreviousRemark : RemarkTypeAIDM {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderChangeRQQueryPayment : OrderPaymentFormType {
        
        private OrderChangeRQQueryPaymentOffer[] offersField;
        
        private string[] orderItemIDField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Offer", IsNullable=false)]
        public OrderChangeRQQueryPaymentOffer[] Offers {
            get {
                return this.offersField;
            }
            set {
                this.offersField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("OrderItemID")]
        public string[] OrderItemID {
            get {
                return this.orderItemIDField;
            }
            set {
                this.orderItemIDField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderChangeRQQueryPaymentOffer {
        
        private string[] offerItemIDField;
        
        private string offerIDField;
        
        private string ownerField;
        
        private string responseIDField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("OfferItemID")]
        public string[] OfferItemID {
            get {
                return this.offerItemIDField;
            }
            set {
                this.offerItemIDField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string OfferID {
            get {
                return this.offerIDField;
            }
            set {
                this.offerIDField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Owner {
            get {
                return this.ownerField;
            }
            set {
                this.ownerField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string ResponseID {
            get {
                return this.responseIDField;
            }
            set {
                this.responseIDField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    [System.Xml.Serialization.XmlRootAttribute("OrderChangeMetadata", Namespace="http://www.iata.org/IATA/EDIST/2017.2", IsNullable=false)]
    public partial class OrdChangeMetadataType {
        
        private PassengerMetadataType[] passengerMetadataField;
        
        private OrdChangeMetadataTypeOtherMetadata[] otherField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("PassengerMetadata")]
        public PassengerMetadataType[] PassengerMetadata {
            get {
                return this.passengerMetadataField;
            }
            set {
                this.passengerMetadataField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("OtherMetadata", IsNullable=false)]
        public OrdChangeMetadataTypeOtherMetadata[] Other {
            get {
                return this.otherField;
            }
            set {
                this.otherField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrdChangeMetadataTypeOtherMetadata {
        
        private object itemField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("AddressMetadatas", typeof(AddressMetadatas))]
        [System.Xml.Serialization.XmlElementAttribute("AircraftMetadatas", typeof(AircraftMetadatas))]
        [System.Xml.Serialization.XmlElementAttribute("AirportMetadatas", typeof(AirportMetadatas))]
        [System.Xml.Serialization.XmlElementAttribute("CityMetadatas", typeof(CityMetadatas))]
        [System.Xml.Serialization.XmlElementAttribute("CodesetMetadatas", typeof(CodesetMetadatas))]
        [System.Xml.Serialization.XmlElementAttribute("ContactMetadatas", typeof(ContactMetadatas))]
        [System.Xml.Serialization.XmlElementAttribute("ContentMetadatas", typeof(ContentMetadatas))]
        [System.Xml.Serialization.XmlElementAttribute("CountryMetadatas", typeof(CountryMetadatas))]
        [System.Xml.Serialization.XmlElementAttribute("CurrencyMetadatas", typeof(CurrencyMetadatas))]
        [System.Xml.Serialization.XmlElementAttribute("DescriptionMetadatas", typeof(DescriptionMetadatas))]
        [System.Xml.Serialization.XmlElementAttribute("EquivalentID_Metadatas", typeof(EquivalentID_Metadatas))]
        [System.Xml.Serialization.XmlElementAttribute("LanguageMetadatas", typeof(LanguageMetadatas))]
        [System.Xml.Serialization.XmlElementAttribute("PaymentCardMetadatas", typeof(PaymentCardMetadatas))]
        [System.Xml.Serialization.XmlElementAttribute("PaymentFormMetadatas", typeof(PaymentFormMetadatas))]
        [System.Xml.Serialization.XmlElementAttribute("PriceMetadatas", typeof(PriceMetadatas))]
        [System.Xml.Serialization.XmlElementAttribute("RuleMetadatas", typeof(RuleMetadatas))]
        [System.Xml.Serialization.XmlElementAttribute("StateProvMetadatas", typeof(StateProvMetadatas))]
        [System.Xml.Serialization.XmlElementAttribute("ZoneMetadatas", typeof(ZoneMetadatas))]
        public object Item {
            get {
                return this.itemField;
            }
            set {
                this.itemField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderChangeRQDataLists {
        
        private PassengerType[] passengerListField;
        
        private ContactInformationType[] contactListField;
        
        private ListOfBagDisclosureTypeBagDisclosure[] bagDisclosureListField;
        
        private OrderChangeRQDataListsBaggageAllowance[] baggageAllowanceListField;
        
        private ListOfClassOfServiceTypeServiceClass[] classOfServiceListField;
        
        private ListOfContentSourceTypeContentSource[] contentSourceListField;
        
        private Description[] descriptionListField;
        
        private ListOfDisclosureTypeDisclosures[] disclosureListField;
        
        private FareListFareGroup[] fareListField;
        
        private ListOfFlightSegmentType[] flightSegmentListField;
        
        private OrderChangeRQDataListsFlight[] flightListField;
        
        private OriginDestination[] originDestinationListField;
        
        private ListOfOfferInstructionsTypeInstruction[] instructionsListField;
        
        private ListOfMediaTypeMedia[] mediaListField;
        
        private ListOfOfferPenaltyTypePenalty[] penaltyListField;
        
        private PriceClassType[] priceClassListField;
        
        private ServiceDefinitionType[] serviceDefinitionListField;
        
        private ListOfOfferTermsType termsListField;
        
        private OrderChangeRQDataListsSeatDefinition[] seatDefinitionListField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Passenger", IsNullable=false)]
        public PassengerType[] PassengerList {
            get {
                return this.passengerListField;
            }
            set {
                this.passengerListField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("ContactInformation", IsNullable=false)]
        public ContactInformationType[] ContactList {
            get {
                return this.contactListField;
            }
            set {
                this.contactListField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("BagDisclosure", IsNullable=false)]
        public ListOfBagDisclosureTypeBagDisclosure[] BagDisclosureList {
            get {
                return this.bagDisclosureListField;
            }
            set {
                this.bagDisclosureListField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("BaggageAllowance", IsNullable=false)]
        public OrderChangeRQDataListsBaggageAllowance[] BaggageAllowanceList {
            get {
                return this.baggageAllowanceListField;
            }
            set {
                this.baggageAllowanceListField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("ServiceClass", IsNullable=false)]
        public ListOfClassOfServiceTypeServiceClass[] ClassOfServiceList {
            get {
                return this.classOfServiceListField;
            }
            set {
                this.classOfServiceListField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("ContentSource", IsNullable=false)]
        public ListOfContentSourceTypeContentSource[] ContentSourceList {
            get {
                return this.contentSourceListField;
            }
            set {
                this.contentSourceListField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Description", IsNullable=false)]
        public Description[] DescriptionList {
            get {
                return this.descriptionListField;
            }
            set {
                this.descriptionListField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Disclosures", IsNullable=false)]
        public ListOfDisclosureTypeDisclosures[] DisclosureList {
            get {
                return this.disclosureListField;
            }
            set {
                this.disclosureListField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("FareGroup", IsNullable=false)]
        public FareListFareGroup[] FareList {
            get {
                return this.fareListField;
            }
            set {
                this.fareListField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("FlightSegment", IsNullable=false)]
        public ListOfFlightSegmentType[] FlightSegmentList {
            get {
                return this.flightSegmentListField;
            }
            set {
                this.flightSegmentListField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Flight", IsNullable=false)]
        public OrderChangeRQDataListsFlight[] FlightList {
            get {
                return this.flightListField;
            }
            set {
                this.flightListField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("OriginDestination", IsNullable=false)]
        public OriginDestination[] OriginDestinationList {
            get {
                return this.originDestinationListField;
            }
            set {
                this.originDestinationListField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Instruction", IsNullable=false)]
        public ListOfOfferInstructionsTypeInstruction[] InstructionsList {
            get {
                return this.instructionsListField;
            }
            set {
                this.instructionsListField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Media", IsNullable=false)]
        public ListOfMediaTypeMedia[] MediaList {
            get {
                return this.mediaListField;
            }
            set {
                this.mediaListField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Penalty", IsNullable=false)]
        public ListOfOfferPenaltyTypePenalty[] PenaltyList {
            get {
                return this.penaltyListField;
            }
            set {
                this.penaltyListField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("PriceClass", IsNullable=false)]
        public PriceClassType[] PriceClassList {
            get {
                return this.priceClassListField;
            }
            set {
                this.priceClassListField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("ServiceDefinition", IsNullable=false)]
        public ServiceDefinitionType[] ServiceDefinitionList {
            get {
                return this.serviceDefinitionListField;
            }
            set {
                this.serviceDefinitionListField = value;
            }
        }
        
        /// <remarks/>
        public ListOfOfferTermsType TermsList {
            get {
                return this.termsListField;
            }
            set {
                this.termsListField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("SeatDefinition", IsNullable=false)]
        public OrderChangeRQDataListsSeatDefinition[] SeatDefinitionList {
            get {
                return this.seatDefinitionListField;
            }
            set {
                this.seatDefinitionListField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderChangeRQDataListsBaggageAllowance {
        
        private BaggageCategoryListType baggageCategoryField;
        
        private BagAllowanceDescType allowanceDescriptionField;
        
        private BagAllowanceDimensionType dimensionAllowanceField;
        
        private PieceAllowance[] pieceAllowanceField;
        
        private WeightAllowance weightAllowanceField;
        
        private BaggageDeterminingCarrierType baggageDeterminingCarrierField;
        
        private string baggageAllowanceIDField;
        
        /// <remarks/>
        public BaggageCategoryListType BaggageCategory {
            get {
                return this.baggageCategoryField;
            }
            set {
                this.baggageCategoryField = value;
            }
        }
        
        /// <remarks/>
        public BagAllowanceDescType AllowanceDescription {
            get {
                return this.allowanceDescriptionField;
            }
            set {
                this.allowanceDescriptionField = value;
            }
        }
        
        /// <remarks/>
        public BagAllowanceDimensionType DimensionAllowance {
            get {
                return this.dimensionAllowanceField;
            }
            set {
                this.dimensionAllowanceField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("PieceAllowance")]
        public PieceAllowance[] PieceAllowance {
            get {
                return this.pieceAllowanceField;
            }
            set {
                this.pieceAllowanceField = value;
            }
        }
        
        /// <remarks/>
        public WeightAllowance WeightAllowance {
            get {
                return this.weightAllowanceField;
            }
            set {
                this.weightAllowanceField = value;
            }
        }
        
        /// <remarks/>
        public BaggageDeterminingCarrierType BaggageDeterminingCarrier {
            get {
                return this.baggageDeterminingCarrierField;
            }
            set {
                this.baggageDeterminingCarrierField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="ID")]
        public string BaggageAllowanceID {
            get {
                return this.baggageAllowanceIDField;
            }
            set {
                this.baggageAllowanceIDField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderChangeRQDataListsFlight {
        
        private TotalJourneyType journeyField;
        
        private SegmentReferences segmentReferencesField;
        
        private OrderChangeRQDataListsFlightSettlement settlementField;
        
        private string refsField;
        
        private string flightKeyField;
        
        /// <remarks/>
        public TotalJourneyType Journey {
            get {
                return this.journeyField;
            }
            set {
                this.journeyField = value;
            }
        }
        
        /// <remarks/>
        public SegmentReferences SegmentReferences {
            get {
                return this.segmentReferencesField;
            }
            set {
                this.segmentReferencesField = value;
            }
        }
        
        /// <remarks/>
        public OrderChangeRQDataListsFlightSettlement Settlement {
            get {
                return this.settlementField;
            }
            set {
                this.settlementField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="IDREFS")]
        public string refs {
            get {
                return this.refsField;
            }
            set {
                this.refsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="ID")]
        public string FlightKey {
            get {
                return this.flightKeyField;
            }
            set {
                this.flightKeyField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderChangeRQDataListsFlightSettlement {
        
        private string methodField;
        
        private CurrencyAmountOptType interlineSettlementValueField;
        
        /// <remarks/>
        public string Method {
            get {
                return this.methodField;
            }
            set {
                this.methodField = value;
            }
        }
        
        /// <remarks/>
        public CurrencyAmountOptType InterlineSettlementValue {
            get {
                return this.interlineSettlementValueField;
            }
            set {
                this.interlineSettlementValueField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderChangeRQDataListsSeatDefinition {
        
        private OrderChangeRQDataListsSeatDefinitionDescription[] descriptionField;
        
        private OrderChangeRQDataListsSeatDefinitionMarketing marketingField;
        
        private string[] seatCharacteristicCodeField;
        
        private SizeUnitSimpleType uOMField;
        
        private bool uOMFieldSpecified;
        
        private decimal seatWidthLowField;
        
        private bool seatWidthLowFieldSpecified;
        
        private decimal seatPitchLowField;
        
        private bool seatPitchLowFieldSpecified;
        
        private KeyWordType[] keywordsField;
        
        private string seatDefinitionIDField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("Description")]
        public OrderChangeRQDataListsSeatDefinitionDescription[] Description {
            get {
                return this.descriptionField;
            }
            set {
                this.descriptionField = value;
            }
        }
        
        /// <remarks/>
        public OrderChangeRQDataListsSeatDefinitionMarketing Marketing {
            get {
                return this.marketingField;
            }
            set {
                this.marketingField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("SeatCharacteristicCode")]
        public string[] SeatCharacteristicCode {
            get {
                return this.seatCharacteristicCodeField;
            }
            set {
                this.seatCharacteristicCodeField = value;
            }
        }
        
        /// <remarks/>
        public SizeUnitSimpleType UOM {
            get {
                return this.uOMField;
            }
            set {
                this.uOMField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool UOMSpecified {
            get {
                return this.uOMFieldSpecified;
            }
            set {
                this.uOMFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        public decimal SeatWidthLow {
            get {
                return this.seatWidthLowField;
            }
            set {
                this.seatWidthLowField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool SeatWidthLowSpecified {
            get {
                return this.seatWidthLowFieldSpecified;
            }
            set {
                this.seatWidthLowFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        public decimal SeatPitchLow {
            get {
                return this.seatPitchLowField;
            }
            set {
                this.seatPitchLowField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool SeatPitchLowSpecified {
            get {
                return this.seatPitchLowFieldSpecified;
            }
            set {
                this.seatPitchLowFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("KeyWord", IsNullable=false)]
        public KeyWordType[] Keywords {
            get {
                return this.keywordsField;
            }
            set {
                this.keywordsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="ID")]
        public string SeatDefinitionID {
            get {
                return this.seatDefinitionIDField;
            }
            set {
                this.seatDefinitionIDField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderChangeRQDataListsSeatDefinitionDescription {
        
        private OrderChangeRQDataListsSeatDefinitionDescriptionText textField;
        
        private string markupStyleField;
        
        private string linkField;
        
        private OrderChangeRQDataListsSeatDefinitionDescriptionMedia[] mediaField;
        
        /// <remarks/>
        public OrderChangeRQDataListsSeatDefinitionDescriptionText Text {
            get {
                return this.textField;
            }
            set {
                this.textField = value;
            }
        }
        
        /// <remarks/>
        public string MarkupStyle {
            get {
                return this.markupStyleField;
            }
            set {
                this.markupStyleField = value;
            }
        }
        
        /// <remarks/>
        public string Link {
            get {
                return this.linkField;
            }
            set {
                this.linkField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("Media")]
        public OrderChangeRQDataListsSeatDefinitionDescriptionMedia[] Media {
            get {
                return this.mediaField;
            }
            set {
                this.mediaField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderChangeRQDataListsSeatDefinitionDescriptionText {
        
        private string refsField;
        
        private string valueField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="IDREFS")]
        public string refs {
            get {
                return this.refsField;
            }
            set {
                this.refsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        public string Value {
            get {
                return this.valueField;
            }
            set {
                this.valueField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderChangeRQDataListsSeatDefinitionDescriptionMedia {
        
        private object itemField;
        
        private ItemChoiceType9 itemElementNameField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("AttachmentID", typeof(MediaID_Type))]
        [System.Xml.Serialization.XmlElementAttribute("MediaLink", typeof(MediaLink))]
        [System.Xml.Serialization.XmlElementAttribute("ObjectID", typeof(MediaID_Type))]
        [System.Xml.Serialization.XmlChoiceIdentifierAttribute("ItemElementName")]
        public object Item {
            get {
                return this.itemField;
            }
            set {
                this.itemField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public ItemChoiceType9 ItemElementName {
            get {
                return this.itemElementNameField;
            }
            set {
                this.itemElementNameField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderChangeRQDataListsSeatDefinitionMarketing : SeatMapMessageType {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public enum OrderChangeRQTarget {
        
        /// <remarks/>
        Test,
        
        /// <remarks/>
        Production,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public enum OrderChangeRQTransactionStatusCode {
        
        /// <remarks/>
        Start,
        
        /// <remarks/>
        End,
        
        /// <remarks/>
        Rollback,
        
        /// <remarks/>
        InSeries,
        
        /// <remarks/>
        Continuation,
        
        /// <remarks/>
        Subsequent,
    }
}
