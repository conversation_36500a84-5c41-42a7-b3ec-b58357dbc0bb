//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// 
// This source code was auto-generated by xsd, Version=4.0.30319.33440.
// 
namespace Ubimecs.IBS.Models {
    using System.Xml.Serialization;
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace="http://www.iata.org/IATA/EDIST/2017.2", IsNullable=false)]
    public partial class OrderReshopRS {
        
        private MsgDocumentType documentField;
        
        private object[] itemsField;
        
        private string echoTokenField;
        
        private System.DateTime timeStampField;
        
        private bool timeStampFieldSpecified;
        
        private OrderReshopRSTarget targetField;
        
        private string versionField;
        
        private string transactionIdentifierField;
        
        private string sequenceNmbrField;
        
        private OrderReshopRSTransactionStatusCode transactionStatusCodeField;
        
        private bool transactionStatusCodeFieldSpecified;
        
        private bool retransmissionIndicatorField;
        
        private bool retransmissionIndicatorFieldSpecified;
        
        private string correlationIDField;
        
        private bool asynchronousAllowedIndField;
        
        private bool asynchronousAllowedIndFieldSpecified;
        
        public OrderReshopRS() {
            this.targetField = OrderReshopRSTarget.Production;
        }
        
        /// <remarks/>
        public MsgDocumentType Document {
            get {
                return this.documentField;
            }
            set {
                this.documentField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("Errors", typeof(ErrorsType))]
        [System.Xml.Serialization.XmlElementAttribute("Response", typeof(OrderReshopRSResponse))]
        [System.Xml.Serialization.XmlElementAttribute("Success", typeof(SuccessType))]
        [System.Xml.Serialization.XmlElementAttribute("Warnings", typeof(WarningsType))]
        public object[] Items {
            get {
                return this.itemsField;
            }
            set {
                this.itemsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string EchoToken {
            get {
                return this.echoTokenField;
            }
            set {
                this.echoTokenField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public System.DateTime TimeStamp {
            get {
                return this.timeStampField;
            }
            set {
                this.timeStampField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool TimeStampSpecified {
            get {
                return this.timeStampFieldSpecified;
            }
            set {
                this.timeStampFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        [System.ComponentModel.DefaultValueAttribute(OrderReshopRSTarget.Production)]
        public OrderReshopRSTarget Target {
            get {
                return this.targetField;
            }
            set {
                this.targetField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Version {
            get {
                return this.versionField;
            }
            set {
                this.versionField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string TransactionIdentifier {
            get {
                return this.transactionIdentifierField;
            }
            set {
                this.transactionIdentifierField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="nonNegativeInteger")]
        public string SequenceNmbr {
            get {
                return this.sequenceNmbrField;
            }
            set {
                this.sequenceNmbrField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public OrderReshopRSTransactionStatusCode TransactionStatusCode {
            get {
                return this.transactionStatusCodeField;
            }
            set {
                this.transactionStatusCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool TransactionStatusCodeSpecified {
            get {
                return this.transactionStatusCodeFieldSpecified;
            }
            set {
                this.transactionStatusCodeFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public bool RetransmissionIndicator {
            get {
                return this.retransmissionIndicatorField;
            }
            set {
                this.retransmissionIndicatorField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool RetransmissionIndicatorSpecified {
            get {
                return this.retransmissionIndicatorFieldSpecified;
            }
            set {
                this.retransmissionIndicatorFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string CorrelationID {
            get {
                return this.correlationIDField;
            }
            set {
                this.correlationIDField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public bool AsynchronousAllowedInd {
            get {
                return this.asynchronousAllowedIndField;
            }
            set {
                this.asynchronousAllowedIndField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool AsynchronousAllowedIndSpecified {
            get {
                return this.asynchronousAllowedIndFieldSpecified;
            }
            set {
                this.asynchronousAllowedIndFieldSpecified = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderReshopRSResponse {
        
        private ItinReshopProcessType orderReshopProcessingField;
        
        private ShoppingResponseID_Type reShoppingResponseIDField;
        
        private OrderReshopRSResponseGroup groupField;
        
        private object itemField;
        
        private OrderPenaltyType changeFeesField;
        
        private PaymentMethodTypeMethod[] paymentsField;
        
        private OrderReshopRSResponseCommission commissionField;
        
        private OrderReshopRSResponseDataLists dataListsField;
        
        private ItinReshopMetadataType metadataField;
        
        /// <remarks/>
        public ItinReshopProcessType OrderReshopProcessing {
            get {
                return this.orderReshopProcessingField;
            }
            set {
                this.orderReshopProcessingField = value;
            }
        }
        
        /// <remarks/>
        public ShoppingResponseID_Type ReShoppingResponseID {
            get {
                return this.reShoppingResponseIDField;
            }
            set {
                this.reShoppingResponseIDField = value;
            }
        }
        
        /// <remarks/>
        public OrderReshopRSResponseGroup Group {
            get {
                return this.groupField;
            }
            set {
                this.groupField = value;
            }
        }

        /// <remarks/>
        [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
        [System.SerializableAttribute()]
        [System.Diagnostics.DebuggerStepThroughAttribute()]
        [System.ComponentModel.DesignerCategoryAttribute("code")]
        [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.iata.org/IATA/EDIST/2017.2")]
        public partial class ItinReshopMetadataTypeOtherMetadata
        {

            private object itemField;

            /// <remarks/>
            [System.Xml.Serialization.XmlElementAttribute("AddressMetadatas", typeof(AddressMetadatas))]
            [System.Xml.Serialization.XmlElementAttribute("AircraftMetadatas", typeof(AircraftMetadatas))]
            [System.Xml.Serialization.XmlElementAttribute("AirportMetadatas", typeof(AirportMetadatas))]
            [System.Xml.Serialization.XmlElementAttribute("CityMetadatas", typeof(CityMetadatas))]
            [System.Xml.Serialization.XmlElementAttribute("CodesetMetadatas", typeof(CodesetMetadatas))]
            [System.Xml.Serialization.XmlElementAttribute("ContactMetadatas", typeof(ContactMetadatas))]
            [System.Xml.Serialization.XmlElementAttribute("ContentMetadatas", typeof(ContentMetadatas))]
            [System.Xml.Serialization.XmlElementAttribute("CountryMetadatas", typeof(CountryMetadatas))]
            [System.Xml.Serialization.XmlElementAttribute("CurrencyMetadatas", typeof(CurrencyMetadatas))]
            [System.Xml.Serialization.XmlElementAttribute("DescriptionMetadatas", typeof(DescriptionMetadatas))]
            [System.Xml.Serialization.XmlElementAttribute("EquivalentID_Metadatas", typeof(EquivalentID_Metadatas))]
            [System.Xml.Serialization.XmlElementAttribute("LanguageMetadatas", typeof(LanguageMetadatas))]
            [System.Xml.Serialization.XmlElementAttribute("PaymentCardMetadatas", typeof(PaymentCardMetadatas))]
            [System.Xml.Serialization.XmlElementAttribute("PaymentFormMetadatas", typeof(PaymentFormMetadatas))]
            [System.Xml.Serialization.XmlElementAttribute("PriceMetadatas", typeof(PriceMetadatas))]
            [System.Xml.Serialization.XmlElementAttribute("RuleMetadatas", typeof(RuleMetadatas))]
            [System.Xml.Serialization.XmlElementAttribute("StateProvMetadatas", typeof(StateProvMetadatas))]
            [System.Xml.Serialization.XmlElementAttribute("ZoneMetadatas", typeof(ZoneMetadatas))]
            public object Item
            {
                get
                {
                    return this.itemField;
                }
                set
                {
                    this.itemField = value;
                }
            }
        }

        /// <remarks/>
        [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
        [System.SerializableAttribute()]
        [System.Diagnostics.DebuggerStepThroughAttribute()]
        [System.ComponentModel.DesignerCategoryAttribute("code")]
        [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.iata.org/IATA/EDIST/2017.2")]
        public partial class ItinReshopMetadataType
        {

            private PassengerMetadataType[] passengerMetadataField;

            private ItinReshopMetadataTypeOtherMetadata[] otherField;

            /// <remarks/>
            [System.Xml.Serialization.XmlElementAttribute("PassengerMetadata")]
            public PassengerMetadataType[] PassengerMetadata
            {
                get
                {
                    return this.passengerMetadataField;
                }
                set
                {
                    this.passengerMetadataField = value;
                }
            }

            /// <remarks/>
            [System.Xml.Serialization.XmlArrayItemAttribute("OtherMetadata", IsNullable = false)]
            public ItinReshopMetadataTypeOtherMetadata[] Other
            {
                get
                {
                    return this.otherField;
                }
                set
                {
                    this.otherField = value;
                }
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("NoPriceChangeInd", typeof(bool))]
        [System.Xml.Serialization.XmlElementAttribute("RepricedOffer", typeof(OrderReshopRSResponseRepricedOffer))]
        [System.Xml.Serialization.XmlElementAttribute("ReshopOffers", typeof(OrderReshopRSResponseReshopOffers))]
        public object Item {
            get {
                return this.itemField;
            }
            set {
                this.itemField = value;
            }
        }
        
        /// <remarks/>
        public OrderPenaltyType ChangeFees {
            get {
                return this.changeFeesField;
            }
            set {
                this.changeFeesField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Payment", IsNullable=false)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Method", IsNullable=false, NestingLevel=1)]
        public PaymentMethodTypeMethod[] Payments {
            get {
                return this.paymentsField;
            }
            set {
                this.paymentsField = value;
            }
        }
        
        /// <remarks/>
        public OrderReshopRSResponseCommission Commission {
            get {
                return this.commissionField;
            }
            set {
                this.commissionField = value;
            }
        }
        
        /// <remarks/>
        public OrderReshopRSResponseDataLists DataLists {
            get {
                return this.dataListsField;
            }
            set {
                this.dataListsField = value;
            }
        }
        
        /// <remarks/>
        public ItinReshopMetadataType Metadata {
            get {
                return this.metadataField;
            }
            set {
                this.metadataField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    [System.Xml.Serialization.XmlRootAttribute("OrderReshopProcessing", Namespace="http://www.iata.org/IATA/EDIST/2017.2", IsNullable=false)]
    public partial class ItinReshopProcessType : OrderProcessResultType {
        
        private AlertsTypeAlert[] alertsField;
        
        private NoticesNotice[] noticesField;
        
        private RemarkType remarksField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Alert", IsNullable=false)]
        public AlertsTypeAlert[] Alerts {
            get {
                return this.alertsField;
            }
            set {
                this.alertsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Notice", IsNullable=false)]
        public NoticesNotice[] Notices {
            get {
                return this.noticesField;
            }
            set {
                this.noticesField = value;
            }
        }
        
        /// <remarks/>
        public RemarkType Remarks {
            get {
                return this.remarksField;
            }
            set {
                this.remarksField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderReshopRSResponseGroup : GroupType {
        
        private OrderReshopRSResponseGroupActionType actionTypeField;
        
        /// <remarks/>
        public OrderReshopRSResponseGroupActionType ActionType {
            get {
                return this.actionTypeField;
            }
            set {
                this.actionTypeField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderReshopRSResponseGroupActionType {
        
        private string contextField;
        
        private string valueField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Context {
            get {
                return this.contextField;
            }
            set {
                this.contextField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        public string Value {
            get {
                return this.valueField;
            }
            set {
                this.valueField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderReshopRSResponseRepricedOffer {
        
        private OrderReshopRSResponseRepricedOfferRepricedOfferItem[] repricedOfferItemField;
        
        private string offerIDField;
        
        private string ownerField;
        
        private string ownerTypeField;
        
        private bool requestedDateIndField;
        
        private bool requestedDateIndFieldSpecified;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("RepricedOfferItem")]
        public OrderReshopRSResponseRepricedOfferRepricedOfferItem[] RepricedOfferItem {
            get {
                return this.repricedOfferItemField;
            }
            set {
                this.repricedOfferItemField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="ID")]
        public string OfferID {
            get {
                return this.offerIDField;
            }
            set {
                this.offerIDField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Owner {
            get {
                return this.ownerField;
            }
            set {
                this.ownerField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string OwnerType {
            get {
                return this.ownerTypeField;
            }
            set {
                this.ownerTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public bool RequestedDateInd {
            get {
                return this.requestedDateIndField;
            }
            set {
                this.requestedDateIndField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool RequestedDateIndSpecified {
            get {
                return this.requestedDateIndFieldSpecified;
            }
            set {
                this.requestedDateIndFieldSpecified = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderReshopRSResponseRepricedOfferRepricedOfferItem {
        
        private OrderReshopRSResponseRepricedOfferRepricedOfferItemTotalPriceDetail totalPriceDetailField;
        
        private FareDetailType[] fareDetailField;
        
        private OrderReshopRSResponseRepricedOfferRepricedOfferItemOriginalOrderItem originalOrderItemField;
        
        private string offerItemIDField;
        
        /// <remarks/>
        public OrderReshopRSResponseRepricedOfferRepricedOfferItemTotalPriceDetail TotalPriceDetail {
            get {
                return this.totalPriceDetailField;
            }
            set {
                this.totalPriceDetailField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("FareDetail")]
        public FareDetailType[] FareDetail {
            get {
                return this.fareDetailField;
            }
            set {
                this.fareDetailField = value;
            }
        }
        
        /// <remarks/>
        public OrderReshopRSResponseRepricedOfferRepricedOfferItemOriginalOrderItem OriginalOrderItem {
            get {
                return this.originalOrderItemField;
            }
            set {
                this.originalOrderItemField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="ID")]
        public string OfferItemID {
            get {
                return this.offerItemIDField;
            }
            set {
                this.offerItemIDField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderReshopRSResponseRepricedOfferRepricedOfferItemTotalPriceDetail {
        
        private OrderReshopRSResponseRepricedOfferRepricedOfferItemTotalPriceDetailTotalAmount totalAmountField;
        
        private CurrencyAmountOptType baseAmountField;
        
        private FareFilingType fareFiledInField;
        
        private DiscountType discountField;
        
        private FeeSurchargeType[] surchargesField;
        
        private TaxDetailType taxesField;
        
        private TaxExemptionType taxExemptionField;
        
        private object itemField;
        
        private OrderReshopRSResponseRepricedOfferRepricedOfferItemTotalPriceDetailFees feesField;
        
        /// <remarks/>
        public OrderReshopRSResponseRepricedOfferRepricedOfferItemTotalPriceDetailTotalAmount TotalAmount {
            get {
                return this.totalAmountField;
            }
            set {
                this.totalAmountField = value;
            }
        }
        
        /// <remarks/>
        public CurrencyAmountOptType BaseAmount {
            get {
                return this.baseAmountField;
            }
            set {
                this.baseAmountField = value;
            }
        }
        
        /// <remarks/>
        public FareFilingType FareFiledIn {
            get {
                return this.fareFiledInField;
            }
            set {
                this.fareFiledInField = value;
            }
        }
        
        /// <remarks/>
        public DiscountType Discount {
            get {
                return this.discountField;
            }
            set {
                this.discountField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Surcharge", IsNullable=false)]
        public FeeSurchargeType[] Surcharges {
            get {
                return this.surchargesField;
            }
            set {
                this.surchargesField = value;
            }
        }
        
        /// <remarks/>
        public TaxDetailType Taxes {
            get {
                return this.taxesField;
            }
            set {
                this.taxesField = value;
            }
        }
        
        /// <remarks/>
        public TaxExemptionType TaxExemption {
            get {
                return this.taxExemptionField;
            }
            set {
                this.taxExemptionField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("AwardPricing", typeof(AwardPriceUnitType))]
        [System.Xml.Serialization.XmlElementAttribute("CombinationPricing", typeof(CombinationPriceType))]
        public object Item {
            get {
                return this.itemField;
            }
            set {
                this.itemField = value;
            }
        }
        
        /// <remarks/>
        public OrderReshopRSResponseRepricedOfferRepricedOfferItemTotalPriceDetailFees Fees {
            get {
                return this.feesField;
            }
            set {
                this.feesField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderReshopRSResponseRepricedOfferRepricedOfferItemTotalPriceDetailTotalAmount {
        
        private object itemField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("AwardPricing", typeof(AwardPriceUnitType))]
        [System.Xml.Serialization.XmlElementAttribute("CombinationPricing", typeof(CombinationPriceType))]
        [System.Xml.Serialization.XmlElementAttribute("DetailCurrencyPrice", typeof(DetailCurrencyPriceType))]
        [System.Xml.Serialization.XmlElementAttribute("EncodedCurrencyPrice", typeof(EncodedPriceType))]
        [System.Xml.Serialization.XmlElementAttribute("SimpleCurrencyPrice", typeof(SimpleCurrencyPriceType))]
        public object Item {
            get {
                return this.itemField;
            }
            set {
                this.itemField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderReshopRSResponseRepricedOfferRepricedOfferItemTotalPriceDetailFees : FeeSurchargeType {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderReshopRSResponseRepricedOfferRepricedOfferItemOriginalOrderItem {
        
        private OrderReshopRSResponseRepricedOfferRepricedOfferItemOriginalOrderItemTotalPriceDetail totalPriceDetailField;
        
        private FareDetailType[] fareDetailField;
        
        private string orderItemIDField;
        
        /// <remarks/>
        public OrderReshopRSResponseRepricedOfferRepricedOfferItemOriginalOrderItemTotalPriceDetail TotalPriceDetail {
            get {
                return this.totalPriceDetailField;
            }
            set {
                this.totalPriceDetailField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("FareDetail")]
        public FareDetailType[] FareDetail {
            get {
                return this.fareDetailField;
            }
            set {
                this.fareDetailField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string OrderItemID {
            get {
                return this.orderItemIDField;
            }
            set {
                this.orderItemIDField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderReshopRSResponseRepricedOfferRepricedOfferItemOriginalOrderItemTotalPriceDetail {
        
        private OrderReshopRSResponseRepricedOfferRepricedOfferItemOriginalOrderItemTotalPriceDetailTotalAmount totalAmountField;
        
        private CurrencyAmountOptType baseAmountField;
        
        private FareFilingType fareFiledInField;
        
        private DiscountType discountField;
        
        private FeeSurchargeType[] surchargesField;
        
        private TaxDetailType taxesField;
        
        private TaxExemptionType taxExemptionField;
        
        private object itemField;
        
        private OrderReshopRSResponseRepricedOfferRepricedOfferItemOriginalOrderItemTotalPriceDetailFees feesField;
        
        /// <remarks/>
        public OrderReshopRSResponseRepricedOfferRepricedOfferItemOriginalOrderItemTotalPriceDetailTotalAmount TotalAmount {
            get {
                return this.totalAmountField;
            }
            set {
                this.totalAmountField = value;
            }
        }
        
        /// <remarks/>
        public CurrencyAmountOptType BaseAmount {
            get {
                return this.baseAmountField;
            }
            set {
                this.baseAmountField = value;
            }
        }
        
        /// <remarks/>
        public FareFilingType FareFiledIn {
            get {
                return this.fareFiledInField;
            }
            set {
                this.fareFiledInField = value;
            }
        }
        
        /// <remarks/>
        public DiscountType Discount {
            get {
                return this.discountField;
            }
            set {
                this.discountField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Surcharge", IsNullable=false)]
        public FeeSurchargeType[] Surcharges {
            get {
                return this.surchargesField;
            }
            set {
                this.surchargesField = value;
            }
        }
        
        /// <remarks/>
        public TaxDetailType Taxes {
            get {
                return this.taxesField;
            }
            set {
                this.taxesField = value;
            }
        }
        
        /// <remarks/>
        public TaxExemptionType TaxExemption {
            get {
                return this.taxExemptionField;
            }
            set {
                this.taxExemptionField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("AwardPricing", typeof(AwardPriceUnitType))]
        [System.Xml.Serialization.XmlElementAttribute("CombinationPricing", typeof(CombinationPriceType))]
        public object Item {
            get {
                return this.itemField;
            }
            set {
                this.itemField = value;
            }
        }
        
        /// <remarks/>
        public OrderReshopRSResponseRepricedOfferRepricedOfferItemOriginalOrderItemTotalPriceDetailFees Fees {
            get {
                return this.feesField;
            }
            set {
                this.feesField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderReshopRSResponseRepricedOfferRepricedOfferItemOriginalOrderItemTotalPriceDetailTotalAmount {
        
        private object itemField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("AwardPricing", typeof(AwardPriceUnitType))]
        [System.Xml.Serialization.XmlElementAttribute("CombinationPricing", typeof(CombinationPriceType))]
        [System.Xml.Serialization.XmlElementAttribute("DetailCurrencyPrice", typeof(DetailCurrencyPriceType))]
        [System.Xml.Serialization.XmlElementAttribute("EncodedCurrencyPrice", typeof(EncodedPriceType))]
        [System.Xml.Serialization.XmlElementAttribute("SimpleCurrencyPrice", typeof(SimpleCurrencyPriceType))]
        public object Item {
            get {
                return this.itemField;
            }
            set {
                this.itemField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderReshopRSResponseRepricedOfferRepricedOfferItemOriginalOrderItemTotalPriceDetailFees : FeeSurchargeType {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderReshopRSResponseReshopOffers {
        
        private OrderReshopRSResponseReshopOffersReshopOffer[] reshopOfferField;
        
        private OrderReshopRSResponseReshopOffersALaCarteOffer aLaCarteOfferField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("ReshopOffer")]
        public OrderReshopRSResponseReshopOffersReshopOffer[] ReshopOffer {
            get {
                return this.reshopOfferField;
            }
            set {
                this.reshopOfferField = value;
            }
        }
        
        /// <remarks/>
        public OrderReshopRSResponseReshopOffersALaCarteOffer ALaCarteOffer {
            get {
                return this.aLaCarteOfferField;
            }
            set {
                this.aLaCarteOfferField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderReshopRSResponseReshopOffersReshopOffer : OfferType {
        
        private OrderReshopRSResponseReshopOffersReshopOfferFlightsOverview flightsOverviewField;
        
        private OrderReshopRSResponseReshopOffersReshopOfferDeleteOfferItem[] deleteOfferItemField;
        
        private OrderReshopRSResponseReshopOffersReshopOfferAddOfferItem[] addOfferItemField;
        
        private OrderReshopRSResponseReshopOffersReshopOfferNameChangeOfferItem nameChangeOfferItemField;
        
        private OrderReshopRSResponseReshopOffersReshopOfferBaggageAllowance[] baggageAllowanceField;
        
        /// <remarks/>
        public OrderReshopRSResponseReshopOffersReshopOfferFlightsOverview FlightsOverview {
            get {
                return this.flightsOverviewField;
            }
            set {
                this.flightsOverviewField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("DeleteOfferItem")]
        public OrderReshopRSResponseReshopOffersReshopOfferDeleteOfferItem[] DeleteOfferItem {
            get {
                return this.deleteOfferItemField;
            }
            set {
                this.deleteOfferItemField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("AddOfferItem")]
        public OrderReshopRSResponseReshopOffersReshopOfferAddOfferItem[] AddOfferItem {
            get {
                return this.addOfferItemField;
            }
            set {
                this.addOfferItemField = value;
            }
        }
        
        /// <remarks/>
        public OrderReshopRSResponseReshopOffersReshopOfferNameChangeOfferItem NameChangeOfferItem {
            get {
                return this.nameChangeOfferItemField;
            }
            set {
                this.nameChangeOfferItemField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("BaggageAllowance")]
        public OrderReshopRSResponseReshopOffersReshopOfferBaggageAllowance[] BaggageAllowance {
            get {
                return this.baggageAllowanceField;
            }
            set {
                this.baggageAllowanceField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderReshopRSResponseReshopOffersReshopOfferFlightsOverview {
        
        private OrderReshopRSResponseReshopOffersReshopOfferFlightsOverviewFlightRef[] flightRefField;
        
        private string itineraryPriceClassRefField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("FlightRef")]
        public OrderReshopRSResponseReshopOffersReshopOfferFlightsOverviewFlightRef[] FlightRef {
            get {
                return this.flightRefField;
            }
            set {
                this.flightRefField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="IDREF")]
        public string ItineraryPriceClassRef {
            get {
                return this.itineraryPriceClassRefField;
            }
            set {
                this.itineraryPriceClassRefField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderReshopRSResponseReshopOffersReshopOfferFlightsOverviewFlightRef {
        
        private string oDRefField;
        
        private string priceClassRefField;
        
        private string valueField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="IDREF")]
        public string ODRef {
            get {
                return this.oDRefField;
            }
            set {
                this.oDRefField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="IDREF")]
        public string PriceClassRef {
            get {
                return this.priceClassRefField;
            }
            set {
                this.priceClassRefField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute(DataType="IDREF")]
        public string Value {
            get {
                return this.valueField;
            }
            set {
                this.valueField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderReshopRSResponseReshopOffersReshopOfferDeleteOfferItem {
        
        private OrderReshopRSResponseReshopOffersReshopOfferDeleteOfferItemReshopDifferential reshopDifferentialField;
        
        private string offerItemIDField;
        
        private string orderItemIDField;
        
        private bool mandatoryIndField;
        
        private bool mandatoryIndFieldSpecified;
        
        private bool modificationProhibitedIndField;
        
        private bool modificationProhibitedIndFieldSpecified;
        
        /// <remarks/>
        public OrderReshopRSResponseReshopOffersReshopOfferDeleteOfferItemReshopDifferential ReshopDifferential {
            get {
                return this.reshopDifferentialField;
            }
            set {
                this.reshopDifferentialField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="ID")]
        public string OfferItemID {
            get {
                return this.offerItemIDField;
            }
            set {
                this.offerItemIDField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string OrderItemID {
            get {
                return this.orderItemIDField;
            }
            set {
                this.orderItemIDField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public bool MandatoryInd {
            get {
                return this.mandatoryIndField;
            }
            set {
                this.mandatoryIndField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool MandatoryIndSpecified {
            get {
                return this.mandatoryIndFieldSpecified;
            }
            set {
                this.mandatoryIndFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public bool ModificationProhibitedInd {
            get {
                return this.modificationProhibitedIndField;
            }
            set {
                this.modificationProhibitedIndField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool ModificationProhibitedIndSpecified {
            get {
                return this.modificationProhibitedIndFieldSpecified;
            }
            set {
                this.modificationProhibitedIndFieldSpecified = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderReshopRSResponseReshopOffersReshopOfferDeleteOfferItemReshopDifferential {
        
        private OrderReshopRSResponseReshopOffersReshopOfferDeleteOfferItemReshopDifferentialOriginalOrderItem originalOrderItemField;
        
        private OrderReshopRSResponseReshopOffersReshopOfferDeleteOfferItemReshopDifferentialNewOfferItem newOfferItemField;
        
        private OrderReshopRSResponseReshopOffersReshopOfferDeleteOfferItemReshopDifferentialPenaltyAmount penaltyAmountField;
        
        private OrderReshopRSResponseReshopOffersReshopOfferDeleteOfferItemReshopDifferentialFeesAmount feesAmountField;
        
        private OrderReshopRSResponseReshopOffersReshopOfferDeleteOfferItemReshopDifferentialReshopDue reshopDueField;
        
        /// <remarks/>
        public OrderReshopRSResponseReshopOffersReshopOfferDeleteOfferItemReshopDifferentialOriginalOrderItem OriginalOrderItem {
            get {
                return this.originalOrderItemField;
            }
            set {
                this.originalOrderItemField = value;
            }
        }
        
        /// <remarks/>
        public OrderReshopRSResponseReshopOffersReshopOfferDeleteOfferItemReshopDifferentialNewOfferItem NewOfferItem {
            get {
                return this.newOfferItemField;
            }
            set {
                this.newOfferItemField = value;
            }
        }
        
        /// <remarks/>
        public OrderReshopRSResponseReshopOffersReshopOfferDeleteOfferItemReshopDifferentialPenaltyAmount PenaltyAmount {
            get {
                return this.penaltyAmountField;
            }
            set {
                this.penaltyAmountField = value;
            }
        }
        
        /// <remarks/>
        public OrderReshopRSResponseReshopOffersReshopOfferDeleteOfferItemReshopDifferentialFeesAmount FeesAmount {
            get {
                return this.feesAmountField;
            }
            set {
                this.feesAmountField = value;
            }
        }
        
        /// <remarks/>
        public OrderReshopRSResponseReshopOffersReshopOfferDeleteOfferItemReshopDifferentialReshopDue ReshopDue {
            get {
                return this.reshopDueField;
            }
            set {
                this.reshopDueField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderReshopRSResponseReshopOffersReshopOfferDeleteOfferItemReshopDifferentialOriginalOrderItem {
        
        private TotalFareTransactionType totalField;
        
        private TaxDetailType taxesField;
        
        /// <remarks/>
        public TotalFareTransactionType Total {
            get {
                return this.totalField;
            }
            set {
                this.totalField = value;
            }
        }
        
        /// <remarks/>
        public TaxDetailType Taxes {
            get {
                return this.taxesField;
            }
            set {
                this.taxesField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderReshopRSResponseReshopOffersReshopOfferDeleteOfferItemReshopDifferentialNewOfferItem {
        
        private TotalFareTransactionType totalField;
        
        private TaxDetailType taxesField;
        
        /// <remarks/>
        public TotalFareTransactionType Total {
            get {
                return this.totalField;
            }
            set {
                this.totalField = value;
            }
        }
        
        /// <remarks/>
        public TaxDetailType Taxes {
            get {
                return this.taxesField;
            }
            set {
                this.taxesField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderReshopRSResponseReshopOffersReshopOfferDeleteOfferItemReshopDifferentialPenaltyAmount {
        
        private TotalFareTransactionType totalField;
        
        private TaxDetailType taxesField;
        
        /// <remarks/>
        public TotalFareTransactionType Total {
            get {
                return this.totalField;
            }
            set {
                this.totalField = value;
            }
        }
        
        /// <remarks/>
        public TaxDetailType Taxes {
            get {
                return this.taxesField;
            }
            set {
                this.taxesField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderReshopRSResponseReshopOffersReshopOfferDeleteOfferItemReshopDifferentialFeesAmount {
        
        private TotalFareTransactionType totalField;
        
        private TaxDetailType taxesField;
        
        /// <remarks/>
        public TotalFareTransactionType Total {
            get {
                return this.totalField;
            }
            set {
                this.totalField = value;
            }
        }
        
        /// <remarks/>
        public TaxDetailType Taxes {
            get {
                return this.taxesField;
            }
            set {
                this.taxesField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderReshopRSResponseReshopOffersReshopOfferDeleteOfferItemReshopDifferentialReshopDue {
        
        private object itemField;
        
        private TaxDetailType taxesField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("ByAirline", typeof(OrderReshopRSResponseReshopOffersReshopOfferDeleteOfferItemReshopDifferentialReshopDueByAirline))]
        [System.Xml.Serialization.XmlElementAttribute("ByPassenger", typeof(OrderReshopRSResponseReshopOffersReshopOfferDeleteOfferItemReshopDifferentialReshopDueByPassenger))]
        public object Item {
            get {
                return this.itemField;
            }
            set {
                this.itemField = value;
            }
        }
        
        /// <remarks/>
        public TaxDetailType Taxes {
            get {
                return this.taxesField;
            }
            set {
                this.taxesField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderReshopRSResponseReshopOffersReshopOfferDeleteOfferItemReshopDifferentialReshopDueByAirline {
        
        private TotalFareTransactionType totalField;
        
        /// <remarks/>
        public TotalFareTransactionType Total {
            get {
                return this.totalField;
            }
            set {
                this.totalField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderReshopRSResponseReshopOffersReshopOfferDeleteOfferItemReshopDifferentialReshopDueByPassenger {
        
        private TotalFareTransactionType totalField;
        
        /// <remarks/>
        public TotalFareTransactionType Total {
            get {
                return this.totalField;
            }
            set {
                this.totalField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderReshopRSResponseReshopOffersReshopOfferAddOfferItem : OfferItemType {
        
        private OrderReshopRSResponseReshopOffersReshopOfferAddOfferItemReshopDifferential reshopDifferentialField;
        
        private string orderItemIDsField;
        
        /// <remarks/>
        public OrderReshopRSResponseReshopOffersReshopOfferAddOfferItemReshopDifferential ReshopDifferential {
            get {
                return this.reshopDifferentialField;
            }
            set {
                this.reshopDifferentialField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string OrderItemIDs {
            get {
                return this.orderItemIDsField;
            }
            set {
                this.orderItemIDsField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderReshopRSResponseReshopOffersReshopOfferAddOfferItemReshopDifferential {
        
        private OrderReshopRSResponseReshopOffersReshopOfferAddOfferItemReshopDifferentialOriginalOrderItem originalOrderItemField;
        
        private OrderReshopRSResponseReshopOffersReshopOfferAddOfferItemReshopDifferentialNewOfferItem newOfferItemField;
        
        private OrderReshopRSResponseReshopOffersReshopOfferAddOfferItemReshopDifferentialPenaltyAmount penaltyAmountField;
        
        private OrderReshopRSResponseReshopOffersReshopOfferAddOfferItemReshopDifferentialFeesAmount feesAmountField;
        
        private OrderReshopRSResponseReshopOffersReshopOfferAddOfferItemReshopDifferentialReshopDue reshopDueField;
        
        /// <remarks/>
        public OrderReshopRSResponseReshopOffersReshopOfferAddOfferItemReshopDifferentialOriginalOrderItem OriginalOrderItem {
            get {
                return this.originalOrderItemField;
            }
            set {
                this.originalOrderItemField = value;
            }
        }
        
        /// <remarks/>
        public OrderReshopRSResponseReshopOffersReshopOfferAddOfferItemReshopDifferentialNewOfferItem NewOfferItem {
            get {
                return this.newOfferItemField;
            }
            set {
                this.newOfferItemField = value;
            }
        }
        
        /// <remarks/>
        public OrderReshopRSResponseReshopOffersReshopOfferAddOfferItemReshopDifferentialPenaltyAmount PenaltyAmount {
            get {
                return this.penaltyAmountField;
            }
            set {
                this.penaltyAmountField = value;
            }
        }
        
        /// <remarks/>
        public OrderReshopRSResponseReshopOffersReshopOfferAddOfferItemReshopDifferentialFeesAmount FeesAmount {
            get {
                return this.feesAmountField;
            }
            set {
                this.feesAmountField = value;
            }
        }
        
        /// <remarks/>
        public OrderReshopRSResponseReshopOffersReshopOfferAddOfferItemReshopDifferentialReshopDue ReshopDue {
            get {
                return this.reshopDueField;
            }
            set {
                this.reshopDueField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderReshopRSResponseReshopOffersReshopOfferAddOfferItemReshopDifferentialOriginalOrderItem {
        
        private TotalFareTransactionType totalField;
        
        private TaxDetailType taxesField;
        
        /// <remarks/>
        public TotalFareTransactionType Total {
            get {
                return this.totalField;
            }
            set {
                this.totalField = value;
            }
        }
        
        /// <remarks/>
        public TaxDetailType Taxes {
            get {
                return this.taxesField;
            }
            set {
                this.taxesField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderReshopRSResponseReshopOffersReshopOfferAddOfferItemReshopDifferentialNewOfferItem {
        
        private TotalFareTransactionType totalField;
        
        private TaxDetailType taxesField;
        
        /// <remarks/>
        public TotalFareTransactionType Total {
            get {
                return this.totalField;
            }
            set {
                this.totalField = value;
            }
        }
        
        /// <remarks/>
        public TaxDetailType Taxes {
            get {
                return this.taxesField;
            }
            set {
                this.taxesField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderReshopRSResponseReshopOffersReshopOfferAddOfferItemReshopDifferentialPenaltyAmount {
        
        private TotalFareTransactionType totalField;
        
        private TaxDetailType taxesField;
        
        /// <remarks/>
        public TotalFareTransactionType Total {
            get {
                return this.totalField;
            }
            set {
                this.totalField = value;
            }
        }
        
        /// <remarks/>
        public TaxDetailType Taxes {
            get {
                return this.taxesField;
            }
            set {
                this.taxesField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderReshopRSResponseReshopOffersReshopOfferAddOfferItemReshopDifferentialFeesAmount {
        
        private TotalFareTransactionType totalField;
        
        private TaxDetailType taxesField;
        
        /// <remarks/>
        public TotalFareTransactionType Total {
            get {
                return this.totalField;
            }
            set {
                this.totalField = value;
            }
        }
        
        /// <remarks/>
        public TaxDetailType Taxes {
            get {
                return this.taxesField;
            }
            set {
                this.taxesField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderReshopRSResponseReshopOffersReshopOfferAddOfferItemReshopDifferentialReshopDue {
        
        private object itemField;
        
        private TaxDetailType taxesField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("ByAirline", typeof(OrderReshopRSResponseReshopOffersReshopOfferAddOfferItemReshopDifferentialReshopDueByAirline))]
        [System.Xml.Serialization.XmlElementAttribute("ByPassenger", typeof(OrderReshopRSResponseReshopOffersReshopOfferAddOfferItemReshopDifferentialReshopDueByPassenger))]
        public object Item {
            get {
                return this.itemField;
            }
            set {
                this.itemField = value;
            }
        }
        
        /// <remarks/>
        public TaxDetailType Taxes {
            get {
                return this.taxesField;
            }
            set {
                this.taxesField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderReshopRSResponseReshopOffersReshopOfferAddOfferItemReshopDifferentialReshopDueByAirline {
        
        private TotalFareTransactionType totalField;
        
        /// <remarks/>
        public TotalFareTransactionType Total {
            get {
                return this.totalField;
            }
            set {
                this.totalField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderReshopRSResponseReshopOffersReshopOfferAddOfferItemReshopDifferentialReshopDueByPassenger {
        
        private TotalFareTransactionType totalField;
        
        /// <remarks/>
        public TotalFareTransactionType Total {
            get {
                return this.totalField;
            }
            set {
                this.totalField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderReshopRSResponseReshopOffersReshopOfferNameChangeOfferItem : NameChangeOfferItemType {
        
        private OrderReshopRSResponseReshopOffersReshopOfferNameChangeOfferItemNameChange nameChangeField;
        
        /// <remarks/>
        public OrderReshopRSResponseReshopOffersReshopOfferNameChangeOfferItemNameChange NameChange {
            get {
                return this.nameChangeField;
            }
            set {
                this.nameChangeField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderReshopRSResponseReshopOffersReshopOfferNameChangeOfferItemNameChange {
        
        private string passengerIDField;
        
        private string nameTitleField;
        
        private string[] givenNameField;
        
        private string[] middleNameField;
        
        private string surnameField;
        
        private string surnameSuffixField;
        
        /// <remarks/>
        public string PassengerID {
            get {
                return this.passengerIDField;
            }
            set {
                this.passengerIDField = value;
            }
        }
        
        /// <remarks/>
        public string NameTitle {
            get {
                return this.nameTitleField;
            }
            set {
                this.nameTitleField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("GivenName")]
        public string[] GivenName {
            get {
                return this.givenNameField;
            }
            set {
                this.givenNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("MiddleName")]
        public string[] MiddleName {
            get {
                return this.middleNameField;
            }
            set {
                this.middleNameField = value;
            }
        }
        
        /// <remarks/>
        public string Surname {
            get {
                return this.surnameField;
            }
            set {
                this.surnameField = value;
            }
        }
        
        /// <remarks/>
        public string SurnameSuffix {
            get {
                return this.surnameSuffixField;
            }
            set {
                this.surnameSuffixField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderReshopRSResponseReshopOffersReshopOfferBaggageAllowance {
        
        private string flightRefsField;
        
        private string passengerRefsField;
        
        private string baggageAllowanceRefField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="IDREFS")]
        public string FlightRefs {
            get {
                return this.flightRefsField;
            }
            set {
                this.flightRefsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="IDREFS")]
        public string PassengerRefs {
            get {
                return this.passengerRefsField;
            }
            set {
                this.passengerRefsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="IDREF")]
        public string BaggageAllowanceRef {
            get {
                return this.baggageAllowanceRefField;
            }
            set {
                this.baggageAllowanceRefField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderReshopRSResponseReshopOffersALaCarteOffer : OfferType {
        
        private ALaCarteOfferItemType[] aLaCarteOfferItemField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("ALaCarteOfferItem")]
        public ALaCarteOfferItemType[] ALaCarteOfferItem {
            get {
                return this.aLaCarteOfferItemField;
            }
            set {
                this.aLaCarteOfferItemField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderReshopRSResponseCommission : CommissionType {
        
        private OrderReshopRSResponseCommissionActionType actionTypeField;
        
        /// <remarks/>
        public OrderReshopRSResponseCommissionActionType ActionType {
            get {
                return this.actionTypeField;
            }
            set {
                this.actionTypeField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderReshopRSResponseCommissionActionType {
        
        private string contextField;
        
        private string valueField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Context {
            get {
                return this.contextField;
            }
            set {
                this.contextField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        public string Value {
            get {
                return this.valueField;
            }
            set {
                this.valueField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderReshopRSResponseDataLists {
        
        private PassengerType[] passengerListField;
        
        private ContactInformationType[] contactListField;
        
        private ListOfBagDisclosureTypeBagDisclosure[] bagDisclosureListField;
        
        private OrderReshopRSResponseDataListsBaggageAllowance[] baggageAllowanceListField;
        
        private ListOfClassOfServiceTypeServiceClass[] classOfServiceListField;
        
        private ListOfContentSourceTypeContentSource[] contentSourceListField;
        
        private Description[] descriptionListField;
        
        private ListOfDisclosureTypeDisclosures[] disclosureListField;
        
        private FareListFareGroup[] fareListField;
        
        private ListOfFlightSegmentType[] flightSegmentListField;
        
        private OrderReshopRSResponseDataListsFlight[] flightListField;
        
        private OriginDestination[] originDestinationListField;
        
        private ListOfOfferInstructionsTypeInstruction[] instructionsListField;
        
        private ListOfMediaTypeMedia[] mediaListField;
        
        private ListOfOfferPenaltyTypePenalty[] penaltyListField;
        
        private PriceClassType[] priceClassListField;
        
        private ServiceDefinitionType[] serviceDefinitionListField;
        
        private ListOfOfferTermsType termsListField;
        
        private OrderReshopRSResponseDataListsSeatDefinition[] seatDefinitionListField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Passenger", IsNullable=false)]
        public PassengerType[] PassengerList {
            get {
                return this.passengerListField;
            }
            set {
                this.passengerListField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("ContactInformation", IsNullable=false)]
        public ContactInformationType[] ContactList {
            get {
                return this.contactListField;
            }
            set {
                this.contactListField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("BagDisclosure", IsNullable=false)]
        public ListOfBagDisclosureTypeBagDisclosure[] BagDisclosureList {
            get {
                return this.bagDisclosureListField;
            }
            set {
                this.bagDisclosureListField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("BaggageAllowance", IsNullable=false)]
        public OrderReshopRSResponseDataListsBaggageAllowance[] BaggageAllowanceList {
            get {
                return this.baggageAllowanceListField;
            }
            set {
                this.baggageAllowanceListField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("ServiceClass", IsNullable=false)]
        public ListOfClassOfServiceTypeServiceClass[] ClassOfServiceList {
            get {
                return this.classOfServiceListField;
            }
            set {
                this.classOfServiceListField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("ContentSource", IsNullable=false)]
        public ListOfContentSourceTypeContentSource[] ContentSourceList {
            get {
                return this.contentSourceListField;
            }
            set {
                this.contentSourceListField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Description", IsNullable=false)]
        public Description[] DescriptionList {
            get {
                return this.descriptionListField;
            }
            set {
                this.descriptionListField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Disclosures", IsNullable=false)]
        public ListOfDisclosureTypeDisclosures[] DisclosureList {
            get {
                return this.disclosureListField;
            }
            set {
                this.disclosureListField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("FareGroup", IsNullable=false)]
        public FareListFareGroup[] FareList {
            get {
                return this.fareListField;
            }
            set {
                this.fareListField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("FlightSegment", IsNullable=false)]
        public ListOfFlightSegmentType[] FlightSegmentList {
            get {
                return this.flightSegmentListField;
            }
            set {
                this.flightSegmentListField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Flight", IsNullable=false)]
        public OrderReshopRSResponseDataListsFlight[] FlightList {
            get {
                return this.flightListField;
            }
            set {
                this.flightListField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("OriginDestination", IsNullable=false)]
        public OriginDestination[] OriginDestinationList {
            get {
                return this.originDestinationListField;
            }
            set {
                this.originDestinationListField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Instruction", IsNullable=false)]
        public ListOfOfferInstructionsTypeInstruction[] InstructionsList {
            get {
                return this.instructionsListField;
            }
            set {
                this.instructionsListField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Media", IsNullable=false)]
        public ListOfMediaTypeMedia[] MediaList {
            get {
                return this.mediaListField;
            }
            set {
                this.mediaListField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Penalty", IsNullable=false)]
        public ListOfOfferPenaltyTypePenalty[] PenaltyList {
            get {
                return this.penaltyListField;
            }
            set {
                this.penaltyListField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("PriceClass", IsNullable=false)]
        public PriceClassType[] PriceClassList {
            get {
                return this.priceClassListField;
            }
            set {
                this.priceClassListField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("ServiceDefinition", IsNullable=false)]
        public ServiceDefinitionType[] ServiceDefinitionList {
            get {
                return this.serviceDefinitionListField;
            }
            set {
                this.serviceDefinitionListField = value;
            }
        }
        
        /// <remarks/>
        public ListOfOfferTermsType TermsList {
            get {
                return this.termsListField;
            }
            set {
                this.termsListField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("SeatDefinition", IsNullable=false)]
        public OrderReshopRSResponseDataListsSeatDefinition[] SeatDefinitionList {
            get {
                return this.seatDefinitionListField;
            }
            set {
                this.seatDefinitionListField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderReshopRSResponseDataListsBaggageAllowance {
        
        private BaggageCategoryListType baggageCategoryField;
        
        private BagAllowanceDescType allowanceDescriptionField;
        
        private BagAllowanceDimensionType dimensionAllowanceField;
        
        private PieceAllowance[] pieceAllowanceField;
        
        private WeightAllowance weightAllowanceField;
        
        private BaggageDeterminingCarrierType baggageDeterminingCarrierField;
        
        private string baggageAllowanceIDField;
        
        /// <remarks/>
        public BaggageCategoryListType BaggageCategory {
            get {
                return this.baggageCategoryField;
            }
            set {
                this.baggageCategoryField = value;
            }
        }
        
        /// <remarks/>
        public BagAllowanceDescType AllowanceDescription {
            get {
                return this.allowanceDescriptionField;
            }
            set {
                this.allowanceDescriptionField = value;
            }
        }
        
        /// <remarks/>
        public BagAllowanceDimensionType DimensionAllowance {
            get {
                return this.dimensionAllowanceField;
            }
            set {
                this.dimensionAllowanceField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("PieceAllowance")]
        public PieceAllowance[] PieceAllowance {
            get {
                return this.pieceAllowanceField;
            }
            set {
                this.pieceAllowanceField = value;
            }
        }
        
        /// <remarks/>
        public WeightAllowance WeightAllowance {
            get {
                return this.weightAllowanceField;
            }
            set {
                this.weightAllowanceField = value;
            }
        }
        
        /// <remarks/>
        public BaggageDeterminingCarrierType BaggageDeterminingCarrier {
            get {
                return this.baggageDeterminingCarrierField;
            }
            set {
                this.baggageDeterminingCarrierField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="ID")]
        public string BaggageAllowanceID {
            get {
                return this.baggageAllowanceIDField;
            }
            set {
                this.baggageAllowanceIDField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderReshopRSResponseDataListsFlight {
        
        private TotalJourneyType journeyField;
        
        private SegmentReferences segmentReferencesField;
        
        private OrderReshopRSResponseDataListsFlightSettlement settlementField;
        
        private string refsField;
        
        private string flightKeyField;
        
        /// <remarks/>
        public TotalJourneyType Journey {
            get {
                return this.journeyField;
            }
            set {
                this.journeyField = value;
            }
        }
        
        /// <remarks/>
        public SegmentReferences SegmentReferences {
            get {
                return this.segmentReferencesField;
            }
            set {
                this.segmentReferencesField = value;
            }
        }
        
        /// <remarks/>
        public OrderReshopRSResponseDataListsFlightSettlement Settlement {
            get {
                return this.settlementField;
            }
            set {
                this.settlementField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="IDREFS")]
        public string refs {
            get {
                return this.refsField;
            }
            set {
                this.refsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="ID")]
        public string FlightKey {
            get {
                return this.flightKeyField;
            }
            set {
                this.flightKeyField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderReshopRSResponseDataListsFlightSettlement {
        
        private string methodField;
        
        private CurrencyAmountOptType interlineSettlementValueField;
        
        /// <remarks/>
        public string Method {
            get {
                return this.methodField;
            }
            set {
                this.methodField = value;
            }
        }
        
        /// <remarks/>
        public CurrencyAmountOptType InterlineSettlementValue {
            get {
                return this.interlineSettlementValueField;
            }
            set {
                this.interlineSettlementValueField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderReshopRSResponseDataListsSeatDefinition {
        
        private OrderReshopRSResponseDataListsSeatDefinitionDescription[] descriptionField;
        
        private OrderReshopRSResponseDataListsSeatDefinitionMarketing marketingField;
        
        private string[] seatCharacteristicCodeField;
        
        private SizeUnitSimpleType uOMField;
        
        private bool uOMFieldSpecified;
        
        private decimal seatWidthLowField;
        
        private bool seatWidthLowFieldSpecified;
        
        private decimal seatPitchLowField;
        
        private bool seatPitchLowFieldSpecified;
        
        private KeyWordType[] keywordsField;
        
        private string seatDefinitionIDField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("Description")]
        public OrderReshopRSResponseDataListsSeatDefinitionDescription[] Description {
            get {
                return this.descriptionField;
            }
            set {
                this.descriptionField = value;
            }
        }
        
        /// <remarks/>
        public OrderReshopRSResponseDataListsSeatDefinitionMarketing Marketing {
            get {
                return this.marketingField;
            }
            set {
                this.marketingField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("SeatCharacteristicCode")]
        public string[] SeatCharacteristicCode {
            get {
                return this.seatCharacteristicCodeField;
            }
            set {
                this.seatCharacteristicCodeField = value;
            }
        }
        
        /// <remarks/>
        public SizeUnitSimpleType UOM {
            get {
                return this.uOMField;
            }
            set {
                this.uOMField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool UOMSpecified {
            get {
                return this.uOMFieldSpecified;
            }
            set {
                this.uOMFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        public decimal SeatWidthLow {
            get {
                return this.seatWidthLowField;
            }
            set {
                this.seatWidthLowField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool SeatWidthLowSpecified {
            get {
                return this.seatWidthLowFieldSpecified;
            }
            set {
                this.seatWidthLowFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        public decimal SeatPitchLow {
            get {
                return this.seatPitchLowField;
            }
            set {
                this.seatPitchLowField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool SeatPitchLowSpecified {
            get {
                return this.seatPitchLowFieldSpecified;
            }
            set {
                this.seatPitchLowFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("KeyWord", IsNullable=false)]
        public KeyWordType[] Keywords {
            get {
                return this.keywordsField;
            }
            set {
                this.keywordsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="ID")]
        public string SeatDefinitionID {
            get {
                return this.seatDefinitionIDField;
            }
            set {
                this.seatDefinitionIDField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderReshopRSResponseDataListsSeatDefinitionDescription {
        
        private OrderReshopRSResponseDataListsSeatDefinitionDescriptionText textField;
        
        private string markupStyleField;
        
        private string linkField;
        
        private OrderReshopRSResponseDataListsSeatDefinitionDescriptionMedia[] mediaField;
        
        /// <remarks/>
        public OrderReshopRSResponseDataListsSeatDefinitionDescriptionText Text {
            get {
                return this.textField;
            }
            set {
                this.textField = value;
            }
        }
        
        /// <remarks/>
        public string MarkupStyle {
            get {
                return this.markupStyleField;
            }
            set {
                this.markupStyleField = value;
            }
        }
        
        /// <remarks/>
        public string Link {
            get {
                return this.linkField;
            }
            set {
                this.linkField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("Media")]
        public OrderReshopRSResponseDataListsSeatDefinitionDescriptionMedia[] Media {
            get {
                return this.mediaField;
            }
            set {
                this.mediaField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderReshopRSResponseDataListsSeatDefinitionDescriptionText {
        
        private string refsField;
        
        private string valueField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="IDREFS")]
        public string refs {
            get {
                return this.refsField;
            }
            set {
                this.refsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        public string Value {
            get {
                return this.valueField;
            }
            set {
                this.valueField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderReshopRSResponseDataListsSeatDefinitionDescriptionMedia {
        
        private object itemField;
        
        private ItemChoiceType9 itemElementNameField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("AttachmentID", typeof(MediaID_Type))]
        [System.Xml.Serialization.XmlElementAttribute("MediaLink", typeof(MediaLink))]
        [System.Xml.Serialization.XmlElementAttribute("ObjectID", typeof(MediaID_Type))]
        [System.Xml.Serialization.XmlChoiceIdentifierAttribute("ItemElementName")]
        public object Item {
            get {
                return this.itemField;
            }
            set {
                this.itemField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public ItemChoiceType9 ItemElementName {
            get {
                return this.itemElementNameField;
            }
            set {
                this.itemElementNameField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderReshopRSResponseDataListsSeatDefinitionMarketing : SeatMapMessageType {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public enum OrderReshopRSTarget {
        
        /// <remarks/>
        Test,
        
        /// <remarks/>
        Production,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.0.30319.33440")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public enum OrderReshopRSTransactionStatusCode {
        
        /// <remarks/>
        Start,
        
        /// <remarks/>
        End,
        
        /// <remarks/>
        Rollback,
        
        /// <remarks/>
        InSeries,
        
        /// <remarks/>
        Continuation,
        
        /// <remarks/>
        Subsequent,
    }
}
