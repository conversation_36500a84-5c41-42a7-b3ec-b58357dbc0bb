//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// 
// This source code was auto-generated by xsd, Version=4.6.1055.0.
// 
namespace Ubimecs.IBS.Models {
    using System.Xml.Serialization;
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace="http://www.iata.org/IATA/EDIST/2017.2", IsNullable=false)]
    public partial class OrderRetrieveRQ {
        
        private PointOfSaleType pointOfSaleField;
        
        private MsgDocumentType documentField;
        
        private MsgPartiesType partyField;
        
        private OrdRetrieveParamsType orderRetrieveParametersField;
        
        private OrderRetrieveRQQuery queryField;
        
        private string echoTokenField;
        
        private System.DateTime timeStampField;
        
        private bool timeStampFieldSpecified;
        
        private OrderRetrieveRQTarget targetField;
        
        private string versionField;
        
        private string transactionIdentifierField;
        
        private string sequenceNmbrField;
        
        private OrderRetrieveRQTransactionStatusCode transactionStatusCodeField;
        
        private bool transactionStatusCodeFieldSpecified;
        
        private bool retransmissionIndicatorField;
        
        private bool retransmissionIndicatorFieldSpecified;
        
        private string correlationIDField;
        
        private bool asynchronousAllowedIndField;
        
        private bool asynchronousAllowedIndFieldSpecified;
        
        public OrderRetrieveRQ() {
            this.targetField = OrderRetrieveRQTarget.Production;
        }
        
        /// <remarks/>
        public PointOfSaleType PointOfSale {
            get {
                return this.pointOfSaleField;
            }
            set {
                this.pointOfSaleField = value;
            }
        }
        
        /// <remarks/>
        public MsgDocumentType Document {
            get {
                return this.documentField;
            }
            set {
                this.documentField = value;
            }
        }
        
        /// <remarks/>
        public MsgPartiesType Party {
            get {
                return this.partyField;
            }
            set {
                this.partyField = value;
            }
        }
        
        /// <remarks/>
        public OrdRetrieveParamsType OrderRetrieveParameters {
            get {
                return this.orderRetrieveParametersField;
            }
            set {
                this.orderRetrieveParametersField = value;
            }
        }
        
        /// <remarks/>
        public OrderRetrieveRQQuery Query {
            get {
                return this.queryField;
            }
            set {
                this.queryField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string EchoToken {
            get {
                return this.echoTokenField;
            }
            set {
                this.echoTokenField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public System.DateTime TimeStamp {
            get {
                return this.timeStampField;
            }
            set {
                this.timeStampField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool TimeStampSpecified {
            get {
                return this.timeStampFieldSpecified;
            }
            set {
                this.timeStampFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        [System.ComponentModel.DefaultValueAttribute(OrderRetrieveRQTarget.Production)]
        public OrderRetrieveRQTarget Target {
            get {
                return this.targetField;
            }
            set {
                this.targetField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Version {
            get {
                return this.versionField;
            }
            set {
                this.versionField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string TransactionIdentifier {
            get {
                return this.transactionIdentifierField;
            }
            set {
                this.transactionIdentifierField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="nonNegativeInteger")]
        public string SequenceNmbr {
            get {
                return this.sequenceNmbrField;
            }
            set {
                this.sequenceNmbrField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public OrderRetrieveRQTransactionStatusCode TransactionStatusCode {
            get {
                return this.transactionStatusCodeField;
            }
            set {
                this.transactionStatusCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool TransactionStatusCodeSpecified {
            get {
                return this.transactionStatusCodeFieldSpecified;
            }
            set {
                this.transactionStatusCodeFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public bool RetransmissionIndicator {
            get {
                return this.retransmissionIndicatorField;
            }
            set {
                this.retransmissionIndicatorField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool RetransmissionIndicatorSpecified {
            get {
                return this.retransmissionIndicatorFieldSpecified;
            }
            set {
                this.retransmissionIndicatorFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string CorrelationID {
            get {
                return this.correlationIDField;
            }
            set {
                this.correlationIDField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public bool AsynchronousAllowedInd {
            get {
                return this.asynchronousAllowedIndField;
            }
            set {
                this.asynchronousAllowedIndField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool AsynchronousAllowedIndSpecified {
            get {
                return this.asynchronousAllowedIndFieldSpecified;
            }
            set {
                this.asynchronousAllowedIndFieldSpecified = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    [System.Xml.Serialization.XmlRootAttribute("OrderRetrieveParameters", Namespace="http://www.iata.org/IATA/EDIST/2017.2", IsNullable=false)]
    public partial class OrdRetrieveParamsType {
        
        private AlertsTypeAlert[] alertsField;
        
        private NoticesNotice[] noticesField;
        
        private string referencesField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Alert", IsNullable=false)]
        public AlertsTypeAlert[] Alerts {
            get {
                return this.alertsField;
            }
            set {
                this.alertsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Notice", IsNullable=false)]
        public NoticesNotice[] Notices {
            get {
                return this.noticesField;
            }
            set {
                this.noticesField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="IDREFS")]
        public string references {
            get {
                return this.referencesField;
            }
            set {
                this.referencesField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderRetrieveRQQuery {
        
        private OrderRetrieveRQQueryFilters filtersField;
        
        /// <remarks/>
        public OrderRetrieveRQQueryFilters Filters {
            get {
                return this.filtersField;
            }
            set {
                this.filtersField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderRetrieveRQQueryFilters {
        
        private OrderID_Type orderIDField;
        
        private OrderRetrieveRQQueryFiltersFlight flightField;
        
        private OrderRetrieveRQQueryFiltersPassengers passengersField;
        
        private OrderRetrieveRQQueryFiltersTicketDocument ticketDocumentField;
        
        private OrderRetrieveRQQueryFiltersGroup groupField;
        
        private OrderRetrieveRQQueryFiltersPayments paymentsField;
        
        private BookingReferenceType[] bookingReferencesField;
        
        /// <remarks/>
        public OrderID_Type OrderID {
            get {
                return this.orderIDField;
            }
            set {
                this.orderIDField = value;
            }
        }
        
        /// <remarks/>
        public OrderRetrieveRQQueryFiltersFlight Flight {
            get {
                return this.flightField;
            }
            set {
                this.flightField = value;
            }
        }
        
        /// <remarks/>
        public OrderRetrieveRQQueryFiltersPassengers Passengers {
            get {
                return this.passengersField;
            }
            set {
                this.passengersField = value;
            }
        }
        
        /// <remarks/>
        public OrderRetrieveRQQueryFiltersTicketDocument TicketDocument {
            get {
                return this.ticketDocumentField;
            }
            set {
                this.ticketDocumentField = value;
            }
        }
        
        /// <remarks/>
        public OrderRetrieveRQQueryFiltersGroup Group {
            get {
                return this.groupField;
            }
            set {
                this.groupField = value;
            }
        }
        
        /// <remarks/>
        public OrderRetrieveRQQueryFiltersPayments Payments {
            get {
                return this.paymentsField;
            }
            set {
                this.paymentsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("BookingReference", IsNullable=false)]
        public BookingReferenceType[] BookingReferences {
            get {
                return this.bookingReferencesField;
            }
            set {
                this.bookingReferencesField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderRetrieveRQQueryFiltersFlight {
        
        private FlightCOS_CoreType classOfServiceField;
        
        private FareBasisCodeType fareBasisCodeField;
        
        private OrderRetrieveRQQueryFiltersFlightOriginDestination originDestinationField;
        
        private OrderRetrieveRQQueryFiltersFlightSegment[] segmentField;
        
        /// <remarks/>
        public FlightCOS_CoreType ClassOfService {
            get {
                return this.classOfServiceField;
            }
            set {
                this.classOfServiceField = value;
            }
        }
        
        /// <remarks/>
        public FareBasisCodeType FareBasisCode {
            get {
                return this.fareBasisCodeField;
            }
            set {
                this.fareBasisCodeField = value;
            }
        }
        
        /// <remarks/>
        public OrderRetrieveRQQueryFiltersFlightOriginDestination OriginDestination {
            get {
                return this.originDestinationField;
            }
            set {
                this.originDestinationField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("Segment")]
        public OrderRetrieveRQQueryFiltersFlightSegment[] Segment {
            get {
                return this.segmentField;
            }
            set {
                this.segmentField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderRetrieveRQQueryFiltersFlightOriginDestination {
        
        private OrderRetrieveRQQueryFiltersFlightOriginDestinationDeparture departureField;
        
        private OrderRetrieveRQQueryFiltersFlightOriginDestinationArrival arrivalField;
        
        private string refsField;
        
        /// <remarks/>
        public OrderRetrieveRQQueryFiltersFlightOriginDestinationDeparture Departure {
            get {
                return this.departureField;
            }
            set {
                this.departureField = value;
            }
        }
        
        /// <remarks/>
        public OrderRetrieveRQQueryFiltersFlightOriginDestinationArrival Arrival {
            get {
                return this.arrivalField;
            }
            set {
                this.arrivalField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="IDREFS")]
        public string refs {
            get {
                return this.refsField;
            }
            set {
                this.refsField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderRetrieveRQQueryFiltersFlightOriginDestinationDeparture {
        
        private OrderRetrieveRQQueryFiltersFlightOriginDestinationDepartureAirportCode airportCodeField;
        
        private System.DateTime dateField;
        
        /// <remarks/>
        public OrderRetrieveRQQueryFiltersFlightOriginDestinationDepartureAirportCode AirportCode {
            get {
                return this.airportCodeField;
            }
            set {
                this.airportCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="date")]
        public System.DateTime Date {
            get {
                return this.dateField;
            }
            set {
                this.dateField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderRetrieveRQQueryFiltersFlightOriginDestinationDepartureAirportCode {
        
        private string applicationField;
        
        private string areaField;
        
        private DistanceUnitListType uOMField;
        
        private bool uOMFieldSpecified;
        
        private string valueField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Application {
            get {
                return this.applicationField;
            }
            set {
                this.applicationField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="positiveInteger")]
        public string Area {
            get {
                return this.areaField;
            }
            set {
                this.areaField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public DistanceUnitListType UOM {
            get {
                return this.uOMField;
            }
            set {
                this.uOMField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool UOMSpecified {
            get {
                return this.uOMFieldSpecified;
            }
            set {
                this.uOMFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        public string Value {
            get {
                return this.valueField;
            }
            set {
                this.valueField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderRetrieveRQQueryFiltersFlightOriginDestinationArrival {
        
        private OrderRetrieveRQQueryFiltersFlightOriginDestinationArrivalAirportCode airportCodeField;
        
        private System.DateTime dateField;
        
        /// <remarks/>
        public OrderRetrieveRQQueryFiltersFlightOriginDestinationArrivalAirportCode AirportCode {
            get {
                return this.airportCodeField;
            }
            set {
                this.airportCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="date")]
        public System.DateTime Date {
            get {
                return this.dateField;
            }
            set {
                this.dateField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderRetrieveRQQueryFiltersFlightOriginDestinationArrivalAirportCode {
        
        private string applicationField;
        
        private string areaField;
        
        private DistanceUnitListType uOMField;
        
        private bool uOMFieldSpecified;
        
        private string valueField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Application {
            get {
                return this.applicationField;
            }
            set {
                this.applicationField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="positiveInteger")]
        public string Area {
            get {
                return this.areaField;
            }
            set {
                this.areaField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public DistanceUnitListType UOM {
            get {
                return this.uOMField;
            }
            set {
                this.uOMField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool UOMSpecified {
            get {
                return this.uOMFieldSpecified;
            }
            set {
                this.uOMFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        public string Value {
            get {
                return this.valueField;
            }
            set {
                this.valueField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderRetrieveRQQueryFiltersFlightSegment {
        
        private OrderRetrieveRQQueryFiltersFlightSegmentDeparture departureField;
        
        private OrderRetrieveRQQueryFiltersFlightSegmentArrival arrivalField;
        
        private string refsField;
        
        /// <remarks/>
        public OrderRetrieveRQQueryFiltersFlightSegmentDeparture Departure {
            get {
                return this.departureField;
            }
            set {
                this.departureField = value;
            }
        }
        
        /// <remarks/>
        public OrderRetrieveRQQueryFiltersFlightSegmentArrival Arrival {
            get {
                return this.arrivalField;
            }
            set {
                this.arrivalField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="IDREFS")]
        public string refs {
            get {
                return this.refsField;
            }
            set {
                this.refsField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderRetrieveRQQueryFiltersFlightSegmentDeparture {
        
        private OrderRetrieveRQQueryFiltersFlightSegmentDepartureAirportCode airportCodeField;
        
        private System.DateTime dateField;
        
        /// <remarks/>
        public OrderRetrieveRQQueryFiltersFlightSegmentDepartureAirportCode AirportCode {
            get {
                return this.airportCodeField;
            }
            set {
                this.airportCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="date")]
        public System.DateTime Date {
            get {
                return this.dateField;
            }
            set {
                this.dateField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderRetrieveRQQueryFiltersFlightSegmentDepartureAirportCode {
        
        private string applicationField;
        
        private string areaField;
        
        private DistanceUnitListType uOMField;
        
        private bool uOMFieldSpecified;
        
        private string valueField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Application {
            get {
                return this.applicationField;
            }
            set {
                this.applicationField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="positiveInteger")]
        public string Area {
            get {
                return this.areaField;
            }
            set {
                this.areaField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public DistanceUnitListType UOM {
            get {
                return this.uOMField;
            }
            set {
                this.uOMField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool UOMSpecified {
            get {
                return this.uOMFieldSpecified;
            }
            set {
                this.uOMFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        public string Value {
            get {
                return this.valueField;
            }
            set {
                this.valueField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderRetrieveRQQueryFiltersFlightSegmentArrival {
        
        private OrderRetrieveRQQueryFiltersFlightSegmentArrivalAirportCode airportCodeField;
        
        private System.DateTime dateField;
        
        /// <remarks/>
        public OrderRetrieveRQQueryFiltersFlightSegmentArrivalAirportCode AirportCode {
            get {
                return this.airportCodeField;
            }
            set {
                this.airportCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="date")]
        public System.DateTime Date {
            get {
                return this.dateField;
            }
            set {
                this.dateField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderRetrieveRQQueryFiltersFlightSegmentArrivalAirportCode {
        
        private string applicationField;
        
        private string areaField;
        
        private DistanceUnitListType uOMField;
        
        private bool uOMFieldSpecified;
        
        private string valueField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Application {
            get {
                return this.applicationField;
            }
            set {
                this.applicationField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="positiveInteger")]
        public string Area {
            get {
                return this.areaField;
            }
            set {
                this.areaField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public DistanceUnitListType UOM {
            get {
                return this.uOMField;
            }
            set {
                this.uOMField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool UOMSpecified {
            get {
                return this.uOMFieldSpecified;
            }
            set {
                this.uOMFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        public string Value {
            get {
                return this.valueField;
            }
            set {
                this.valueField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderRetrieveRQQueryFiltersPassengers {
        
        private OrderRetrieveRQQueryFiltersPassengersIndividual individualField;
        
        private string profileIDField;
        
        private OrderRetrieveRQQueryFiltersPassengersLoyaltyProgramAccount[] loyaltyProgramAccountField;
        
        private FOID fOIDField;
        
        private IdentityDocumentType[] identityDocumentField;
        
        private OrderRetrieveRQQueryFiltersPassengersContactInformation[] contactInformationField;
        
        /// <remarks/>
        public OrderRetrieveRQQueryFiltersPassengersIndividual Individual {
            get {
                return this.individualField;
            }
            set {
                this.individualField = value;
            }
        }
        
        /// <remarks/>
        public string ProfileID {
            get {
                return this.profileIDField;
            }
            set {
                this.profileIDField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("LoyaltyProgramAccount")]
        public OrderRetrieveRQQueryFiltersPassengersLoyaltyProgramAccount[] LoyaltyProgramAccount {
            get {
                return this.loyaltyProgramAccountField;
            }
            set {
                this.loyaltyProgramAccountField = value;
            }
        }
        
        /// <remarks/>
        public FOID FOID {
            get {
                return this.fOIDField;
            }
            set {
                this.fOIDField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("IdentityDocument")]
        public IdentityDocumentType[] IdentityDocument {
            get {
                return this.identityDocumentField;
            }
            set {
                this.identityDocumentField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("ContactInformation")]
        public OrderRetrieveRQQueryFiltersPassengersContactInformation[] ContactInformation {
            get {
                return this.contactInformationField;
            }
            set {
                this.contactInformationField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderRetrieveRQQueryFiltersPassengersIndividual {
        
        private string[] givenNameField;
        
        private string[] middleNameField;
        
        private string surnameField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("GivenName")]
        public string[] GivenName {
            get {
                return this.givenNameField;
            }
            set {
                this.givenNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("MiddleName")]
        public string[] MiddleName {
            get {
                return this.middleNameField;
            }
            set {
                this.middleNameField = value;
            }
        }
        
        /// <remarks/>
        public string Surname {
            get {
                return this.surnameField;
            }
            set {
                this.surnameField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderRetrieveRQQueryFiltersPassengersLoyaltyProgramAccount {
        
        private AirlineTypeAIDM airlineField;
        
        private string programNameField;
        
        private string programCodeField;
        
        private string accountNumberField;
        
        private string signInIDField;
        
        /// <remarks/>
        public AirlineTypeAIDM Airline {
            get {
                return this.airlineField;
            }
            set {
                this.airlineField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="token")]
        public string ProgramName {
            get {
                return this.programNameField;
            }
            set {
                this.programNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="token")]
        public string ProgramCode {
            get {
                return this.programCodeField;
            }
            set {
                this.programCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="token")]
        public string AccountNumber {
            get {
                return this.accountNumberField;
            }
            set {
                this.accountNumberField = value;
            }
        }
        
        /// <remarks/>
        public string SignInID {
            get {
                return this.signInIDField;
            }
            set {
                this.signInIDField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderRetrieveRQQueryFiltersPassengersContactInformation {
        
        private string contactTypeField;
        
        private PostalAddressType[] postalAddressField;
        
        private EmailAddressType[] emailAddressField;
        
        private PhoneTypeAIDM[] phoneField;
        
        private OtherAddressType[] otherAddressField;
        
        /// <remarks/>
        public string ContactType {
            get {
                return this.contactTypeField;
            }
            set {
                this.contactTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("PostalAddress")]
        public PostalAddressType[] PostalAddress {
            get {
                return this.postalAddressField;
            }
            set {
                this.postalAddressField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("EmailAddress")]
        public EmailAddressType[] EmailAddress {
            get {
                return this.emailAddressField;
            }
            set {
                this.emailAddressField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("Phone")]
        public PhoneTypeAIDM[] Phone {
            get {
                return this.phoneField;
            }
            set {
                this.phoneField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("OtherAddress")]
        public OtherAddressType[] OtherAddress {
            get {
                return this.otherAddressField;
            }
            set {
                this.otherAddressField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderRetrieveRQQueryFiltersTicketDocument {
        
        private string ticketDocNbrField;
        
        private System.DateTime dateOfIssueField;
        
        private bool dateOfIssueFieldSpecified;
        
        private string ticketingLocationField;
        
        private string couponNumberField;
        
        /// <remarks/>
        public string TicketDocNbr {
            get {
                return this.ticketDocNbrField;
            }
            set {
                this.ticketDocNbrField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="date")]
        public System.DateTime DateOfIssue {
            get {
                return this.dateOfIssueField;
            }
            set {
                this.dateOfIssueField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool DateOfIssueSpecified {
            get {
                return this.dateOfIssueFieldSpecified;
            }
            set {
                this.dateOfIssueFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        public string TicketingLocation {
            get {
                return this.ticketingLocationField;
            }
            set {
                this.ticketingLocationField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="integer")]
        public string CouponNumber {
            get {
                return this.couponNumberField;
            }
            set {
                this.couponNumberField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderRetrieveRQQueryFiltersGroup {
        
        private string nameField;
        
        private string totalPartySizeField;
        
        private ContactsContact[] contactsField;
        
        /// <remarks/>
        public string Name {
            get {
                return this.nameField;
            }
            set {
                this.nameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="positiveInteger")]
        public string TotalPartySize {
            get {
                return this.totalPartySizeField;
            }
            set {
                this.totalPartySizeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Contact", IsNullable=false)]
        public ContactsContact[] Contacts {
            get {
                return this.contactsField;
            }
            set {
                this.contactsField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderRetrieveRQQueryFiltersPayments {
        
        private OrderRetrieveRQQueryFiltersPaymentsMethod methodField;
        
        /// <remarks/>
        public OrderRetrieveRQQueryFiltersPaymentsMethod Method {
            get {
                return this.methodField;
            }
            set {
                this.methodField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderRetrieveRQQueryFiltersPaymentsMethod {
        
        private string cardCodeField;
        
        private OrderRetrieveRQQueryFiltersPaymentsMethodMaskedCardNumber maskedCardNumberField;
        
        private string tokenizedCardNumberField;
        
        private OrderRetrieveRQQueryFiltersPaymentsMethodEffectiveExpireDate effectiveExpireDateField;
        
        private OrderRetrieveRQQueryFiltersPaymentsMethodCardHolderName cardHolderNameField;
        
        private string refsField;
        
        /// <remarks/>
        public string CardCode {
            get {
                return this.cardCodeField;
            }
            set {
                this.cardCodeField = value;
            }
        }
        
        /// <remarks/>
        public OrderRetrieveRQQueryFiltersPaymentsMethodMaskedCardNumber MaskedCardNumber {
            get {
                return this.maskedCardNumberField;
            }
            set {
                this.maskedCardNumberField = value;
            }
        }
        
        /// <remarks/>
        public string TokenizedCardNumber {
            get {
                return this.tokenizedCardNumberField;
            }
            set {
                this.tokenizedCardNumberField = value;
            }
        }
        
        /// <remarks/>
        public OrderRetrieveRQQueryFiltersPaymentsMethodEffectiveExpireDate EffectiveExpireDate {
            get {
                return this.effectiveExpireDateField;
            }
            set {
                this.effectiveExpireDateField = value;
            }
        }
        
        /// <remarks/>
        public OrderRetrieveRQQueryFiltersPaymentsMethodCardHolderName CardHolderName {
            get {
                return this.cardHolderNameField;
            }
            set {
                this.cardHolderNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="IDREFS")]
        public string refs {
            get {
                return this.refsField;
            }
            set {
                this.refsField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderRetrieveRQQueryFiltersPaymentsMethodMaskedCardNumber {
        
        private string refsField;
        
        private string valueField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="IDREFS")]
        public string refs {
            get {
                return this.refsField;
            }
            set {
                this.refsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        public string Value {
            get {
                return this.valueField;
            }
            set {
                this.valueField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderRetrieveRQQueryFiltersPaymentsMethodEffectiveExpireDate {
        
        private string effectiveField;
        
        private string expirationField;
        
        private string refsField;
        
        /// <remarks/>
        public string Effective {
            get {
                return this.effectiveField;
            }
            set {
                this.effectiveField = value;
            }
        }
        
        /// <remarks/>
        public string Expiration {
            get {
                return this.expirationField;
            }
            set {
                this.expirationField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="IDREFS")]
        public string refs {
            get {
                return this.refsField;
            }
            set {
                this.refsField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public partial class OrderRetrieveRQQueryFiltersPaymentsMethodCardHolderName {
        
        private string refsField;
        
        private string valueField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="IDREFS")]
        public string refs {
            get {
                return this.refsField;
            }
            set {
                this.refsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        public string Value {
            get {
                return this.valueField;
            }
            set {
                this.valueField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public enum OrderRetrieveRQTarget {
        
        /// <remarks/>
        Test,
        
        /// <remarks/>
        Production,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "4.6.1055.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.iata.org/IATA/EDIST/2017.2")]
    public enum OrderRetrieveRQTransactionStatusCode {
        
        /// <remarks/>
        Start,
        
        /// <remarks/>
        End,
        
        /// <remarks/>
        Rollback,
        
        /// <remarks/>
        InSeries,
        
        /// <remarks/>
        Continuation,
        
        /// <remarks/>
        Subsequent,
    }
}
