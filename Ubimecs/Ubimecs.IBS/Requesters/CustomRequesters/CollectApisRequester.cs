using System.ServiceModel;
using System.ServiceModel.Channels;
using Ubimecs.IBS.Models;
using Ubimecs.IBS.Services.CheckInService;
using Ubimecs.IBS.Utility;
using Ubimecs.Infrastructure.Caching;
using Ubimecs.Infrastructure.Logging;

namespace Ubimecs.IBS.Requesters.CustomRequesters
{
    public class CollectApisRequester : IbsRequester<CHK_ApisCollectionRQ, CHK_ApisCollectionRS>
    {
        public CollectApisRequester(SessionCache session, string serviceName) : base(session, serviceName, null, null, null)
        {
        }

        internal override void CheckForGeneralError(Envelope envelope)
        {
            base.CheckForGeneralError(envelope);
        }

        internal override CHK_ApisCollectionRS InternalExecute(CHK_ApisCollectionRQ request)
        {
            CheckinPortClient client = new CheckinPortClient();
            using (new OperationContextScope(client.InnerChannel))
            {
                var requestMessage = new HttpRequestMessageProperty();

                requestMessage.Headers["UserName"] = GeneralConstants.CHECKIN_API_USERNAME;
                requestMessage.Headers["Password"] = GeneralConstants.CHECKIN_API_PASSWORD;
                OperationContext.Current.OutgoingMessageProperties[HttpRequestMessageProperty.Name] = requestMessage;

                Logger.Instance.XmlLog(Session.SessionId, ServiceName, request, typeof(CHK_ApisCollectionRQ).Name);

                CHK_ApisCollectionRS response = client.collectApis(request);

                Logger.Instance.XmlLog(Session.SessionId, ServiceName, response, typeof(CHK_ApisCollectionRS).Name);

                CheckForErrors(response);

                return response;
            }
        }

        private void CheckForErrors(CHK_ApisCollectionRS response)
        {
            if(response.ErrorType != null)
            {
                throw new IbsException(response.ErrorType.errorCode, response.ErrorType.errorValue, Session.Language);

            }
        }
    }
}
