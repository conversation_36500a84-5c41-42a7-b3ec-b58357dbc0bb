using System.ServiceModel;
using System.ServiceModel.Channels;
using Ubimecs.IBS.Services.CheckInService;
using Ubimecs.IBS.Utility;
using Ubimecs.Infrastructure.Caching;
using Ubimecs.Infrastructure.Logging;
using Ubimecs.Infrastructure.Models.Common;

namespace Ubimecs.IBS.Requesters.CustomRequesters
{
    public class SearchGuestRequester : IbsRequester<CHK_SearchGuestRQ, CHK_SearchGuestRS>
    {
        public SearchGuestRequester(SessionCache session, string serviceName, bool isWithCaptcha) : base(session, serviceName, null, null, null)
        {
            IsWithCaptcha = isWithCaptcha;
        }

        public bool IsWithCaptcha { get; }

        internal override CHK_SearchGuestRS InternalExecute(CHK_SearchGuestRQ request)
        {
            CheckinPortClient client = new CheckinPortClient();
            using (new OperationContextScope(client.InnerChannel))
            {
                var requestMessage = new HttpRequestMessageProperty();

                requestMessage.Headers["UserName"] = GeneralConstants.CHECKIN_API_USERNAME;
                requestMessage.Headers["Password"] = GeneralConstants.CHECKIN_API_PASSWORD;
                OperationContext.Current.OutgoingMessageProperties[HttpRequestMessageProperty.Name] = requestMessage;

                Logger.Instance.XmlLog(Session.SessionId, ServiceName, request, typeof(CHK_SearchGuestRQ).Name);

                CHK_SearchGuestRS response = client.searchGuest(request);

                Logger.Instance.XmlLog(Session.SessionId, ServiceName, response, typeof(CHK_SearchGuestRS).Name);

                CheckForErrors(response, request, IsWithCaptcha);

                return response;
            }
        }

        private void CheckForErrors(CHK_SearchGuestRS response, CHK_SearchGuestRQ request, bool isWithCaptcha)
        {
            if (response.ErrorType != null)
            {
                if (response.ErrorType.errorCode == "DCS_039")
                {
                    throw new TmobException(CachedData.GetCMS(Session.Language, "passenger_name_mismatch"));
                }
                throw new IbsException(response.ErrorType.errorCode, response.ErrorType.errorValue, Session.Language);
            }

            if (isWithCaptcha)
            {
                if (request.Name.LastName != null && request.Name.FirstName != null)
                {
                    var isTrueChekinInfo = response.GuestList.Select(s => s.Name).Any(x => 
                    IbsUtility.ArrangeTurkishCharacter(x.FirstName).Equals(IbsUtility.ArrangeTurkishCharacter(request.Name.FirstName)) && 
                    IbsUtility.ArrangeTurkishCharacter(x.LastName).Equals(IbsUtility.ArrangeTurkishCharacter(request.Name.LastName)));

                    if (!isTrueChekinInfo)
                    {
                        throw new TmobException(CachedData.GetCMS(Session.Language, "passenger_name_mismatch"));
                    }
                }
            }
        }
    }
}
