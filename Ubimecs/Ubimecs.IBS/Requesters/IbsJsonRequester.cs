using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System.Net;
using Ubimecs.IBS.JsonModels;
using Ubimecs.Infrastructure.Caching;
using Ubimecs.Infrastructure.Contracts.Http;
using Ubimecs.Infrastructure.Logging;
using Ubimecs.Infrastructure.Models.Request;
using Ubimecs.Infrastructure.Models.Response;

namespace Ubimecs.IBS.Requesters
{
    public class IbsJsonRequester
    {
        public SessionCache Session { get; }

        private readonly IHttpClientService _httpClientService;

        public IbsJsonRequester(SessionCache session,IHttpClientService httpClientService)
        {
            Session = session;
            _httpClientService = httpClientService;
        }

        //private string ExecuteRequest(RestClient client, RestRequest request, string operationName, string requestBody = null)
        //{
        //    var response = client.Execute(request);

        //    Logger.Instance.JsonRequestLog(Session.SessionId, operationName, client.BaseUrl.AbsoluteUri, requestBody, response.Content);

        //    return response.Content;
        //}

        public Dictionary<string, AirportFlightPlan> GetAirportList()
        {

            //RestClient client = new RestClient(GeneralConstants.NEW_AIRPORT_SERVICE_URL);

            //client.Authenticator = new HttpBasicAuthenticator(GeneralConstants.NEW_AIRPORT_SERVICE_USERNAME, GeneralConstants.NEW_AIRPORT_SERVICE_PASSWORD);

            //RestRequest restRequest = new RestRequest(Method.GET);

            //var response = ExecuteRequest(client, restRequest, "AIRPORT_LIST");

            //return JsonConvert.DeserializeObject<Dictionary<string, AirportFlightPlan>>(response);

            var response = _httpClientService.GetAsync<Dictionary<string, AirportFlightPlan>>(GeneralConstants.NEW_AIRPORT_SERVICE_URL, GeneralConstants.NEW_AIRPORT_SERVICE_USERNAME, GeneralConstants.NEW_AIRPORT_SERVICE_PASSWORD).Result;

            string responseContent = JsonConvert.SerializeObject(response);

            Logger.Instance.JsonRequestLog(Session.SessionId, "AIRPORT_LIST", GeneralConstants.NEW_AIRPORT_SERVICE_URL, null, responseContent);

            return response;
        }

        public List<GetBestOffersResponse> GetBestOffers(GetFlightsRequest request)
        {
            var url = String.Format(GeneralConstants.SUNEXPRESS_BEST_OFFERS_API_URL, request.DepartureAirport, request.ArrivalAirport, request.EndDate, request.OrderBy, request.Output, request.ShowSoldOut, request.StartDate, request.Limit, request.DepatureCountryCode);

            var response = _httpClientService.GetAsync<List<GetBestOffersResponse>>(url, GeneralConstants.SUNEXPRESS_BEST_OFFERS_API_USERNAME, GeneralConstants.SUNEXPRESS_BEST_OFFERS_API_PASSWORD).Result;

            string responseContent = JsonConvert.SerializeObject(response);

            Logger.Instance.JsonRequestLog(Session.SessionId, "BEST_OFFERS", url, null, responseContent);

            return response;

            //RestClient client = new RestClient(url);

            //client.Authenticator = new HttpBasicAuthenticator(GeneralConstants.SUNEXPRESS_BEST_OFFERS_API_USERNAME, GeneralConstants.SUNEXPRESS_BEST_OFFERS_API_PASSWORD);

            //RestRequest restRequest = new RestRequest(Method.GET);

            //var response = ExecuteRequest(client, restRequest, "BEST_OFFERS");

            //return JsonConvert.DeserializeObject<List<GetBestOffersResponse>>(response);
        }

        public List<CalendarAvailabilityResponse> GetCalendarAvailability(GetFlightsRequest request)
        {
            var url = String.Format(GeneralConstants.SUNEXPRESS_BEST_OFFERS_API_URL, request.DepartureAirport, request.ArrivalAirport, request.EndDate, request.OrderBy, request.Output, request.ShowSoldOut, request.StartDate, request.Limit,"");
 
            var response = _httpClientService.GetAsync<List<CalendarAvailabilityResponse>>(url, GeneralConstants.SUNEXPRESS_BEST_OFFERS_API_USERNAME, GeneralConstants.SUNEXPRESS_BEST_OFFERS_API_PASSWORD).Result;

            string responseContent = JsonConvert.SerializeObject(response);

            Logger.Instance.JsonRequestLog(Session.SessionId, "CALENDAR_AVAILABILITY", url, null, responseContent);

            return response;

            //RestClient client = new RestClient(url);

            //client.Authenticator = new HttpBasicAuthenticator(GeneralConstants.SUNEXPRESS_BEST_OFFERS_API_USERNAME, GeneralConstants.SUNEXPRESS_BEST_OFFERS_API_PASSWORD);

            //RestRequest restRequest = new RestRequest(Method.GET);            
            //var response = ExecuteRequest(client, restRequest, "CALENDAR_AVAILABILITY");

            //return JsonConvert.DeserializeObject<List<CalendarAvailabilityResponse>>(response);
        }

        public List<GetBoomiFlightStatus> GetBoomiFlightStatus(GetBoomiFlightStatusRequest request)
        {
            //RestClient client = new RestClient(GeneralConstants.BOOMI_FLIGHT_STATUS_URL);

            //client.Authenticator = new HttpBasicAuthenticator(GeneralConstants.BOOMI_FLIGHT_STATUS_USERNAME, GeneralConstants.BOOMI_FLIGHT_STATUS_PASSWORD);
            //client.UseNewtonsoftJson();

            //RestRequest restRequest = new RestRequest(Method.POST);

            //restRequest.JsonSerializer = new JsonNetSerializer(new JsonSerializerSettings() { DateFormatString = "yyyy-MM-dd" });

            //restRequest.AddJsonBody(serviceRequest);

            //var response = ExecuteRequest(client, restRequest, "BOOMI_FLIGHT", JsonConvert.SerializeObject(serviceRequest));

            //List<GetBoomiFlightStatus> resultWithDeserialized = JsonConvert.DeserializeObject<List<GetBoomiFlightStatus>>(response);
            string flightNumber = "";
            if (request.FlightNumber != null)
            {
                flightNumber = request.FlightNumber?.ToUpper();
            }

            var serviceRequest = new BoomiFlightStatusServiceRequest
            {
                ArrivalAirport = request.ArrivalAirport,
                DepartureAirport = request.DepartureAirport,
                FlightDate = request.FlightDate,
                FlightNumber = flightNumber
            };

            var resultWithDeserialized = _httpClientService.PostAsync<BoomiFlightStatusServiceRequest, List<GetBoomiFlightStatus>>(GeneralConstants.BOOMI_FLIGHT_STATUS_URL, serviceRequest, GeneralConstants.BOOMI_FLIGHT_STATUS_USERNAME, GeneralConstants.BOOMI_FLIGHT_STATUS_PASSWORD, true).Result;


            return resultWithDeserialized;
        }

        public string GetLoyaltyServiceToken(string username, string password)
        {
            try
            {

                //RestRequest restRequest = new RestRequest(Method.POST);
                //restRequest.AddJsonBody(new
                //{
                //    user = username,
                //    password = password
                //});
                //ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;

                //RestClient client = new RestClient(GeneralConstants.LOYALTY_JWT_TOKEN_SERVICE_URL);
                //client.Authenticator = new HttpBasicAuthenticator(GeneralConstants.LOYALTY_JWT_TOKEN_SERVICE_AUTH_USERNAME, GeneralConstants.LOYALTY_JWT_TOKEN_SERVICE_AUTH_PASSWORD);

                //var response = ExecuteRequest(client, restRequest, "LOYALTY_TOKEN");

                //return JsonConvert.DeserializeObject<dynamic>(response)?.@object;
                
                var data = new { 
                    user = username, 
                    password = password 
                };

                var response = _httpClientService.PostAsync<object, dynamic>(GeneralConstants.LOYALTY_JWT_TOKEN_SERVICE_URL, data, GeneralConstants.LOYALTY_JWT_TOKEN_SERVICE_AUTH_USERNAME, GeneralConstants.LOYALTY_JWT_TOKEN_SERVICE_AUTH_PASSWORD).Result;

                Logger.Instance.JsonRequestLog(Session.SessionId, "LOYALTY_TOKEN", GeneralConstants.LOYALTY_JWT_TOKEN_SERVICE_URL, JsonConvert.SerializeObject(data), response);
                
                return JsonConvert.SerializeObject(response);
                
            }
            catch
            {
                return null;
            }
        }

        public AddFriendResponse AddFriend(AddFriendRequest request)
        {
            //RestRequest restRequest = new RestRequest(Method.POST);
            //restRequest.AddJsonBody(request);
            //ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;
            //restRequest.AddCookie("authentication_token", Session.UserInfo?.Token);
            //RestClient client = new RestClient(GeneralConstants.LOYALTY_ADD_FRIEND_SERVICE_URL);
            ////TO DO: Daha sonra bu logu kaldır.
            //Logger.Instance.FileLog(Session.SessionId, GeneralConstants.LOYALTY_ADD_FRIEND_SERVICE_URL);
            //client.Authenticator = new HttpBasicAuthenticator(GeneralConstants.LOYALTY_JWT_TOKEN_SERVICE_AUTH_USERNAME, GeneralConstants.LOYALTY_JWT_TOKEN_SERVICE_AUTH_PASSWORD);
            //CookieContainer cookieContainer = new CookieContainer();
            //Cookie jwtCookie = new Cookie();
            //jwtCookie.Name = "authentication_token";
            //jwtCookie.Value = Session.UserInfo?.Token;
            //jwtCookie.Path = "/";
            //jwtCookie.Domain = "aws-staging.sunexpress.com";
            //cookieContainer.Add(jwtCookie);
            //client.CookieContainer = cookieContainer;

            //var response = client.Execute(restRequest);
            //var result = JsonConvert.DeserializeObject<AddFriendResponse>(response.Content.ToString());
            //Logger.Instance.FileLog(Session.SessionId, result);
            //return result;
            
            Logger.Instance.FileLog(Session.SessionId, GeneralConstants.LOYALTY_ADD_FRIEND_SERVICE_URL);

            Cookie jwtCookie = new Cookie();
            jwtCookie.Name = "authentication_token";
            jwtCookie.Value = Session.UserInfo?.Token;
            jwtCookie.Path = "/";
            jwtCookie.Domain = "aws-staging.sunexpress.com";

            var response = _httpClientService.PostAsyncWithCookie<AddFriendRequest, AddFriendResponse>(GeneralConstants.LOYALTY_ADD_FRIEND_SERVICE_URL, request, GeneralConstants.LOYALTY_JWT_TOKEN_SERVICE_AUTH_USERNAME, GeneralConstants.LOYALTY_JWT_TOKEN_SERVICE_AUTH_PASSWORD, jwtCookie).Result;
            var result = JsonConvert.SerializeObject(response);
            
            Logger.Instance.FileLog(Session.SessionId, result);

            return response;
        }

        public CheckHesCodeResponse CheckHesCode(CheckHesCodeRequest request)
        {            
            //RestRequest restRequest = new RestRequest(Method.GET);
            //ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;
            ////{0} = HesCode - {1} = TCKN - {2} = PNR - {3} = FlightDate            
            //var url = String.Format(GeneralConstants.BOOMI_HES_CODE_STATUS_URL, request.HESCode, request.TCKN, request.PnrNumber, request.FlightDate);
            //Logger.Instance.FileLog(Session.SessionId,url);
            //RestClient client = new RestClient(url);
            //client.Authenticator = new HttpBasicAuthenticator(GeneralConstants.BOOMI_HES_CODE_STATUS_USERNAME, GeneralConstants.BOOMI_HES_CODE_STATUS_PASSWORD);
            //var responseService = client.Execute(restRequest);            
            //var responseData = JsonConvert.DeserializeObject<CheckHesCodeResponse>(responseService.Content);
            ////TODO: Bunların hepsine encryption gerekiyor.
            //Logger.Instance.FileLog(Session.SessionId, responseData);
            //if (responseData == null) return new CheckHesCodeResponse();
            //responseData.Status = EvaluateStatus(responseData.Status);
            //return responseData;

            var url = String.Format(GeneralConstants.BOOMI_HES_CODE_STATUS_URL, request.HESCode, request.TCKN, request.PnrNumber, request.FlightDate);
            
            Logger.Instance.FileLog(Session.SessionId, url);
            
            var response = _httpClientService.GetAsync<CheckHesCodeResponse>(url, GeneralConstants.BOOMI_HES_CODE_STATUS_USERNAME, GeneralConstants.BOOMI_HES_CODE_STATUS_PASSWORD).Result;
            
            if (response == null) return new CheckHesCodeResponse();
            
            string responseContent = JsonConvert.SerializeObject(response);

            Logger.Instance.FileLog(Session.SessionId, responseContent);
            
            response.Status = EvaluateStatus(response.Status);
           
            return response;

        }
        private static string EvaluateStatus(string errorCode)
        {
            if (string.IsNullOrEmpty(errorCode)) return string.Empty;
            switch (errorCode)
            {
                case "DENY":
                    return "DENY";// HesCodeErrors.DENY.ToString().Replace('_', ' ');
                case "EXPIRED HES CODE":
                    return "EXPIRED HES CODE";// HesCodeErrors.EXPIRED_HES_CODE.ToString().Replace('_',' ');
                case "HES DOES NOT BELONG TO TC ID":
                    return "HES CODE DOES NOT BELONG TO TC ID";// HesCodeErrors.HES_CODE_DOES_NOT_BELONG_TO_TC_ID.ToString().Replace('_', ' ');
                case "INVALID HES CODE":
                    return "INVALID HES CODE";// HesCodeErrors.INVALID_HES_CODE.ToString().Replace('_', ' ');
                case "MUST PROVIDE TC ID NUMBER":
                    return "MUST PROVIDE TC ID NUMBER";// HesCodeErrors.MUST_PROVIDE_TC_ID_NUMBER.ToString().Replace('_', ' ');
                case "ACCEPT":
                    return "ACCEPT";// HesCodeErrors.MUST_PROVIDE_TC_ID_NUMBER.ToString().Replace('_', ' ');
                default:
                    return string.Empty;
            }
            return string.Empty;
        }


        private class BoomiFlightStatusServiceRequest
        {
            [JsonProperty(PropertyName = "Departure Airport")]
            public string DepartureAirport { get; set; }

            [JsonProperty(PropertyName = "Arrival Airport")]
            public string ArrivalAirport { get; set; }

            [JsonProperty(PropertyName = "Flight Date")]
            [JsonConverter(typeof(CustomDateTimeConverter))]
            public DateTime FlightDate { get; set; }

            [JsonProperty(PropertyName = "Flight Number")]
            public string FlightNumber { get; set; }
        }

        private class CustomDateTimeConverter : IsoDateTimeConverter
        {
            public CustomDateTimeConverter()
            {
                base.DateTimeFormat = "yyyy-MM-dd";
            }
        }

        public class AddFriendRequest
        {
            private readonly string _lang = "en";
            private readonly string _actionType = "addfriend";
            public string memberShipNumber { get; set; }
            public List<string> toEmails { get; set; }
            public string fromEmail { get; set; }
            public string friendName { get; set; }
            public MailParameters mailParams { get; set; }
            public string lang { get { return _lang; } }
            public string actionType { get { return _actionType; } }
        }

        public class MailParameters
        {
            public string friendName { get; set; }
            public string friendEmail { get; set; }
            public string memberName { get; set; }
            public string memberSurname { get; set; }
        }

        public class AddFriendResponse
        {
            public AddFriendResponse()
            {
                statusMessage = "";
                statuscode = "500";
                message = "";
            }

            [JsonProperty("statuscode")]
            public string statuscode { get; set; }
            [JsonProperty("statusMessage")]
            public string statusMessage { get; set; }
            [JsonProperty("object")]
            public string message { get; set; }
        }
    }
}
