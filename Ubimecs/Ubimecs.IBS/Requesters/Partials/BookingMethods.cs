using System.Diagnostics;
using System.Text;
using Ubimecs.IBS.Mappers.RequestMappers;
using Ubimecs.IBS.Models;
using Ubimecs.IBS.Utility;
using Ubimecs.Infrastructure.Caching;
using Ubimecs.Infrastructure.CRM.Contracts;
using Ubimecs.Infrastructure.Logging;
using Ubimecs.Infrastructure.Models.Common;
using Ubimecs.Infrastructure.Models.Request;
using Ubimecs.Infrastructure.Models.Response;

namespace Ubimecs.IBS.Requesters
{
    public partial class IbsServiceProvider
    {
        public BookingSearchFlightResponse BookingSearchFlight(BookingSearchFlightRequest request)
        {
            Session.CurrentFlow.BookingSearchFlightResponse = null;
            var lastSearchFlightDates = Session.CurrentFlow.BookingSearchFlightResponse?.Flights?.SelectMany(t => t.FlightOptions.Select(k => Convert.ToDateTime(k.FlightDate?.ToString("dd.MM.yyyy")))).Distinct().ToList();
            if (lastSearchFlightDates != null && lastSearchFlightDates.Count > 0 && request.Flights.Any(t => Session.CurrentFlow.BookingSearchFlightResponse.Flights.Select(k => k.ArrivalCode).Contains(t.ArrivalAirport)) && request.Flights.Any(t => Session.CurrentFlow.BookingSearchFlightResponse.Flights.Select(k => k.DepartureCode).Contains(t.DepartureAirport)))
            {
                var currentSearchFlightDates = request.Flights?.SelectMany(t => new[] { Convert.ToDateTime(t.DepartureDate.ToString("dd.MM.yyyy")), Convert.ToDateTime(t.ReturnDepartureDate?.ToString("dd.MM.yyyy")) }).ToList();
                foreach (var date in lastSearchFlightDates)
                {
                    //
                    if (!currentSearchFlightDates.Any(t => t == date))
                    {
                        var deletingFlight = Session.CurrentFlow?.PNR?.Flights?.FirstOrDefault(t => t.Segments?.FirstOrDefault()?.DepartureDate == Convert.ToDateTime(date));
                        var deletingFlightTmobId = deletingFlight?.TmobId;
                        var deletingFlightSegmentTmobIds = deletingFlight?.Segments?.Select(t => t.TmobId).ToList();
                        if (deletingFlightTmobId != null && deletingFlightSegmentTmobIds.Count > 0 && deletingFlightSegmentTmobIds != null)
                        {
                            Session.CurrentFlow?.PNR?.Flights?.RemoveAll(t => t.TmobId == deletingFlightTmobId);
                            Session.CurrentFlow?.PNR?.SportEquipments?.RemoveAll(t => t.FlightTmobId == deletingFlightTmobId);
                            Session.CurrentFlow?.PNR?.Seats?.RemoveAll(t => deletingFlightSegmentTmobIds.Contains(t.SegmentTmobId));
                            Session.CurrentFlow?.PNR?.ExtraBaggages?.RemoveAll(t => t.TmobId == deletingFlightTmobId);
                            Session.CurrentFlow?.PNR?.Coronas?.RemoveAll(t => t.TmobId == deletingFlightTmobId);
                            Session.CurrentFlow?.PNR?.Meals?.RemoveAll(t => deletingFlightSegmentTmobIds.Contains(t.SegmentTmobId));
                            Session.CurrentFlow?.PNR?.IfeServices?.RemoveAll(t => t.TmobId == deletingFlightTmobId);
                        }
                    }
                }
            }
            else
            {
                Session.CurrentFlow.PNR.Flights.Clear();
                Session.CurrentFlow.PNR.ClearServices();
            }
           
            var IbsRequest = request.Map(Session);
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            var response = new IbsRequester<AirShoppingRQ, AirShoppingRS>(Session, ServiceName, GeneralConstants.SUNEXPRESS_BOOKING_SERVICE_URL, GeneralConstants.BOOKING_API_USERNAME, GeneralConstants.BOOKING_API_PASSWORD)
                .Execute(IbsRequest);
            stopwatch.Stop();
            //_utilityService.InsertServiceTime(stopwatch.ElapsedMilliseconds, (int)ServiceTimeTypeEnum.IBS, ServiceName);
            Session.CurrentFlow.SaveIbsData(response, IbsDataTypeEnum.ShopAirResponse);

            var result = response.Map(request, Session);
         

            Session.CurrentFlow.PNR.Currency = result.Flights?.FirstOrDefault()?.PriceCalendar?.FirstOrDefault()?.Currency ?? CurrencyTypeEnum.TRY.ToString();
            result.Currency = Session.CurrentFlow.PNR.Currency;
            Session.CurrentFlow.BookingSearchFlightResponse = result;
            Session.CurrentFlow.PNR.Passengers = result.Passengers.Select(s => new Ubimecs.Infrastructure.Models.PNR.Passenger
            {
                GlobalId = s.Id,
                LocalId = s.Id,
                Name = null,
                PassengerType = (PassengerTypeEnum)Enum.Parse(typeof(PassengerTypeEnum), s.PassengerType, true),
                Surname = null
            }).ToList();

            if (Ubimecs.Infrastructure.Utilities.Utility.FlightSelectedBefore(Session) && result.Flights.SelectMany(t => t.FlightOptions).Any(k => k != null))
            {
                var currentSearchFlightDates = request.Flights?.SelectMany(t => new[] { Convert.ToDateTime(t.DepartureDate.ToString("dd.MM.yyyy")), Convert.ToDateTime(t.ReturnDepartureDate?.ToString("dd.MM.yyyy")) }).ToList();
                var selectingFlights = new List<string>();
                if (currentSearchFlightDates != null && currentSearchFlightDates.Count > 0)
                {
                    foreach (var date in currentSearchFlightDates)
                    {
                        if ((bool)Session.CurrentFlow?.PNR?.Flights?.Any(t => t != null))
                        {
                            var existingFlight = Session.CurrentFlow?.PNR?.Flights?.FirstOrDefault(t => t.Segments.FirstOrDefault().DepartureDate.Date == Convert.ToDateTime(date));
                            if (existingFlight != null)
                            {
                                selectingFlights.Add(existingFlight.TmobId);
                            }
                        }
                    }
                }
                if (selectingFlights.Count > 0)
                {
                    BookingSelectFlight(new FlightSelectRequest { FlightIds = selectingFlights });
                }

            }

            return result;
        }

        public FlightSelectResponse BookingSelectFlight(FlightSelectRequest request)
        {
            var bundleSelections = Session.CurrentFlow.PNR.Flights.Select(s =>
            new
            {
                s.TmobId,
                s.SelectedBundle
            }).ToList();


            Session.CurrentFlow.PNR.Flights.Clear();
            Session.CurrentFlow.PNR.ClearServices();


            Session.CurrentFlow.PNR.Flights = request.FlightIds.Select(s => Session.CurrentFlow.BookingSearchFlightResponse.GetSessionPnrFlightDTO(s)).Any(k => k != null) ?
                                                    request.FlightIds.Select(s => Session.CurrentFlow.BookingSearchFlightResponse.GetSessionPnrFlightDTO(s)).ToList() :
                                                    new List<Ubimecs.Infrastructure.Models.DTO.Flight.SessionPnrFlightDTO>();


            foreach (var item in Session.CurrentFlow.PNR.Flights)
            {
                if (item != null && item.TmobId != null)
                {
                    item.SelectedBundle = bundleSelections.FirstOrDefault(f => f.TmobId == item.TmobId)?.SelectedBundle ?? ServiceCategoryEnum.SUN_ECO_BUNDLE;                   
                }
            }

            Session.Save();

            BookingSaveMetaData();
            try
            {
                foreach (var item in request.FlightIds)
                {
                    var responseBaggageAllowence = GetBaggageAllowance(new GetBaggageAllowanceRequest { TmobId = item });
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.ErrorLog(Session.SessionId, ServiceName, ex);
            }
            FlightSelectResponse result = new FlightSelectResponse();
            ServicesResponse services = new ServicesResponse();
            List<Task> tasks = new List<Task>();
            try
            {
                foreach (var item in request.FlightIds)
                {
                    var responseBaggageAllowence = GetBaggageAllowance(new GetBaggageAllowanceRequest { TmobId = item });
                }
            }
            catch (Exception ex)
            {

            }

            try
            {
                Stopwatch stopwatch = new Stopwatch();
                stopwatch.Start();
                tasks.Add(Task.Run(() =>
                    {
                        var ibsRequst = Session.MapToOfferPrice();

                        var response = new IbsRequester<OfferPriceRQ, OfferPriceRS>(Session, ServiceName, GeneralConstants.SUNEXPRESS_BOOKING_SERVICE_URL, GeneralConstants.BOOKING_API_USERNAME, GeneralConstants.BOOKING_API_PASSWORD)
                           .Execute(ibsRequst);

                        result = new FlightSelectResponse(response.Map(Session), Session.CurrentFlow.Type);

                    }));

                tasks.Add(Task.Run(() =>
                {
                    services = GetServices();
                }));

                Task.WaitAll(tasks.ToArray());
                stopwatch.Stop();
                //_utilityService.InsertServiceTime(stopwatch.ElapsedMilliseconds, (int)ServiceTimeTypeEnum.IBS, ServiceName);
            }
            catch (System.Exception ex)
            {
                if (ex.InnerException != null)
                {
                    throw ex.InnerException;
                }
                else
                {
                    throw ex;
                }
            }

            result.FlightServices = services;
            Session.CurrentFlow.Services = services;
            Session.CurrentFlow.BookingSelectFlightResponse = result;
            return result;
        }

        public BookingSelectBundleResponse BookingSelectBundle(BookingSelectBundleRequest request)
        {
            //BookingSelectBundleResponse result = new BookingSelectBundleResponse();
            var flight = Session.CurrentFlow.PNR.Flights.FirstOrDefault(f => f.TmobId == request.TmobId);

            if (flight != null)
            {
                bool isbundleTypeChanging = flight.SelectedBundle != (ServiceCategoryEnum)request.BundleCategoryId;
                flight.SelectedBundle = (ServiceCategoryEnum)request.BundleCategoryId;
                Session.CurrentFlow.PNR.Meals.RemoveAll(r => flight.Segments.Any(a => a.TmobId == r.SegmentTmobId));

                var ibsRequst = Session.MapToOfferPrice(isbundleTypeChanging);
                Stopwatch stopwatch = new Stopwatch();
                stopwatch.Start();
                var response = new IbsRequester<OfferPriceRQ, OfferPriceRS>(Session, ServiceName, GeneralConstants.SUNEXPRESS_BOOKING_SERVICE_URL, GeneralConstants.BOOKING_API_USERNAME, GeneralConstants.BOOKING_API_PASSWORD)
                   .Execute(ibsRequst);
                stopwatch.Stop();
                //_utilityService.InsertServiceTime(stopwatch.ElapsedMilliseconds, (int)ServiceTimeTypeEnum.IBS, ServiceName);
                var result = new BookingSelectBundleResponse(response.Map(Session));
                return result;

            }
            else
            {
                return null;
            }
        }

        public BasePriceResponse BookingSelectSeat(BookingSelectSeatRequest request)
        {
            var ibsRequst = Session.MapToOfferPrice();
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            var response = new IbsRequester<OfferPriceRQ, OfferPriceRS>(Session, ServiceName, GeneralConstants.SUNEXPRESS_BOOKING_SERVICE_URL, GeneralConstants.BOOKING_API_USERNAME, GeneralConstants.BOOKING_API_PASSWORD)
               .Execute(ibsRequst);
            stopwatch.Stop();
            //_utilityService.InsertServiceTime(stopwatch.ElapsedMilliseconds, (int)ServiceTimeTypeEnum.IBS, ServiceName);
            return response.Map(Session);
        }

        public BasePriceResponse BookingReSelectSeat(BookingReSelectSeatRequest request)
        {
            var ibsRequst = Session.MapToOfferPrice();
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            var response = new IbsRequester<OfferPriceRQ, OfferPriceRS>(Session, ServiceName, GeneralConstants.SUNEXPRESS_BOOKING_SERVICE_URL, GeneralConstants.BOOKING_API_USERNAME, GeneralConstants.BOOKING_API_PASSWORD)
               .Execute(ibsRequst);
            stopwatch.Stop();
            //_utilityService.InsertServiceTime(stopwatch.ElapsedMilliseconds, (int)ServiceTimeTypeEnum.IBS, ServiceName);
            return response.Map(Session);
        }

        public BasePriceResponse BookingSetExtraBaggage(SetExtraBaggageRequest request)
        {
            foreach (var item in Session.CurrentFlow.PNR.ExtraBaggages.Select(s => s.TmobId).Distinct())
            {
                var chargeResponse = Session.CurrentFlow.GetIbsData<BaggageChargesRS>(IbsDataTypeEnum.BaggageCharge.ToString() + "_" + item);

                if (chargeResponse == null)
                {
                    var dummyResposne = GetBaggageAllowance(new GetBaggageAllowanceRequest
                    {
                        TmobId = item
                    });
                }
            }

            var ibsRequst = Session.MapToOfferPrice();
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            var response = new IbsRequester<OfferPriceRQ, OfferPriceRS>(Session, ServiceName, GeneralConstants.SUNEXPRESS_BOOKING_SERVICE_URL, GeneralConstants.BOOKING_API_USERNAME, GeneralConstants.BOOKING_API_PASSWORD)
               .Execute(ibsRequst);
            stopwatch.Stop();
            //_utilityService.InsertServiceTime(stopwatch.ElapsedMilliseconds, (int)ServiceTimeTypeEnum.IBS, ServiceName);
            return response.Map(Session);
        }

        public BasePriceResponse BookingSetSportEquipment(SetSportEquipmentRequest request)
        {
            var ibsRequst = Session.MapToOfferPrice();
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            var response = new IbsRequester<OfferPriceRQ, OfferPriceRS>(Session, ServiceName, GeneralConstants.SUNEXPRESS_BOOKING_SERVICE_URL, GeneralConstants.BOOKING_API_USERNAME, GeneralConstants.BOOKING_API_PASSWORD)
               .Execute(ibsRequst);
            stopwatch.Stop();
            //_utilityService.InsertServiceTime(stopwatch.ElapsedMilliseconds, (int)ServiceTimeTypeEnum.IBS, ServiceName);
            return response.Map(Session);
        }

        public BasePriceResponse BookingReSetSportEquipment(BookingReSetSportEquipmentRequest request)
        {
            var ibsRequst = Session.MapToOfferPrice();
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            var response = new IbsRequester<OfferPriceRQ, OfferPriceRS>(Session, ServiceName, GeneralConstants.SUNEXPRESS_BOOKING_SERVICE_URL, GeneralConstants.BOOKING_API_USERNAME, GeneralConstants.BOOKING_API_PASSWORD)
               .Execute(ibsRequst);
            stopwatch.Stop();
            //_utilityService.InsertServiceTime(stopwatch.ElapsedMilliseconds, (int)ServiceTimeTypeEnum.IBS, ServiceName);
            return response.Map(Session);
        }

        public BasePriceResponse BookingSetCorona(SetCoronaRequest request)
        {
            var ibsRequst = Session.MapToOfferPrice();
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            var response = new IbsRequester<OfferPriceRQ, OfferPriceRS>(Session, ServiceName, GeneralConstants.SUNEXPRESS_BOOKING_SERVICE_URL, GeneralConstants.BOOKING_API_USERNAME, GeneralConstants.BOOKING_API_PASSWORD)
               .Execute(ibsRequst);
            stopwatch.Stop();
            //_utilityService.InsertServiceTime(stopwatch.ElapsedMilliseconds, (int)ServiceTimeTypeEnum.IBS, ServiceName);
            return response.Map(Session);
        }

        public BasePriceResponse BookingSelectMeal(BookingSelectMealRequest request)
        {
            var ibsRequst = Session.MapToOfferPrice();
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            var response = new IbsRequester<OfferPriceRQ, OfferPriceRS>(Session, ServiceName, GeneralConstants.SUNEXPRESS_BOOKING_SERVICE_URL, GeneralConstants.BOOKING_API_USERNAME, GeneralConstants.BOOKING_API_PASSWORD)
               .Execute(ibsRequst);
            stopwatch.Stop();
            //_utilityService.InsertServiceTime(stopwatch.ElapsedMilliseconds, (int)ServiceTimeTypeEnum.IBS, ServiceName);
            return response.Map(Session);
        }

        public BasePriceResponse BookingReSelectMeal(BookingReSelectMealRequest request)
        {
            var ibsRequst = Session.MapToOfferPrice();
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            var response = new IbsRequester<OfferPriceRQ, OfferPriceRS>(Session, ServiceName, GeneralConstants.SUNEXPRESS_BOOKING_SERVICE_URL, GeneralConstants.BOOKING_API_USERNAME, GeneralConstants.BOOKING_API_PASSWORD)
               .Execute(ibsRequst);
            stopwatch.Stop();
            //_utilityService.InsertServiceTime(stopwatch.ElapsedMilliseconds, (int)ServiceTimeTypeEnum.IBS, ServiceName);
            return response.Map(Session);
        }

        public BasePriceResponse BookingSelectIfe(SelectIfeRequest request)
        {
            var ibsRequst = Session.MapToOfferPrice();
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            var response = new IbsRequester<OfferPriceRQ, OfferPriceRS>(Session, ServiceName, GeneralConstants.SUNEXPRESS_BOOKING_SERVICE_URL, GeneralConstants.BOOKING_API_USERNAME, GeneralConstants.BOOKING_API_PASSWORD)
               .Execute(ibsRequst);
            stopwatch.Stop();
            //_utilityService.InsertServiceTime(stopwatch.ElapsedMilliseconds, (int)ServiceTimeTypeEnum.IBS, ServiceName);
            return response.Map(Session);
        }

        public BasePriceResponse BookingSelectGolfBundle(SelectGolfBundleRequest request)
        {
            var ibsRequst = Session.MapToOfferPrice();
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            var response = new IbsRequester<OfferPriceRQ, OfferPriceRS>(Session, ServiceName, GeneralConstants.SUNEXPRESS_BOOKING_SERVICE_URL, GeneralConstants.BOOKING_API_USERNAME, GeneralConstants.BOOKING_API_PASSWORD)
               .Execute(ibsRequst);
            stopwatch.Stop();
            //_utilityService.InsertServiceTime(stopwatch.ElapsedMilliseconds, (int)ServiceTimeTypeEnum.IBS, ServiceName);
            return response.Map(Session);
        }

        public BasePriceResponse BookingGetInstallmentFee(GetInstallmentFeeRequest request)
        {
            var ibsRequest = Session.MapToOfferPrice();
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            var response = new IbsRequester<OfferPriceRQ, OfferPriceRS>(Session, ServiceName, GeneralConstants.SUNEXPRESS_BOOKING_SERVICE_URL, GeneralConstants.BOOKING_API_USERNAME, GeneralConstants.BOOKING_API_PASSWORD)
               .Execute(ibsRequest);
            stopwatch.Stop();
            //_utilityService.InsertServiceTime(stopwatch.ElapsedMilliseconds, (int)ServiceTimeTypeEnum.IBS, ServiceName);
            return response.Map(Session);
        }

        public GetPnrInfoResponse CraeteOrder(CreateOrderRequest request,ICrmService crmService)
        {
            if (!string.IsNullOrEmpty(request.PaymentInformation.VoucherNumber))
            {
                Session.CurrentFlow.PNR.GiftVoucherAmount = GetGiftVoucherAmount(new GiftVoucherRequest
                {
                    GiftNumber = request.PaymentInformation.VoucherNumber
                })?.Amount ?? 0;
            }

            OrderCreateRQ ibsRequst;

            if (request.Is3dFinishingRequest)
            {
                OrderViewRS latestOrderView = Session.CurrentFlow.GetIbsData<OrderViewRS>(IbsDataTypeEnum.OrderRetrieve);
                Session.CurrentFlow.CreateOrderRequest.SecurePaymentInfomation.cavv = request.SecurePaymentInfomation.cavv;
                Session.CurrentFlow.CreateOrderRequest.SecurePaymentInfomation.xid = request.SecurePaymentInfomation.xid;
                Session.CurrentFlow.CreateOrderRequest.SecurePaymentInfomation.eci = request.SecurePaymentInfomation.eci;
                Session.CurrentFlow.CreateOrderRequest.SecurePaymentInfomation.md = request.SecurePaymentInfomation.md;
                Session.CurrentFlow.CreateOrderRequest.SecurePaymentInfomation.TRANS_ID = request.SecurePaymentInfomation.TRANS_ID;
                Session.CurrentFlow.CreateOrderRequest.SecurePaymentInfomation.RND = request.SecurePaymentInfomation.RND;
                Session.CurrentFlow.CreateOrderRequest.SecurePaymentInfomation.HASH = CleanSpecialCharacters(request.SecurePaymentInfomation.HASH);
                Session.CurrentFlow.CreateOrderRequest.SecurePaymentInfomation.HASH_PARAMS = request.SecurePaymentInfomation.HASH_PARAMS;
                Session.CurrentFlow.CreateOrderRequest.SecurePaymentInfomation.HASH_PARAMS_VAL = request.SecurePaymentInfomation.HASH_PARAMS_VAL;
                //TODO : Hata veren kısım düzeltilecek. Session.CurrentFlow.CreateOrderRequest.SecurePaymentInfomation.SPMTxnReference = latestOrderView?.GetSecurePaymentInfo(Session)?.SPMTxnReference;
                Session.CurrentFlow.CreateOrderRequest.SecurePaymentInfomation.Result = request.SecurePaymentInfomation.Result;
                Session.CurrentFlow.CreateOrderRequest.SecurePaymentInfomation.OrderId = request.SecurePaymentInfomation.OrderId;
                Session.CurrentFlow.CreateOrderRequest.SecurePaymentInfomation.Response = request.SecurePaymentInfomation.Response;
                Session.CurrentFlow.CreateOrderRequest.SecurePaymentInfomation.ModType = request.SecurePaymentInfomation.ModType;
                Session.CurrentFlow.CreateOrderRequest.SecurePaymentInfomation.SystemTransId = request.SecurePaymentInfomation.SystemTransId;
                Session.CurrentFlow.CreateOrderRequest.SecurePaymentInfomation.TotalAmount = request.SecurePaymentInfomation.TotalAmount;
                Session.CurrentFlow.CreateOrderRequest.SecurePaymentInfomation.InstallmentCount = request.SecurePaymentInfomation.InstallmentCount;
                Session.CurrentFlow.CreateOrderRequest.SecurePaymentInfomation.MDStatus = request.SecurePaymentInfomation.MDStatus;
                ibsRequst = CreateOrderMappers.Map(Session, Session.CurrentFlow.CreateOrderRequest);
            }
            else
            {
                ibsRequst = CreateOrderMappers.Map(Session, request);

                Session.CurrentFlow.CreateOrderRequest = request;
            }
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            var response = new IbsRequester<OrderCreateRQ, OrderViewRS>(Session, ServiceName, GeneralConstants.SUNEXPRESS_BOOKING_SERVICE_URL, GeneralConstants.BOOKING_API_USERNAME, GeneralConstants.BOOKING_API_PASSWORD, timeout: 120000)
               .Execute(ibsRequst);
            stopwatch.Stop();
            //_utilityService.InsertServiceTime(stopwatch.ElapsedMilliseconds, (int)ServiceTimeTypeEnum.IBS, ServiceName);
            return response.Map(Session);
        }

        public ChangeCurrencyResponse BookingChangeCurrency()
        {
            var result = new ChangeCurrencyResponse();
            var services = GetServices();
            var ibsRequst = Session.MapToOfferPrice();
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            var response = new IbsRequester<OfferPriceRQ, OfferPriceRS>(Session, ServiceName, GeneralConstants.SUNEXPRESS_BOOKING_SERVICE_URL, GeneralConstants.BOOKING_API_USERNAME, GeneralConstants.BOOKING_API_PASSWORD)
               .Execute(ibsRequst);
            stopwatch.Stop();
            //_utilityService.InsertServiceTime(stopwatch.ElapsedMilliseconds, (int)ServiceTimeTypeEnum.IBS, ServiceName);
            result.FlightServices = services;
            result.PriceInformation = response.Map(Session);
            return result;
        }

        public string CleanSpecialCharacters(string hash)
        {
            StringBuilder sb = new StringBuilder();
            foreach (char c in hash)
            {
                if (!char.IsLetterOrDigit(c))
                {
                    string escapedChrc = Uri.EscapeDataString(c.ToString()).ToLower();
                    sb.Append(escapedChrc);
                }
                else
                {
                    sb.Append(c);
                }
                //else
                //{
                //    string a = Uri.EscapeUriString(c.ToString());
                //    sb.Append(a.ToLower());
                //}
            }
            return sb.ToString();

        }




    }
}
