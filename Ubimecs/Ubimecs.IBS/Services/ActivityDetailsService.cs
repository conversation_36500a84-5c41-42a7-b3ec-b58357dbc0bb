//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Ubimecs.IBS.Services.ActivityDetailsService {
    
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iloyal/common/type/")]
    public partial class WebServiceException : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string faultcodeField;
        
        private string faultstringField;
        
        private string[] faultdataField;
        
        private WebServiceTransactionHeader txnHeaderField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=0)]
        public string faultcode {
            get {
                return this.faultcodeField;
            }
            set {
                this.faultcodeField = value;
                this.RaisePropertyChanged("faultcode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=1)]
        public string faultstring {
            get {
                return this.faultstringField;
            }
            set {
                this.faultstringField = value;
                this.RaisePropertyChanged("faultstring");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("faultdata", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=2)]
        public string[] faultdata {
            get {
                return this.faultdataField;
            }
            set {
                this.faultdataField = value;
                this.RaisePropertyChanged("faultdata");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=3)]
        public WebServiceTransactionHeader txnHeader {
            get {
                return this.txnHeaderField;
            }
            set {
                this.txnHeaderField = value;
                this.RaisePropertyChanged("txnHeader");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iloyal/common/type/")]
    public partial class WebServiceTransactionHeader : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string transactionIDField;
        
        private string userNameField;
        
        private string channelUserCodeField;
        
        private string transactionTokenField;
        
        private System.DateTime timeStampField;
        
        private string deviceIdField;
        
        private string deviceIPField;
        
        private string deviceOperatingSystemField;
        
        private string deviceLocationLatitudeField;
        
        private string deviceLocationLongitudeField;
        
        private string deviceCountryCodeField;
        
        private string additionalInfoField;
        
        private string remarksField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string transactionID {
            get {
                return this.transactionIDField;
            }
            set {
                this.transactionIDField = value;
                this.RaisePropertyChanged("transactionID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string userName {
            get {
                return this.userNameField;
            }
            set {
                this.userNameField = value;
                this.RaisePropertyChanged("userName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=2)]
        public string channelUserCode {
            get {
                return this.channelUserCodeField;
            }
            set {
                this.channelUserCodeField = value;
                this.RaisePropertyChanged("channelUserCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=3)]
        public string transactionToken {
            get {
                return this.transactionTokenField;
            }
            set {
                this.transactionTokenField = value;
                this.RaisePropertyChanged("transactionToken");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public System.DateTime timeStamp {
            get {
                return this.timeStampField;
            }
            set {
                this.timeStampField = value;
                this.RaisePropertyChanged("timeStamp");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=5)]
        public string deviceId {
            get {
                return this.deviceIdField;
            }
            set {
                this.deviceIdField = value;
                this.RaisePropertyChanged("deviceId");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=6)]
        public string deviceIP {
            get {
                return this.deviceIPField;
            }
            set {
                this.deviceIPField = value;
                this.RaisePropertyChanged("deviceIP");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=7)]
        public string deviceOperatingSystem {
            get {
                return this.deviceOperatingSystemField;
            }
            set {
                this.deviceOperatingSystemField = value;
                this.RaisePropertyChanged("deviceOperatingSystem");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=8)]
        public string deviceLocationLatitude {
            get {
                return this.deviceLocationLatitudeField;
            }
            set {
                this.deviceLocationLatitudeField = value;
                this.RaisePropertyChanged("deviceLocationLatitude");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=9)]
        public string deviceLocationLongitude {
            get {
                return this.deviceLocationLongitudeField;
            }
            set {
                this.deviceLocationLongitudeField = value;
                this.RaisePropertyChanged("deviceLocationLongitude");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=10)]
        public string deviceCountryCode {
            get {
                return this.deviceCountryCodeField;
            }
            set {
                this.deviceCountryCodeField = value;
                this.RaisePropertyChanged("deviceCountryCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=11)]
        public string additionalInfo {
            get {
                return this.additionalInfoField;
            }
            set {
                this.additionalInfoField = value;
                this.RaisePropertyChanged("additionalInfo");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=12)]
        public string remarks {
            get {
                return this.remarksField;
            }
            set {
                this.remarksField = value;
                this.RaisePropertyChanged("remarks");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iloyal/member/activitydetail/type/")]
    public partial class MultiLingualDescription : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string languageCodeField;
        
        private string descriptionField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string languageCode {
            get {
                return this.languageCodeField;
            }
            set {
                this.languageCodeField = value;
                this.RaisePropertyChanged("languageCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string description {
            get {
                return this.descriptionField;
            }
            set {
                this.descriptionField = value;
                this.RaisePropertyChanged("description");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iloyal/member/activitydetail/type/")]
    public partial class PartnerDynamicAttributes : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string attributeCodeField;
        
        private string attributeValueField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string attributeCode {
            get {
                return this.attributeCodeField;
            }
            set {
                this.attributeCodeField = value;
                this.RaisePropertyChanged("attributeCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string attributeValue {
            get {
                return this.attributeValueField;
            }
            set {
                this.attributeValueField = value;
                this.RaisePropertyChanged("attributeValue");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iloyal/member/activitydetail/type/")]
    public partial class RedemptionDynamicAttributes : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string attributeCodeField;
        
        private string attributeValueField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string attributeCode {
            get {
                return this.attributeCodeField;
            }
            set {
                this.attributeCodeField = value;
                this.RaisePropertyChanged("attributeCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string attributeValue {
            get {
                return this.attributeValueField;
            }
            set {
                this.attributeValueField = value;
                this.RaisePropertyChanged("attributeValue");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iloyal/member/activitydetail/type/")]
    public partial class AccrualDynamicAttributes : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string attributeCodeField;
        
        private string attributeValueField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string attributeCode {
            get {
                return this.attributeCodeField;
            }
            set {
                this.attributeCodeField = value;
                this.RaisePropertyChanged("attributeCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string attributeValue {
            get {
                return this.attributeValueField;
            }
            set {
                this.attributeValueField = value;
                this.RaisePropertyChanged("attributeValue");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iloyal/member/activitydetail/type/")]
    public partial class ActivityAttribute : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string attributeCodeField;
        
        private string attributeValueField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string attributeCode {
            get {
                return this.attributeCodeField;
            }
            set {
                this.attributeCodeField = value;
                this.RaisePropertyChanged("attributeCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string attributeValue {
            get {
                return this.attributeValueField;
            }
            set {
                this.attributeValueField = value;
                this.RaisePropertyChanged("attributeValue");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iloyal/member/common/type/")]
    public partial class BonusDetail : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string bonusCodeField;
        
        private string bonusNameField;
        
        private double pointsField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string bonusCode {
            get {
                return this.bonusCodeField;
            }
            set {
                this.bonusCodeField = value;
                this.RaisePropertyChanged("bonusCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string bonusName {
            get {
                return this.bonusNameField;
            }
            set {
                this.bonusNameField = value;
                this.RaisePropertyChanged("bonusName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public double points {
            get {
                return this.pointsField;
            }
            set {
                this.pointsField = value;
                this.RaisePropertyChanged("points");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iloyal/member/activitydetail/type/")]
    public partial class PointTransactionDetail : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string pointTypeField;
        
        private string pointDetailsField;
        
        private double pointsField;
        
        private double creditLimitField;
        
        private bool creditLimitFieldSpecified;
        
        private BonusDetail[] bonusDetailsField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string pointType {
            get {
                return this.pointTypeField;
            }
            set {
                this.pointTypeField = value;
                this.RaisePropertyChanged("pointType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=1)]
        public string pointDetails {
            get {
                return this.pointDetailsField;
            }
            set {
                this.pointDetailsField = value;
                this.RaisePropertyChanged("pointDetails");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public double points {
            get {
                return this.pointsField;
            }
            set {
                this.pointsField = value;
                this.RaisePropertyChanged("points");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public double creditLimit {
            get {
                return this.creditLimitField;
            }
            set {
                this.creditLimitField = value;
                this.RaisePropertyChanged("creditLimit");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool creditLimitSpecified {
            get {
                return this.creditLimitFieldSpecified;
            }
            set {
                this.creditLimitFieldSpecified = value;
                this.RaisePropertyChanged("creditLimitSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("bonusDetails", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=4)]
        public BonusDetail[] bonusDetails {
            get {
                return this.bonusDetailsField;
            }
            set {
                this.bonusDetailsField = value;
                this.RaisePropertyChanged("bonusDetails");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iloyal/member/activitydetail/type/")]
    public partial class ActivityDetail : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string activityTypeField;
        
        private string activityDateField;
        
        private string locationActivityDateField;
        
        private string activityDescriptionField;
        
        private string reasonCodeField;
        
        private string parentActivityTypeField;
        
        private System.Nullable<bool> hasNextPageField;
        
        private System.Nullable<int> absoluteIndexField;
        
        private PointTransactionDetail[] pointDetailsField;
        
        private ActivityAttribute[] activityAttributesField;
        
        private AccrualDynamicAttributes[] accrualDynamicAttributesField;
        
        private RedemptionDynamicAttributes[] redemptionDynamicAttributesField;
        
        private PartnerDynamicAttributes[] partnerDynamicAttributesField;
        
        private string activityReferenceNumberField;
        
        private MultiLingualDescription[] multilingualActivityDescriptionsField;
        
        private MultiLingualDescription[] multilingualActivityRemarksField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string activityType {
            get {
                return this.activityTypeField;
            }
            set {
                this.activityTypeField = value;
                this.RaisePropertyChanged("activityType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string activityDate {
            get {
                return this.activityDateField;
            }
            set {
                this.activityDateField = value;
                this.RaisePropertyChanged("activityDate");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=2)]
        public string locationActivityDate {
            get {
                return this.locationActivityDateField;
            }
            set {
                this.locationActivityDateField = value;
                this.RaisePropertyChanged("locationActivityDate");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=3)]
        public string activityDescription {
            get {
                return this.activityDescriptionField;
            }
            set {
                this.activityDescriptionField = value;
                this.RaisePropertyChanged("activityDescription");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=4)]
        public string reasonCode {
            get {
                return this.reasonCodeField;
            }
            set {
                this.reasonCodeField = value;
                this.RaisePropertyChanged("reasonCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=5)]
        public string parentActivityType {
            get {
                return this.parentActivityTypeField;
            }
            set {
                this.parentActivityTypeField = value;
                this.RaisePropertyChanged("parentActivityType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=6)]
        public System.Nullable<bool> hasNextPage {
            get {
                return this.hasNextPageField;
            }
            set {
                this.hasNextPageField = value;
                this.RaisePropertyChanged("hasNextPage");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=7)]
        public System.Nullable<int> absoluteIndex {
            get {
                return this.absoluteIndexField;
            }
            set {
                this.absoluteIndexField = value;
                this.RaisePropertyChanged("absoluteIndex");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("pointDetails", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=8)]
        public PointTransactionDetail[] pointDetails {
            get {
                return this.pointDetailsField;
            }
            set {
                this.pointDetailsField = value;
                this.RaisePropertyChanged("pointDetails");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("activityAttributes", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=9)]
        public ActivityAttribute[] activityAttributes {
            get {
                return this.activityAttributesField;
            }
            set {
                this.activityAttributesField = value;
                this.RaisePropertyChanged("activityAttributes");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("accrualDynamicAttributes", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=10)]
        public AccrualDynamicAttributes[] accrualDynamicAttributes {
            get {
                return this.accrualDynamicAttributesField;
            }
            set {
                this.accrualDynamicAttributesField = value;
                this.RaisePropertyChanged("accrualDynamicAttributes");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("redemptionDynamicAttributes", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=11)]
        public RedemptionDynamicAttributes[] redemptionDynamicAttributes {
            get {
                return this.redemptionDynamicAttributesField;
            }
            set {
                this.redemptionDynamicAttributesField = value;
                this.RaisePropertyChanged("redemptionDynamicAttributes");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("partnerDynamicAttributes", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=12)]
        public PartnerDynamicAttributes[] partnerDynamicAttributes {
            get {
                return this.partnerDynamicAttributesField;
            }
            set {
                this.partnerDynamicAttributesField = value;
                this.RaisePropertyChanged("partnerDynamicAttributes");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=13)]
        public string activityReferenceNumber {
            get {
                return this.activityReferenceNumberField;
            }
            set {
                this.activityReferenceNumberField = value;
                this.RaisePropertyChanged("activityReferenceNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("multilingualActivityDescriptions", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=14)]
        public MultiLingualDescription[] multilingualActivityDescriptions {
            get {
                return this.multilingualActivityDescriptionsField;
            }
            set {
                this.multilingualActivityDescriptionsField = value;
                this.RaisePropertyChanged("multilingualActivityDescriptions");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("multilingualActivityRemarks", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=15)]
        public MultiLingualDescription[] multilingualActivityRemarks {
            get {
                return this.multilingualActivityRemarksField;
            }
            set {
                this.multilingualActivityRemarksField = value;
                this.RaisePropertyChanged("multilingualActivityRemarks");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.ServiceContractAttribute(Namespace="http://www.ibsplc.com/iloyal/member/activitydetail/wsdl", ConfigurationName="ActivityDetailsService.ActivityDetails")]
    public interface ActivityDetails {
        
        // CODEGEN: Generating message contract since the operation retrieveActivityDetails is neither RPC nor document wrapped.
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Ubimecs.IBS.Services.ActivityDetailsService.WebServiceException), Action="", Name="MemberWebServiceException", Namespace="http://www.ibsplc.com/iloyal/member/activitydetail/type/")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        Ubimecs.IBS.Services.ActivityDetailsService.ActivityDetailsResponse1 retrieveActivityDetails(Ubimecs.IBS.Services.ActivityDetailsService.ActivityDetailsRequest1 request);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.ActivityDetailsService.ActivityDetailsResponse1> retrieveActivityDetailsAsync(Ubimecs.IBS.Services.ActivityDetailsService.ActivityDetailsRequest1 request);
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iloyal/member/activitydetail/type/")]
    public partial class ActivityDetailsRequest : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string companyCodeField;
        
        private string programCodeField;
        
        private string membershipNumberField;
        
        private string activityStatusField;
        
        private string fromDateField;
        
        private string toDateField;
        
        private System.Nullable<int> pageNumberField;
        
        private System.Nullable<int> absoluteIndexField;
        
        private System.Nullable<int> pageSizeField;
        
        private string activtyAttributesRequiredField;
        
        private string preferredLanguageCodeField;
        
        private string dateTypeIdentifierField;
        
        private WebServiceTransactionHeader txnHeaderField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string companyCode {
            get {
                return this.companyCodeField;
            }
            set {
                this.companyCodeField = value;
                this.RaisePropertyChanged("companyCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string programCode {
            get {
                return this.programCodeField;
            }
            set {
                this.programCodeField = value;
                this.RaisePropertyChanged("programCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string membershipNumber {
            get {
                return this.membershipNumberField;
            }
            set {
                this.membershipNumberField = value;
                this.RaisePropertyChanged("membershipNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string activityStatus {
            get {
                return this.activityStatusField;
            }
            set {
                this.activityStatusField = value;
                this.RaisePropertyChanged("activityStatus");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string fromDate {
            get {
                return this.fromDateField;
            }
            set {
                this.fromDateField = value;
                this.RaisePropertyChanged("fromDate");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string toDate {
            get {
                return this.toDateField;
            }
            set {
                this.toDateField = value;
                this.RaisePropertyChanged("toDate");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=6)]
        public System.Nullable<int> pageNumber {
            get {
                return this.pageNumberField;
            }
            set {
                this.pageNumberField = value;
                this.RaisePropertyChanged("pageNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=7)]
        public System.Nullable<int> absoluteIndex {
            get {
                return this.absoluteIndexField;
            }
            set {
                this.absoluteIndexField = value;
                this.RaisePropertyChanged("absoluteIndex");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=8)]
        public System.Nullable<int> pageSize {
            get {
                return this.pageSizeField;
            }
            set {
                this.pageSizeField = value;
                this.RaisePropertyChanged("pageSize");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=9)]
        public string activtyAttributesRequired {
            get {
                return this.activtyAttributesRequiredField;
            }
            set {
                this.activtyAttributesRequiredField = value;
                this.RaisePropertyChanged("activtyAttributesRequired");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=10)]
        public string preferredLanguageCode {
            get {
                return this.preferredLanguageCodeField;
            }
            set {
                this.preferredLanguageCodeField = value;
                this.RaisePropertyChanged("preferredLanguageCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=11)]
        public string dateTypeIdentifier {
            get {
                return this.dateTypeIdentifierField;
            }
            set {
                this.dateTypeIdentifierField = value;
                this.RaisePropertyChanged("dateTypeIdentifier");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=12)]
        public WebServiceTransactionHeader txnHeader {
            get {
                return this.txnHeaderField;
            }
            set {
                this.txnHeaderField = value;
                this.RaisePropertyChanged("txnHeader");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iloyal/member/activitydetail/type/")]
    public partial class ActivityDetailsResponse : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string companyCodeField;
        
        private string programCodeField;
        
        private string membershipNumberField;
        
        private ActivityDetail[] activityDetailsField;
        
        private WebServiceTransactionHeader txnHeaderField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string companyCode {
            get {
                return this.companyCodeField;
            }
            set {
                this.companyCodeField = value;
                this.RaisePropertyChanged("companyCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string programCode {
            get {
                return this.programCodeField;
            }
            set {
                this.programCodeField = value;
                this.RaisePropertyChanged("programCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string membershipNumber {
            get {
                return this.membershipNumberField;
            }
            set {
                this.membershipNumberField = value;
                this.RaisePropertyChanged("membershipNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("activityDetails", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=3)]
        public ActivityDetail[] activityDetails {
            get {
                return this.activityDetailsField;
            }
            set {
                this.activityDetailsField = value;
                this.RaisePropertyChanged("activityDetails");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public WebServiceTransactionHeader txnHeader {
            get {
                return this.txnHeaderField;
            }
            set {
                this.txnHeaderField = value;
                this.RaisePropertyChanged("txnHeader");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class ActivityDetailsRequest1 {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iloyal/member/activitydetail/type/", Order=0)]
        public Ubimecs.IBS.Services.ActivityDetailsService.ActivityDetailsRequest ActivityDetailsRequest;
        
        public ActivityDetailsRequest1() {
        }
        
        public ActivityDetailsRequest1(Ubimecs.IBS.Services.ActivityDetailsService.ActivityDetailsRequest ActivityDetailsRequest) {
            this.ActivityDetailsRequest = ActivityDetailsRequest;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class ActivityDetailsResponse1 {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iloyal/member/activitydetail/type/", Order=0)]
        public Ubimecs.IBS.Services.ActivityDetailsService.ActivityDetailsResponse ActivityDetailsResponse;
        
        public ActivityDetailsResponse1() {
        }
        
        public ActivityDetailsResponse1(Ubimecs.IBS.Services.ActivityDetailsService.ActivityDetailsResponse ActivityDetailsResponse) {
            this.ActivityDetailsResponse = ActivityDetailsResponse;
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    public interface ActivityDetailsChannel : Ubimecs.IBS.Services.ActivityDetailsService.ActivityDetails, System.ServiceModel.IClientChannel {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    public partial class ActivityDetailsClient : System.ServiceModel.ClientBase<Ubimecs.IBS.Services.ActivityDetailsService.ActivityDetails>, Ubimecs.IBS.Services.ActivityDetailsService.ActivityDetails {
        
        public ActivityDetailsClient() {
        }
        
        public ActivityDetailsClient(string endpointConfigurationName) : 
                base(endpointConfigurationName) {
        }
        
        public ActivityDetailsClient(string endpointConfigurationName, string remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public ActivityDetailsClient(string endpointConfigurationName, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public ActivityDetailsClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress) {
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Ubimecs.IBS.Services.ActivityDetailsService.ActivityDetailsResponse1 Ubimecs.IBS.Services.ActivityDetailsService.ActivityDetails.retrieveActivityDetails(Ubimecs.IBS.Services.ActivityDetailsService.ActivityDetailsRequest1 request) {
            return base.Channel.retrieveActivityDetails(request);
        }
        
        public Ubimecs.IBS.Services.ActivityDetailsService.ActivityDetailsResponse retrieveActivityDetails(Ubimecs.IBS.Services.ActivityDetailsService.ActivityDetailsRequest ActivityDetailsRequest) {
            Ubimecs.IBS.Services.ActivityDetailsService.ActivityDetailsRequest1 inValue = new Ubimecs.IBS.Services.ActivityDetailsService.ActivityDetailsRequest1();
            inValue.ActivityDetailsRequest = ActivityDetailsRequest;
            Ubimecs.IBS.Services.ActivityDetailsService.ActivityDetailsResponse1 retVal = ((Ubimecs.IBS.Services.ActivityDetailsService.ActivityDetails)(this)).retrieveActivityDetails(inValue);
            return retVal.ActivityDetailsResponse;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.ActivityDetailsService.ActivityDetailsResponse1> Ubimecs.IBS.Services.ActivityDetailsService.ActivityDetails.retrieveActivityDetailsAsync(Ubimecs.IBS.Services.ActivityDetailsService.ActivityDetailsRequest1 request) {
            return base.Channel.retrieveActivityDetailsAsync(request);
        }
        
        public System.Threading.Tasks.Task<Ubimecs.IBS.Services.ActivityDetailsService.ActivityDetailsResponse1> retrieveActivityDetailsAsync(Ubimecs.IBS.Services.ActivityDetailsService.ActivityDetailsRequest ActivityDetailsRequest) {
            Ubimecs.IBS.Services.ActivityDetailsService.ActivityDetailsRequest1 inValue = new Ubimecs.IBS.Services.ActivityDetailsService.ActivityDetailsRequest1();
            inValue.ActivityDetailsRequest = ActivityDetailsRequest;
            return ((Ubimecs.IBS.Services.ActivityDetailsService.ActivityDetails)(this)).retrieveActivityDetailsAsync(inValue);
        }
    }
}
