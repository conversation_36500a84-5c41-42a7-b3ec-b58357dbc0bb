//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Ubimecs.IBS.Services.AddAccountNomineeService {
    
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iloyal/common/type/")]
    public partial class WebServiceException : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string faultcodeField;
        
        private string faultstringField;
        
        private string[] faultdataField;
        
        private WebServiceTransactionHeader txnHeaderField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=0)]
        public string faultcode {
            get {
                return this.faultcodeField;
            }
            set {
                this.faultcodeField = value;
                this.RaisePropertyChanged("faultcode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=1)]
        public string faultstring {
            get {
                return this.faultstringField;
            }
            set {
                this.faultstringField = value;
                this.RaisePropertyChanged("faultstring");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("faultdata", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=2)]
        public string[] faultdata {
            get {
                return this.faultdataField;
            }
            set {
                this.faultdataField = value;
                this.RaisePropertyChanged("faultdata");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=3)]
        public WebServiceTransactionHeader txnHeader {
            get {
                return this.txnHeaderField;
            }
            set {
                this.txnHeaderField = value;
                this.RaisePropertyChanged("txnHeader");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iloyal/common/type/")]
    public partial class WebServiceTransactionHeader : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string transactionIDField;
        
        private string userNameField;
        
        private string channelUserCodeField;
        
        private string transactionTokenField;
        
        private System.DateTime timeStampField;
        
        private string deviceIdField;
        
        private string deviceIPField;
        
        private string deviceOperatingSystemField;
        
        private string deviceLocationLatitudeField;
        
        private string deviceLocationLongitudeField;
        
        private string deviceCountryCodeField;
        
        private string additionalInfoField;
        
        private string remarksField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string transactionID {
            get {
                return this.transactionIDField;
            }
            set {
                this.transactionIDField = value;
                this.RaisePropertyChanged("transactionID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string userName {
            get {
                return this.userNameField;
            }
            set {
                this.userNameField = value;
                this.RaisePropertyChanged("userName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=2)]
        public string channelUserCode {
            get {
                return this.channelUserCodeField;
            }
            set {
                this.channelUserCodeField = value;
                this.RaisePropertyChanged("channelUserCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=3)]
        public string transactionToken {
            get {
                return this.transactionTokenField;
            }
            set {
                this.transactionTokenField = value;
                this.RaisePropertyChanged("transactionToken");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public System.DateTime timeStamp {
            get {
                return this.timeStampField;
            }
            set {
                this.timeStampField = value;
                this.RaisePropertyChanged("timeStamp");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=5)]
        public string deviceId {
            get {
                return this.deviceIdField;
            }
            set {
                this.deviceIdField = value;
                this.RaisePropertyChanged("deviceId");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=6)]
        public string deviceIP {
            get {
                return this.deviceIPField;
            }
            set {
                this.deviceIPField = value;
                this.RaisePropertyChanged("deviceIP");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=7)]
        public string deviceOperatingSystem {
            get {
                return this.deviceOperatingSystemField;
            }
            set {
                this.deviceOperatingSystemField = value;
                this.RaisePropertyChanged("deviceOperatingSystem");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=8)]
        public string deviceLocationLatitude {
            get {
                return this.deviceLocationLatitudeField;
            }
            set {
                this.deviceLocationLatitudeField = value;
                this.RaisePropertyChanged("deviceLocationLatitude");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=9)]
        public string deviceLocationLongitude {
            get {
                return this.deviceLocationLongitudeField;
            }
            set {
                this.deviceLocationLongitudeField = value;
                this.RaisePropertyChanged("deviceLocationLongitude");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=10)]
        public string deviceCountryCode {
            get {
                return this.deviceCountryCodeField;
            }
            set {
                this.deviceCountryCodeField = value;
                this.RaisePropertyChanged("deviceCountryCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=11)]
        public string additionalInfo {
            get {
                return this.additionalInfoField;
            }
            set {
                this.additionalInfoField = value;
                this.RaisePropertyChanged("additionalInfo");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=12)]
        public string remarks {
            get {
                return this.remarksField;
            }
            set {
                this.remarksField = value;
                this.RaisePropertyChanged("remarks");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iloyal/member/addaccountnominee/type/")]
    public partial class MemberActivityStatus : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string companyCodeField;
        
        private string programCodeField;
        
        private string membershipNumberField;
        
        private string activityNumberField;
        
        private string activityStatusField;
        
        private string activityCodeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string companyCode {
            get {
                return this.companyCodeField;
            }
            set {
                this.companyCodeField = value;
                this.RaisePropertyChanged("companyCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string programCode {
            get {
                return this.programCodeField;
            }
            set {
                this.programCodeField = value;
                this.RaisePropertyChanged("programCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string membershipNumber {
            get {
                return this.membershipNumberField;
            }
            set {
                this.membershipNumberField = value;
                this.RaisePropertyChanged("membershipNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string activityNumber {
            get {
                return this.activityNumberField;
            }
            set {
                this.activityNumberField = value;
                this.RaisePropertyChanged("activityNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string activityStatus {
            get {
                return this.activityStatusField;
            }
            set {
                this.activityStatusField = value;
                this.RaisePropertyChanged("activityStatus");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string activityCode {
            get {
                return this.activityCodeField;
            }
            set {
                this.activityCodeField = value;
                this.RaisePropertyChanged("activityCode");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iloyal/member/addaccountnominee/type/")]
    public partial class CustomerDynamicAttribute : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string attributeCodeField;
        
        private string attributeValueField;
        
        private string dynamicAttributeGroupNameField;
        
        private string operationFlagField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string attributeCode {
            get {
                return this.attributeCodeField;
            }
            set {
                this.attributeCodeField = value;
                this.RaisePropertyChanged("attributeCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string attributeValue {
            get {
                return this.attributeValueField;
            }
            set {
                this.attributeValueField = value;
                this.RaisePropertyChanged("attributeValue");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string dynamicAttributeGroupName {
            get {
                return this.dynamicAttributeGroupNameField;
            }
            set {
                this.dynamicAttributeGroupNameField = value;
                this.RaisePropertyChanged("dynamicAttributeGroupName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string operationFlag {
            get {
                return this.operationFlagField;
            }
            set {
                this.operationFlagField = value;
                this.RaisePropertyChanged("operationFlag");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iloyal/member/addaccountnominee/type/")]
    public partial class AccountNomineeDetail : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string nomineeReferenceNumberField;
        
        private string memberTypeField;
        
        private string customerNumberField;
        
        private string accountGroupTypeField;
        
        private string membershipNumberField;
        
        private string operationFlagField;
        
        private string relationshipField;
        
        private string titleField;
        
        private string givenNameField;
        
        private string secondNameField;
        
        private string familyNameField;
        
        private string displayNameField;
        
        private string genderField;
        
        private string dateOfBirthField;
        
        private string passportNumberField;
        
        private string countryOfResidenceField;
        
        private string emailAddressField;
        
        private string phoneISDCodeField;
        
        private string phoneAreaCodeField;
        
        private string phoneNumberField;
        
        private string enrollmentSourceField;
        
        private string preferredLanguageField;
        
        private CustomerDynamicAttribute[] customerDynamicAttributeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=0)]
        public string nomineeReferenceNumber {
            get {
                return this.nomineeReferenceNumberField;
            }
            set {
                this.nomineeReferenceNumberField = value;
                this.RaisePropertyChanged("nomineeReferenceNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string memberType {
            get {
                return this.memberTypeField;
            }
            set {
                this.memberTypeField = value;
                this.RaisePropertyChanged("memberType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=2)]
        public string customerNumber {
            get {
                return this.customerNumberField;
            }
            set {
                this.customerNumberField = value;
                this.RaisePropertyChanged("customerNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=3)]
        public string accountGroupType {
            get {
                return this.accountGroupTypeField;
            }
            set {
                this.accountGroupTypeField = value;
                this.RaisePropertyChanged("accountGroupType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=4)]
        public string membershipNumber {
            get {
                return this.membershipNumberField;
            }
            set {
                this.membershipNumberField = value;
                this.RaisePropertyChanged("membershipNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=5)]
        public string operationFlag {
            get {
                return this.operationFlagField;
            }
            set {
                this.operationFlagField = value;
                this.RaisePropertyChanged("operationFlag");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=6)]
        public string relationship {
            get {
                return this.relationshipField;
            }
            set {
                this.relationshipField = value;
                this.RaisePropertyChanged("relationship");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=7)]
        public string title {
            get {
                return this.titleField;
            }
            set {
                this.titleField = value;
                this.RaisePropertyChanged("title");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=8)]
        public string givenName {
            get {
                return this.givenNameField;
            }
            set {
                this.givenNameField = value;
                this.RaisePropertyChanged("givenName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=9)]
        public string secondName {
            get {
                return this.secondNameField;
            }
            set {
                this.secondNameField = value;
                this.RaisePropertyChanged("secondName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=10)]
        public string familyName {
            get {
                return this.familyNameField;
            }
            set {
                this.familyNameField = value;
                this.RaisePropertyChanged("familyName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=11)]
        public string displayName {
            get {
                return this.displayNameField;
            }
            set {
                this.displayNameField = value;
                this.RaisePropertyChanged("displayName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=12)]
        public string gender {
            get {
                return this.genderField;
            }
            set {
                this.genderField = value;
                this.RaisePropertyChanged("gender");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=13)]
        public string dateOfBirth {
            get {
                return this.dateOfBirthField;
            }
            set {
                this.dateOfBirthField = value;
                this.RaisePropertyChanged("dateOfBirth");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=14)]
        public string passportNumber {
            get {
                return this.passportNumberField;
            }
            set {
                this.passportNumberField = value;
                this.RaisePropertyChanged("passportNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=15)]
        public string countryOfResidence {
            get {
                return this.countryOfResidenceField;
            }
            set {
                this.countryOfResidenceField = value;
                this.RaisePropertyChanged("countryOfResidence");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=16)]
        public string emailAddress {
            get {
                return this.emailAddressField;
            }
            set {
                this.emailAddressField = value;
                this.RaisePropertyChanged("emailAddress");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=17)]
        public string phoneISDCode {
            get {
                return this.phoneISDCodeField;
            }
            set {
                this.phoneISDCodeField = value;
                this.RaisePropertyChanged("phoneISDCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=18)]
        public string phoneAreaCode {
            get {
                return this.phoneAreaCodeField;
            }
            set {
                this.phoneAreaCodeField = value;
                this.RaisePropertyChanged("phoneAreaCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=19)]
        public string phoneNumber {
            get {
                return this.phoneNumberField;
            }
            set {
                this.phoneNumberField = value;
                this.RaisePropertyChanged("phoneNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=20)]
        public string enrollmentSource {
            get {
                return this.enrollmentSourceField;
            }
            set {
                this.enrollmentSourceField = value;
                this.RaisePropertyChanged("enrollmentSource");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=21)]
        public string preferredLanguage {
            get {
                return this.preferredLanguageField;
            }
            set {
                this.preferredLanguageField = value;
                this.RaisePropertyChanged("preferredLanguage");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("customerDynamicAttribute", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=22)]
        public CustomerDynamicAttribute[] customerDynamicAttribute {
            get {
                return this.customerDynamicAttributeField;
            }
            set {
                this.customerDynamicAttributeField = value;
                this.RaisePropertyChanged("customerDynamicAttribute");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iloyal/member/addaccountnominee/type/")]
    public partial class GroupTypeCounts : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string accountGroupTypeField;
        
        private int countField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=0)]
        public string accountGroupType {
            get {
                return this.accountGroupTypeField;
            }
            set {
                this.accountGroupTypeField = value;
                this.RaisePropertyChanged("accountGroupType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public int count {
            get {
                return this.countField;
            }
            set {
                this.countField = value;
                this.RaisePropertyChanged("count");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "4.0.0.0")]
    [System.ServiceModel.ServiceContractAttribute(Namespace="http://www.ibsplc.com/iloyal/member/addaccountnominee/wsdl", ConfigurationName="AddAccountNomineeService.AddAccountNominee")]
    public interface AddAccountNominee {
        
        // CODEGEN: Generating message contract since the operation addAccountNominee is neither RPC nor document wrapped.
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Ubimecs.IBS.Services.AddAccountNomineeService.WebServiceException), Action="", Name="MemberWebServiceException", Namespace="http://www.ibsplc.com/iloyal/member/addaccountnominee/type/")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        Ubimecs.IBS.Services.AddAccountNomineeService.AddAccountNomineeResponse1 addAccountNominee(Ubimecs.IBS.Services.AddAccountNomineeService.AddAccountNomineeRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.AddAccountNomineeService.AddAccountNomineeResponse1> addAccountNomineeAsync(Ubimecs.IBS.Services.AddAccountNomineeService.AddAccountNomineeRequest request);
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iloyal/member/addaccountnominee/type/")]
    public partial class AccountNominee : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string companyCodeField;
        
        private string programCodeField;
        
        private string accountStatusField;
        
        private string membershipNumberField;
        
        private string membershipTypeField;
        
        private GroupTypeCounts[] groupTypeCountsField;
        
        private string preferredLanguageField;
        
        private string operationFlagField;
        
        private AccountNomineeDetail[] nomineeDetailsField;
        
        private WebServiceTransactionHeader txnHeaderField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string companyCode {
            get {
                return this.companyCodeField;
            }
            set {
                this.companyCodeField = value;
                this.RaisePropertyChanged("companyCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string programCode {
            get {
                return this.programCodeField;
            }
            set {
                this.programCodeField = value;
                this.RaisePropertyChanged("programCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=2)]
        public string accountStatus {
            get {
                return this.accountStatusField;
            }
            set {
                this.accountStatusField = value;
                this.RaisePropertyChanged("accountStatus");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string membershipNumber {
            get {
                return this.membershipNumberField;
            }
            set {
                this.membershipNumberField = value;
                this.RaisePropertyChanged("membershipNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string membershipType {
            get {
                return this.membershipTypeField;
            }
            set {
                this.membershipTypeField = value;
                this.RaisePropertyChanged("membershipType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("groupTypeCounts", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public GroupTypeCounts[] groupTypeCounts {
            get {
                return this.groupTypeCountsField;
            }
            set {
                this.groupTypeCountsField = value;
                this.RaisePropertyChanged("groupTypeCounts");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=6)]
        public string preferredLanguage {
            get {
                return this.preferredLanguageField;
            }
            set {
                this.preferredLanguageField = value;
                this.RaisePropertyChanged("preferredLanguage");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=7)]
        public string operationFlag {
            get {
                return this.operationFlagField;
            }
            set {
                this.operationFlagField = value;
                this.RaisePropertyChanged("operationFlag");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("nomineeDetails", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=8)]
        public AccountNomineeDetail[] nomineeDetails {
            get {
                return this.nomineeDetailsField;
            }
            set {
                this.nomineeDetailsField = value;
                this.RaisePropertyChanged("nomineeDetails");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public WebServiceTransactionHeader txnHeader {
            get {
                return this.txnHeaderField;
            }
            set {
                this.txnHeaderField = value;
                this.RaisePropertyChanged("txnHeader");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iloyal/member/addaccountnominee/type/")]
    public partial class AddAccountNomineeResponse : object, System.ComponentModel.INotifyPropertyChanged {
        
        private MemberActivityStatus memberActivityStatusField;
        
        private WebServiceTransactionHeader txnHeaderField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=true, Order=0)]
        public MemberActivityStatus MemberActivityStatus {
            get {
                return this.memberActivityStatusField;
            }
            set {
                this.memberActivityStatusField = value;
                this.RaisePropertyChanged("MemberActivityStatus");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public WebServiceTransactionHeader txnHeader {
            get {
                return this.txnHeaderField;
            }
            set {
                this.txnHeaderField = value;
                this.RaisePropertyChanged("txnHeader");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "4.0.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class AddAccountNomineeRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iloyal/member/addaccountnominee/type/", Order=0)]
        public Ubimecs.IBS.Services.AddAccountNomineeService.AccountNominee AccountNominee;
        
        public AddAccountNomineeRequest() {
        }
        
        public AddAccountNomineeRequest(Ubimecs.IBS.Services.AddAccountNomineeService.AccountNominee AccountNominee) {
            this.AccountNominee = AccountNominee;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "4.0.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class AddAccountNomineeResponse1 {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iloyal/member/addaccountnominee/type/", Order=0)]
        public Ubimecs.IBS.Services.AddAccountNomineeService.AddAccountNomineeResponse AddAccountNomineeResponse;
        
        public AddAccountNomineeResponse1() {
        }
        
        public AddAccountNomineeResponse1(Ubimecs.IBS.Services.AddAccountNomineeService.AddAccountNomineeResponse AddAccountNomineeResponse) {
            this.AddAccountNomineeResponse = AddAccountNomineeResponse;
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "4.0.0.0")]
    public interface AddAccountNomineeChannel : Ubimecs.IBS.Services.AddAccountNomineeService.AddAccountNominee, System.ServiceModel.IClientChannel {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "4.0.0.0")]
    public partial class AddAccountNomineeClient : System.ServiceModel.ClientBase<Ubimecs.IBS.Services.AddAccountNomineeService.AddAccountNominee>, Ubimecs.IBS.Services.AddAccountNomineeService.AddAccountNominee {
        
        public AddAccountNomineeClient() {
        }
        
        public AddAccountNomineeClient(string endpointConfigurationName) : 
                base(endpointConfigurationName) {
        }
        
        public AddAccountNomineeClient(string endpointConfigurationName, string remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public AddAccountNomineeClient(string endpointConfigurationName, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public AddAccountNomineeClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress) {
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Ubimecs.IBS.Services.AddAccountNomineeService.AddAccountNomineeResponse1 Ubimecs.IBS.Services.AddAccountNomineeService.AddAccountNominee.addAccountNominee(Ubimecs.IBS.Services.AddAccountNomineeService.AddAccountNomineeRequest request) {
            return base.Channel.addAccountNominee(request);
        }
        
        public Ubimecs.IBS.Services.AddAccountNomineeService.AddAccountNomineeResponse addAccountNominee(Ubimecs.IBS.Services.AddAccountNomineeService.AccountNominee AccountNominee) {
            Ubimecs.IBS.Services.AddAccountNomineeService.AddAccountNomineeRequest inValue = new Ubimecs.IBS.Services.AddAccountNomineeService.AddAccountNomineeRequest();
            inValue.AccountNominee = AccountNominee;
            Ubimecs.IBS.Services.AddAccountNomineeService.AddAccountNomineeResponse1 retVal = ((Ubimecs.IBS.Services.AddAccountNomineeService.AddAccountNominee)(this)).addAccountNominee(inValue);
            return retVal.AddAccountNomineeResponse;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.AddAccountNomineeService.AddAccountNomineeResponse1> Ubimecs.IBS.Services.AddAccountNomineeService.AddAccountNominee.addAccountNomineeAsync(Ubimecs.IBS.Services.AddAccountNomineeService.AddAccountNomineeRequest request) {
            return base.Channel.addAccountNomineeAsync(request);
        }
        
        public System.Threading.Tasks.Task<Ubimecs.IBS.Services.AddAccountNomineeService.AddAccountNomineeResponse1> addAccountNomineeAsync(Ubimecs.IBS.Services.AddAccountNomineeService.AccountNominee AccountNominee) {
            Ubimecs.IBS.Services.AddAccountNomineeService.AddAccountNomineeRequest inValue = new Ubimecs.IBS.Services.AddAccountNomineeService.AddAccountNomineeRequest();
            inValue.AccountNominee = AccountNominee;
            return ((Ubimecs.IBS.Services.AddAccountNomineeService.AddAccountNominee)(this)).addAccountNomineeAsync(inValue);
        }
    }
}
