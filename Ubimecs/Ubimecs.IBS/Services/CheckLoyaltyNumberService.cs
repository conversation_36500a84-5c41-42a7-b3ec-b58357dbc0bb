//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Ubimecs.IBS.Services.CheckLoyaltyNumberService {
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.ServiceContractAttribute(Namespace="http://www.ibsplc.com/wsdl", ConfigurationName="CheckLoyaltyNumberService.CustomerProfilePort")]
    public interface CustomerProfilePort {
        
        // CODEGEN: Generating message contract since the operation createCustomerProfile is neither RPC nor document wrapped.
        [System.ServiceModel.OperationContractAttribute(Action="urn:#createCustomerProfile", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        Ubimecs.IBS.Services.CheckLoyaltyNumberService.createCustomerProfileResponse createCustomerProfile(Ubimecs.IBS.Services.CheckLoyaltyNumberService.createCustomerProfileRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:#createCustomerProfile", ReplyAction="*")]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.CheckLoyaltyNumberService.createCustomerProfileResponse> createCustomerProfileAsync(Ubimecs.IBS.Services.CheckLoyaltyNumberService.createCustomerProfileRequest request);
        
        // CODEGEN: Generating message contract since the operation retrieveCustomerBookedList is neither RPC nor document wrapped.
        [System.ServiceModel.OperationContractAttribute(Action="urn:#retrieveCustomerBookedList", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        Ubimecs.IBS.Services.CheckLoyaltyNumberService.retrieveCustomerBookedListResponse retrieveCustomerBookedList(Ubimecs.IBS.Services.CheckLoyaltyNumberService.retrieveCustomerBookedListRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:#retrieveCustomerBookedList", ReplyAction="*")]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.CheckLoyaltyNumberService.retrieveCustomerBookedListResponse> retrieveCustomerBookedListAsync(Ubimecs.IBS.Services.CheckLoyaltyNumberService.retrieveCustomerBookedListRequest request);
        
        // CODEGEN: Generating message contract since the operation deactivateCustomerProfile is neither RPC nor document wrapped.
        [System.ServiceModel.OperationContractAttribute(Action="urn:#deactivateCustomerProfile", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        Ubimecs.IBS.Services.CheckLoyaltyNumberService.deactivateCustomerProfileResponse deactivateCustomerProfile(Ubimecs.IBS.Services.CheckLoyaltyNumberService.deactivateCustomerProfileRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:#deactivateCustomerProfile", ReplyAction="*")]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.CheckLoyaltyNumberService.deactivateCustomerProfileResponse> deactivateCustomerProfileAsync(Ubimecs.IBS.Services.CheckLoyaltyNumberService.deactivateCustomerProfileRequest request);
        
        // CODEGEN: Generating message contract since the operation getNewsLetterSubscriptionInfo is neither RPC nor document wrapped.
        [System.ServiceModel.OperationContractAttribute(Action="urn:#getNewsLetterSubscriptionInfo", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        Ubimecs.IBS.Services.CheckLoyaltyNumberService.getNewsLetterSubscriptionInfoResponse getNewsLetterSubscriptionInfo(Ubimecs.IBS.Services.CheckLoyaltyNumberService.getNewsLetterSubscriptionInfoRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:#getNewsLetterSubscriptionInfo", ReplyAction="*")]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.CheckLoyaltyNumberService.getNewsLetterSubscriptionInfoResponse> getNewsLetterSubscriptionInfoAsync(Ubimecs.IBS.Services.CheckLoyaltyNumberService.getNewsLetterSubscriptionInfoRequest request);
        
        // CODEGEN: Generating message contract since the operation listCustomerProfile is neither RPC nor document wrapped.
        [System.ServiceModel.OperationContractAttribute(Action="urn:#listCustomerProfile", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        Ubimecs.IBS.Services.CheckLoyaltyNumberService.listCustomerProfileResponse listCustomerProfile(Ubimecs.IBS.Services.CheckLoyaltyNumberService.listCustomerProfileRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:#listCustomerProfile", ReplyAction="*")]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.CheckLoyaltyNumberService.listCustomerProfileResponse> listCustomerProfileAsync(Ubimecs.IBS.Services.CheckLoyaltyNumberService.listCustomerProfileRequest request);
        
        // CODEGEN: Generating message contract since the operation modifyCustomerProfile is neither RPC nor document wrapped.
        [System.ServiceModel.OperationContractAttribute(Action="urn:#modifyCustomerProfile", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        Ubimecs.IBS.Services.CheckLoyaltyNumberService.modifyCustomerProfileResponse modifyCustomerProfile(Ubimecs.IBS.Services.CheckLoyaltyNumberService.modifyCustomerProfileRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:#modifyCustomerProfile", ReplyAction="*")]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.CheckLoyaltyNumberService.modifyCustomerProfileResponse> modifyCustomerProfileAsync(Ubimecs.IBS.Services.CheckLoyaltyNumberService.modifyCustomerProfileRequest request);
        
        // CODEGEN: Generating message contract since the operation resetLoyaltyPassword is neither RPC nor document wrapped.
        [System.ServiceModel.OperationContractAttribute(Action="urn:#resetLoyaltyPassword", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        Ubimecs.IBS.Services.CheckLoyaltyNumberService.resetLoyaltyPasswordResponse resetLoyaltyPassword(Ubimecs.IBS.Services.CheckLoyaltyNumberService.resetLoyaltyPasswordRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:#resetLoyaltyPassword", ReplyAction="*")]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.CheckLoyaltyNumberService.resetLoyaltyPasswordResponse> resetLoyaltyPasswordAsync(Ubimecs.IBS.Services.CheckLoyaltyNumberService.resetLoyaltyPasswordRequest request);
        
        // CODEGEN: Generating message contract since the operation setLoyaltyPassword is neither RPC nor document wrapped.
        [System.ServiceModel.OperationContractAttribute(Action="urn:#setLoyaltyPassword", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        Ubimecs.IBS.Services.CheckLoyaltyNumberService.setLoyaltyPasswordResponse setLoyaltyPassword(Ubimecs.IBS.Services.CheckLoyaltyNumberService.setLoyaltyPasswordRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:#setLoyaltyPassword", ReplyAction="*")]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.CheckLoyaltyNumberService.setLoyaltyPasswordResponse> setLoyaltyPasswordAsync(Ubimecs.IBS.Services.CheckLoyaltyNumberService.setLoyaltyPasswordRequest request);
        
        // CODEGEN: Generating message contract since the operation subscribeAirlineNewsletter is neither RPC nor document wrapped.
        [System.ServiceModel.OperationContractAttribute(Action="urn:#subscribeAirlineNewsletter", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        Ubimecs.IBS.Services.CheckLoyaltyNumberService.subscribeAirlineNewsletterResponse subscribeAirlineNewsletter(Ubimecs.IBS.Services.CheckLoyaltyNumberService.subscribeAirlineNewsletterRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:#subscribeAirlineNewsletter", ReplyAction="*")]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.CheckLoyaltyNumberService.subscribeAirlineNewsletterResponse> subscribeAirlineNewsletterAsync(Ubimecs.IBS.Services.CheckLoyaltyNumberService.subscribeAirlineNewsletterRequest request);
        
        // CODEGEN: Generating message contract since the operation validateLoyalty is neither RPC nor document wrapped.
        [System.ServiceModel.OperationContractAttribute(Action="urn:#validateLoyalty", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        Ubimecs.IBS.Services.CheckLoyaltyNumberService.validateLoyaltyResponse validateLoyalty(Ubimecs.IBS.Services.CheckLoyaltyNumberService.validateLoyaltyRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:#validateLoyalty", ReplyAction="*")]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.CheckLoyaltyNumberService.validateLoyaltyResponse> validateLoyaltyAsync(Ubimecs.IBS.Services.CheckLoyaltyNumberService.validateLoyaltyRequest request);
        
        // CODEGEN: Generating message contract since the operation viewCustomerProfile is neither RPC nor document wrapped.
        [System.ServiceModel.OperationContractAttribute(Action="urn:#viewCustomerProfile", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        Ubimecs.IBS.Services.CheckLoyaltyNumberService.viewCustomerProfileResponse viewCustomerProfile(Ubimecs.IBS.Services.CheckLoyaltyNumberService.viewCustomerProfileRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:#viewCustomerProfile", ReplyAction="*")]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.CheckLoyaltyNumberService.viewCustomerProfileResponse> viewCustomerProfileAsync(Ubimecs.IBS.Services.CheckLoyaltyNumberService.viewCustomerProfileRequest request);
        
        // CODEGEN: Generating message contract since the operation viewCustomerProfileViaWeb is neither RPC nor document wrapped.
        [System.ServiceModel.OperationContractAttribute(Action="urn:#viewCustomerProfileViaWeb", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        Ubimecs.IBS.Services.CheckLoyaltyNumberService.viewCustomerProfileViaWebResponse viewCustomerProfileViaWeb(Ubimecs.IBS.Services.CheckLoyaltyNumberService.viewCustomerProfileViaWebRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:#viewCustomerProfileViaWeb", ReplyAction="*")]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.CheckLoyaltyNumberService.viewCustomerProfileViaWebResponse> viewCustomerProfileViaWebAsync(Ubimecs.IBS.Services.CheckLoyaltyNumberService.viewCustomerProfileViaWebRequest request);
        
        // CODEGEN: Generating message contract since the operation validateDuplicateCustomerProfile is neither RPC nor document wrapped.
        [System.ServiceModel.OperationContractAttribute(Action="urn:#validateDuplicateCustomerProfile", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        Ubimecs.IBS.Services.CheckLoyaltyNumberService.validateDuplicateCustomerProfileResponse validateDuplicateCustomerProfile(Ubimecs.IBS.Services.CheckLoyaltyNumberService.validateDuplicateCustomerProfileRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:#validateDuplicateCustomerProfile", ReplyAction="*")]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.CheckLoyaltyNumberService.validateDuplicateCustomerProfileResponse> validateDuplicateCustomerProfileAsync(Ubimecs.IBS.Services.CheckLoyaltyNumberService.validateDuplicateCustomerProfileRequest request);
        
        // CODEGEN: Generating message contract since the operation validateCustomerProfileList is neither RPC nor document wrapped.
        [System.ServiceModel.OperationContractAttribute(Action="urn:#validateCustomerProfileList", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        Ubimecs.IBS.Services.CheckLoyaltyNumberService.validateCustomerProfileListResponse validateCustomerProfileList(Ubimecs.IBS.Services.CheckLoyaltyNumberService.validateCustomerProfileListRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:#validateCustomerProfileList", ReplyAction="*")]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.CheckLoyaltyNumberService.validateCustomerProfileListResponse> validateCustomerProfileListAsync(Ubimecs.IBS.Services.CheckLoyaltyNumberService.validateCustomerProfileListRequest request);
        
        // CODEGEN: Generating message contract since the operation activateCustomerProfileLogin is neither RPC nor document wrapped.
        [System.ServiceModel.OperationContractAttribute(Action="urn:#activateCustomerProfileLogin", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        Ubimecs.IBS.Services.CheckLoyaltyNumberService.activateCustomerProfileLoginResponse activateCustomerProfileLogin(Ubimecs.IBS.Services.CheckLoyaltyNumberService.activateCustomerProfileLoginRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:#activateCustomerProfileLogin", ReplyAction="*")]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.CheckLoyaltyNumberService.activateCustomerProfileLoginResponse> activateCustomerProfileLoginAsync(Ubimecs.IBS.Services.CheckLoyaltyNumberService.activateCustomerProfileLoginRequest request);
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class CreateCustomerProfileRQ : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string airlineCodeField;
        
        private BookingChannelKeyType bookingChannelField;
        
        private string profileAliasField;
        
        private string profileTypeField;
        
        private LoginDetails loginDetailsField;
        
        private ProviderDetails providerDetailsField;
        
        private GuestDetails[] guestDetailsField;
        
        private CustomerProfileContactDetails homeContactField;
        
        private CustomerProfileContactDetails businessContactField;
        
        private CustomerProfileContactDetails alternateContactField;
        
        private string viewModeField;
        
        private FeeWaiveOrOverrideDetails[] feeWaiveOrOverrideDetailsField;
        
        private string externalProfileIDField;
        
        private bool canBypassDupeCheckField;
        
        private bool canBypassDupeCheckFieldSpecified;
        
        private CustomerProfileContactDetails localLanguageContactField;
        
        private CustomerProfileContactDetails[] paxSpecificContactsField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AirlineCode {
            get {
                return this.airlineCodeField;
            }
            set {
                this.airlineCodeField = value;
                this.RaisePropertyChanged("AirlineCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public BookingChannelKeyType BookingChannel {
            get {
                return this.bookingChannelField;
            }
            set {
                this.bookingChannelField = value;
                this.RaisePropertyChanged("BookingChannel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string ProfileAlias {
            get {
                return this.profileAliasField;
            }
            set {
                this.profileAliasField = value;
                this.RaisePropertyChanged("ProfileAlias");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string ProfileType {
            get {
                return this.profileTypeField;
            }
            set {
                this.profileTypeField = value;
                this.RaisePropertyChanged("ProfileType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public LoginDetails LoginDetails {
            get {
                return this.loginDetailsField;
            }
            set {
                this.loginDetailsField = value;
                this.RaisePropertyChanged("LoginDetails");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public ProviderDetails ProviderDetails {
            get {
                return this.providerDetailsField;
            }
            set {
                this.providerDetailsField = value;
                this.RaisePropertyChanged("ProviderDetails");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("GuestDetails", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public GuestDetails[] GuestDetails {
            get {
                return this.guestDetailsField;
            }
            set {
                this.guestDetailsField = value;
                this.RaisePropertyChanged("GuestDetails");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public CustomerProfileContactDetails HomeContact {
            get {
                return this.homeContactField;
            }
            set {
                this.homeContactField = value;
                this.RaisePropertyChanged("HomeContact");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public CustomerProfileContactDetails BusinessContact {
            get {
                return this.businessContactField;
            }
            set {
                this.businessContactField = value;
                this.RaisePropertyChanged("BusinessContact");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public CustomerProfileContactDetails AlternateContact {
            get {
                return this.alternateContactField;
            }
            set {
                this.alternateContactField = value;
                this.RaisePropertyChanged("AlternateContact");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=10)]
        public string ViewMode {
            get {
                return this.viewModeField;
            }
            set {
                this.viewModeField = value;
                this.RaisePropertyChanged("ViewMode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("FeeWaiveOrOverrideDetails", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=11)]
        public FeeWaiveOrOverrideDetails[] FeeWaiveOrOverrideDetails {
            get {
                return this.feeWaiveOrOverrideDetailsField;
            }
            set {
                this.feeWaiveOrOverrideDetailsField = value;
                this.RaisePropertyChanged("FeeWaiveOrOverrideDetails");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=12)]
        public string ExternalProfileID {
            get {
                return this.externalProfileIDField;
            }
            set {
                this.externalProfileIDField = value;
                this.RaisePropertyChanged("ExternalProfileID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=13)]
        public bool CanBypassDupeCheck {
            get {
                return this.canBypassDupeCheckField;
            }
            set {
                this.canBypassDupeCheckField = value;
                this.RaisePropertyChanged("CanBypassDupeCheck");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool CanBypassDupeCheckSpecified {
            get {
                return this.canBypassDupeCheckFieldSpecified;
            }
            set {
                this.canBypassDupeCheckFieldSpecified = value;
                this.RaisePropertyChanged("CanBypassDupeCheckSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=14)]
        public CustomerProfileContactDetails LocalLanguageContact {
            get {
                return this.localLanguageContactField;
            }
            set {
                this.localLanguageContactField = value;
                this.RaisePropertyChanged("LocalLanguageContact");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("PaxSpecificContacts", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=15)]
        public CustomerProfileContactDetails[] PaxSpecificContacts {
            get {
                return this.paxSpecificContactsField;
            }
            set {
                this.paxSpecificContactsField = value;
                this.RaisePropertyChanged("PaxSpecificContacts");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class BookingChannelKeyType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string channelTypeField;
        
        private string channelField;
        
        private string localeField;
        
        private string sessionIdField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string ChannelType {
            get {
                return this.channelTypeField;
            }
            set {
                this.channelTypeField = value;
                this.RaisePropertyChanged("ChannelType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string Channel {
            get {
                return this.channelField;
            }
            set {
                this.channelField = value;
                this.RaisePropertyChanged("Channel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string Locale {
            get {
                return this.localeField;
            }
            set {
                this.localeField = value;
                this.RaisePropertyChanged("Locale");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string SessionId {
            get {
                return this.sessionIdField;
            }
            set {
                this.sessionIdField = value;
                this.RaisePropertyChanged("SessionId");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class CustomerProfileValidateResp : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string firstNameField;
        
        private string lastNameField;
        
        private IdDetailsType idDetailsField;
        
        private bool isValidField;
        
        private bool isValidFieldSpecified;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string FirstName {
            get {
                return this.firstNameField;
            }
            set {
                this.firstNameField = value;
                this.RaisePropertyChanged("FirstName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string LastName {
            get {
                return this.lastNameField;
            }
            set {
                this.lastNameField = value;
                this.RaisePropertyChanged("LastName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public IdDetailsType IdDetails {
            get {
                return this.idDetailsField;
            }
            set {
                this.idDetailsField = value;
                this.RaisePropertyChanged("IdDetails");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public bool isValid {
            get {
                return this.isValidField;
            }
            set {
                this.isValidField = value;
                this.RaisePropertyChanged("isValid");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool isValidSpecified {
            get {
                return this.isValidFieldSpecified;
            }
            set {
                this.isValidFieldSpecified = value;
                this.RaisePropertyChanged("isValidSpecified");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class IdDetailsType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string itemField;
        
        private ItemChoiceType itemElementNameField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("CustomerId", typeof(string), Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        [System.Xml.Serialization.XmlElementAttribute("LoyaltyNumber", typeof(string), Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        [System.Xml.Serialization.XmlElementAttribute("ProfileId", typeof(string), Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        [System.Xml.Serialization.XmlChoiceIdentifierAttribute("ItemElementName")]
        public string Item {
            get {
                return this.itemField;
            }
            set {
                this.itemField = value;
                this.RaisePropertyChanged("Item");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public ItemChoiceType ItemElementName {
            get {
                return this.itemElementNameField;
            }
            set {
                this.itemElementNameField = value;
                this.RaisePropertyChanged("ItemElementName");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", IncludeInSchema=false)]
    public enum ItemChoiceType {
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute(":CustomerId")]
        CustomerId,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute(":LoyaltyNumber")]
        LoyaltyNumber,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute(":ProfileId")]
        ProfileId,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class CustomerProfileDetailsType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string firstNameField;
        
        private string lastNameField;
        
        private IdDetailsType idDetailsField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string FirstName {
            get {
                return this.firstNameField;
            }
            set {
                this.firstNameField = value;
                this.RaisePropertyChanged("FirstName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string LastName {
            get {
                return this.lastNameField;
            }
            set {
                this.lastNameField = value;
                this.RaisePropertyChanged("LastName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public IdDetailsType IdDetails {
            get {
                return this.idDetailsField;
            }
            set {
                this.idDetailsField = value;
                this.RaisePropertyChanged("IdDetails");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class DupeProfileDetails : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string profileIdField;
        
        private string firstNameField;
        
        private string lastNameField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string ProfileId {
            get {
                return this.profileIdField;
            }
            set {
                this.profileIdField = value;
                this.RaisePropertyChanged("ProfileId");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string FirstName {
            get {
                return this.firstNameField;
            }
            set {
                this.firstNameField = value;
                this.RaisePropertyChanged("FirstName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string LastName {
            get {
                return this.lastNameField;
            }
            set {
                this.lastNameField = value;
                this.RaisePropertyChanged("LastName");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class ViewCustomerProfileDetails : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string profileAliasField;
        
        private string groupNameField;
        
        private string groupTypeField;
        
        private string groupLeaderFirstNameField;
        
        private string groupLeaderLastNameField;
        
        private string profileTypeField;
        
        private LoginDetails loginDetailsField;
        
        private SecurityInfoType securityInfoField;
        
        private GuestDetails[] guestDetailsField;
        
        private CustomerProfileContactDetails homeContactField;
        
        private CustomerProfileContactDetails businessContactField;
        
        private CustomerProfileContactDetails alternateContactField;
        
        private CustomerProfileContactDetails localLanguageContactField;
        
        private CustomerProfileContactDetails[] paxSpecificContactsField;
        
        private string viewModeField;
        
        private FeeWaiveOrOverrideDetails[] feeWaiveOrOverrideDetailsField;
        
        private string externalProfileIDField;
        
        private System.DateTime lastSyncTimeField;
        
        private ErrorType errorTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string ProfileAlias {
            get {
                return this.profileAliasField;
            }
            set {
                this.profileAliasField = value;
                this.RaisePropertyChanged("ProfileAlias");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string GroupName {
            get {
                return this.groupNameField;
            }
            set {
                this.groupNameField = value;
                this.RaisePropertyChanged("GroupName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string GroupType {
            get {
                return this.groupTypeField;
            }
            set {
                this.groupTypeField = value;
                this.RaisePropertyChanged("GroupType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string GroupLeaderFirstName {
            get {
                return this.groupLeaderFirstNameField;
            }
            set {
                this.groupLeaderFirstNameField = value;
                this.RaisePropertyChanged("GroupLeaderFirstName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string GroupLeaderLastName {
            get {
                return this.groupLeaderLastNameField;
            }
            set {
                this.groupLeaderLastNameField = value;
                this.RaisePropertyChanged("GroupLeaderLastName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string ProfileType {
            get {
                return this.profileTypeField;
            }
            set {
                this.profileTypeField = value;
                this.RaisePropertyChanged("ProfileType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public LoginDetails LoginDetails {
            get {
                return this.loginDetailsField;
            }
            set {
                this.loginDetailsField = value;
                this.RaisePropertyChanged("LoginDetails");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public SecurityInfoType SecurityInfo {
            get {
                return this.securityInfoField;
            }
            set {
                this.securityInfoField = value;
                this.RaisePropertyChanged("SecurityInfo");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("GuestDetails", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public GuestDetails[] GuestDetails {
            get {
                return this.guestDetailsField;
            }
            set {
                this.guestDetailsField = value;
                this.RaisePropertyChanged("GuestDetails");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public CustomerProfileContactDetails HomeContact {
            get {
                return this.homeContactField;
            }
            set {
                this.homeContactField = value;
                this.RaisePropertyChanged("HomeContact");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=10)]
        public CustomerProfileContactDetails BusinessContact {
            get {
                return this.businessContactField;
            }
            set {
                this.businessContactField = value;
                this.RaisePropertyChanged("BusinessContact");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=11)]
        public CustomerProfileContactDetails AlternateContact {
            get {
                return this.alternateContactField;
            }
            set {
                this.alternateContactField = value;
                this.RaisePropertyChanged("AlternateContact");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=12)]
        public CustomerProfileContactDetails LocalLanguageContact {
            get {
                return this.localLanguageContactField;
            }
            set {
                this.localLanguageContactField = value;
                this.RaisePropertyChanged("LocalLanguageContact");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("PaxSpecificContacts", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=13)]
        public CustomerProfileContactDetails[] PaxSpecificContacts {
            get {
                return this.paxSpecificContactsField;
            }
            set {
                this.paxSpecificContactsField = value;
                this.RaisePropertyChanged("PaxSpecificContacts");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=14)]
        public string ViewMode {
            get {
                return this.viewModeField;
            }
            set {
                this.viewModeField = value;
                this.RaisePropertyChanged("ViewMode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("FeeWaiveOrOverrideDetails", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=15)]
        public FeeWaiveOrOverrideDetails[] FeeWaiveOrOverrideDetails {
            get {
                return this.feeWaiveOrOverrideDetailsField;
            }
            set {
                this.feeWaiveOrOverrideDetailsField = value;
                this.RaisePropertyChanged("FeeWaiveOrOverrideDetails");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=16)]
        public string ExternalProfileID {
            get {
                return this.externalProfileIDField;
            }
            set {
                this.externalProfileIDField = value;
                this.RaisePropertyChanged("ExternalProfileID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=17)]
        public System.DateTime LastSyncTime {
            get {
                return this.lastSyncTimeField;
            }
            set {
                this.lastSyncTimeField = value;
                this.RaisePropertyChanged("LastSyncTime");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=18)]
        public ErrorType ErrorType {
            get {
                return this.errorTypeField;
            }
            set {
                this.errorTypeField = value;
                this.RaisePropertyChanged("ErrorType");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class LoginDetails : object, System.ComponentModel.INotifyPropertyChanged {
        
        private UserInfo[] loginUserInfoField;
        
        private string passwordField;
        
        private SecurityInfoType securityInfoField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("LoginUserInfo", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public UserInfo[] LoginUserInfo {
            get {
                return this.loginUserInfoField;
            }
            set {
                this.loginUserInfoField = value;
                this.RaisePropertyChanged("LoginUserInfo");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string Password {
            get {
                return this.passwordField;
            }
            set {
                this.passwordField = value;
                this.RaisePropertyChanged("Password");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public SecurityInfoType SecurityInfo {
            get {
                return this.securityInfoField;
            }
            set {
                this.securityInfoField = value;
                this.RaisePropertyChanged("SecurityInfo");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class UserInfo : object, System.ComponentModel.INotifyPropertyChanged {
        
        private UserType loginUserTypeField;
        
        private bool loginUserTypeFieldSpecified;
        
        private string userNameField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public UserType LoginUserType {
            get {
                return this.loginUserTypeField;
            }
            set {
                this.loginUserTypeField = value;
                this.RaisePropertyChanged("LoginUserType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool LoginUserTypeSpecified {
            get {
                return this.loginUserTypeFieldSpecified;
            }
            set {
                this.loginUserTypeFieldSpecified = value;
                this.RaisePropertyChanged("LoginUserTypeSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string UserName {
            get {
                return this.userNameField;
            }
            set {
                this.userNameField = value;
                this.RaisePropertyChanged("UserName");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public enum UserType {
        
        /// <remarks/>
        OTHERS,
        
        /// <remarks/>
        EMAIL,
        
        /// <remarks/>
        LOYALTYNO,
        
        /// <remarks/>
        CUSTNO,
        
        /// <remarks/>
        PHONENO,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class SecurityInfoType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string securityQuestionField;
        
        private string securityAnswerField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string SecurityQuestion {
            get {
                return this.securityQuestionField;
            }
            set {
                this.securityQuestionField = value;
                this.RaisePropertyChanged("SecurityQuestion");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string SecurityAnswer {
            get {
                return this.securityAnswerField;
            }
            set {
                this.securityAnswerField = value;
                this.RaisePropertyChanged("SecurityAnswer");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class GuestDetails : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string lastNameField;
        
        private string localLastNameField;
        
        private string localLastName1Field;
        
        private string localLastName2Field;
        
        private string middleNameField;
        
        private string firstNameField;
        
        private string localFirstNameField;
        
        private string localFirstName1Field;
        
        private string localFirstName2Field;
        
        private LoyaltyNumberDetails loyaltyNumberDetailsField;
        
        private PartnerLoyaltyNumberDetails[] partnerLoyaltyNumberDetailsField;
        
        private string guestSalutaionField;
        
        private NamePrefixType titleField;
        
        private bool titleFieldSpecified;
        
        private System.DateTime dateOfBirthField;
        
        private bool dateOfBirthFieldSpecified;
        
        private GuestType guestTypeField;
        
        private GenderType genderField;
        
        private bool genderFieldSpecified;
        
        private string occupationField;
        
        private string languageField;
        
        private string mealSSRField;
        
        private string seatSSRField;
        
        private string otherSSRField;
        
        private TravelDocuments[] travelDocumentsField;
        
        private CreditCardDetails[] creditCardsField;
        
        private string profileGuestIdField;
        
        private CustomerValueDetails customerValueDetailsField;
        
        private CustomerProfileConsentDetails[] consentDetailsField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string LastName {
            get {
                return this.lastNameField;
            }
            set {
                this.lastNameField = value;
                this.RaisePropertyChanged("LastName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string LocalLastName {
            get {
                return this.localLastNameField;
            }
            set {
                this.localLastNameField = value;
                this.RaisePropertyChanged("LocalLastName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string LocalLastName1 {
            get {
                return this.localLastName1Field;
            }
            set {
                this.localLastName1Field = value;
                this.RaisePropertyChanged("LocalLastName1");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string LocalLastName2 {
            get {
                return this.localLastName2Field;
            }
            set {
                this.localLastName2Field = value;
                this.RaisePropertyChanged("LocalLastName2");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string MiddleName {
            get {
                return this.middleNameField;
            }
            set {
                this.middleNameField = value;
                this.RaisePropertyChanged("MiddleName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string FirstName {
            get {
                return this.firstNameField;
            }
            set {
                this.firstNameField = value;
                this.RaisePropertyChanged("FirstName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string LocalFirstName {
            get {
                return this.localFirstNameField;
            }
            set {
                this.localFirstNameField = value;
                this.RaisePropertyChanged("LocalFirstName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string LocalFirstName1 {
            get {
                return this.localFirstName1Field;
            }
            set {
                this.localFirstName1Field = value;
                this.RaisePropertyChanged("LocalFirstName1");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public string LocalFirstName2 {
            get {
                return this.localFirstName2Field;
            }
            set {
                this.localFirstName2Field = value;
                this.RaisePropertyChanged("LocalFirstName2");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public LoyaltyNumberDetails LoyaltyNumberDetails {
            get {
                return this.loyaltyNumberDetailsField;
            }
            set {
                this.loyaltyNumberDetailsField = value;
                this.RaisePropertyChanged("LoyaltyNumberDetails");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("PartnerLoyaltyNumberDetails", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=10)]
        public PartnerLoyaltyNumberDetails[] PartnerLoyaltyNumberDetails {
            get {
                return this.partnerLoyaltyNumberDetailsField;
            }
            set {
                this.partnerLoyaltyNumberDetailsField = value;
                this.RaisePropertyChanged("PartnerLoyaltyNumberDetails");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=11)]
        public string GuestSalutaion {
            get {
                return this.guestSalutaionField;
            }
            set {
                this.guestSalutaionField = value;
                this.RaisePropertyChanged("GuestSalutaion");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=12)]
        public NamePrefixType Title {
            get {
                return this.titleField;
            }
            set {
                this.titleField = value;
                this.RaisePropertyChanged("Title");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool TitleSpecified {
            get {
                return this.titleFieldSpecified;
            }
            set {
                this.titleFieldSpecified = value;
                this.RaisePropertyChanged("TitleSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, DataType="date", Order=13)]
        public System.DateTime DateOfBirth {
            get {
                return this.dateOfBirthField;
            }
            set {
                this.dateOfBirthField = value;
                this.RaisePropertyChanged("DateOfBirth");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool DateOfBirthSpecified {
            get {
                return this.dateOfBirthFieldSpecified;
            }
            set {
                this.dateOfBirthFieldSpecified = value;
                this.RaisePropertyChanged("DateOfBirthSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=14)]
        public GuestType GuestType {
            get {
                return this.guestTypeField;
            }
            set {
                this.guestTypeField = value;
                this.RaisePropertyChanged("GuestType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=15)]
        public GenderType Gender {
            get {
                return this.genderField;
            }
            set {
                this.genderField = value;
                this.RaisePropertyChanged("Gender");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool GenderSpecified {
            get {
                return this.genderFieldSpecified;
            }
            set {
                this.genderFieldSpecified = value;
                this.RaisePropertyChanged("GenderSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=16)]
        public string Occupation {
            get {
                return this.occupationField;
            }
            set {
                this.occupationField = value;
                this.RaisePropertyChanged("Occupation");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=17)]
        public string Language {
            get {
                return this.languageField;
            }
            set {
                this.languageField = value;
                this.RaisePropertyChanged("Language");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=18)]
        public string MealSSR {
            get {
                return this.mealSSRField;
            }
            set {
                this.mealSSRField = value;
                this.RaisePropertyChanged("MealSSR");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=19)]
        public string SeatSSR {
            get {
                return this.seatSSRField;
            }
            set {
                this.seatSSRField = value;
                this.RaisePropertyChanged("SeatSSR");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=20)]
        public string OtherSSR {
            get {
                return this.otherSSRField;
            }
            set {
                this.otherSSRField = value;
                this.RaisePropertyChanged("OtherSSR");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("TravelDocuments", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=21)]
        public TravelDocuments[] TravelDocuments {
            get {
                return this.travelDocumentsField;
            }
            set {
                this.travelDocumentsField = value;
                this.RaisePropertyChanged("TravelDocuments");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("CreditCards", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=22)]
        public CreditCardDetails[] CreditCards {
            get {
                return this.creditCardsField;
            }
            set {
                this.creditCardsField = value;
                this.RaisePropertyChanged("CreditCards");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=23)]
        public string ProfileGuestId {
            get {
                return this.profileGuestIdField;
            }
            set {
                this.profileGuestIdField = value;
                this.RaisePropertyChanged("ProfileGuestId");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=24)]
        public CustomerValueDetails CustomerValueDetails {
            get {
                return this.customerValueDetailsField;
            }
            set {
                this.customerValueDetailsField = value;
                this.RaisePropertyChanged("CustomerValueDetails");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("ConsentDetails", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=25)]
        public CustomerProfileConsentDetails[] ConsentDetails {
            get {
                return this.consentDetailsField;
            }
            set {
                this.consentDetailsField = value;
                this.RaisePropertyChanged("ConsentDetails");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class LoyaltyNumberDetails : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string airlineCodeField;
        
        private string tierLevelCodeField;
        
        private string tierLevelNameField;
        
        private long pointsField;
        
        private bool pointsFieldSpecified;
        
        private string accountStatusField;
        
        private CustomerLoyaltyDetails customerLoyaltyDetailsField;
        
        private string loyaltyNumberField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AirlineCode {
            get {
                return this.airlineCodeField;
            }
            set {
                this.airlineCodeField = value;
                this.RaisePropertyChanged("AirlineCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string TierLevelCode {
            get {
                return this.tierLevelCodeField;
            }
            set {
                this.tierLevelCodeField = value;
                this.RaisePropertyChanged("TierLevelCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string TierLevelName {
            get {
                return this.tierLevelNameField;
            }
            set {
                this.tierLevelNameField = value;
                this.RaisePropertyChanged("TierLevelName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public long Points {
            get {
                return this.pointsField;
            }
            set {
                this.pointsField = value;
                this.RaisePropertyChanged("Points");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool PointsSpecified {
            get {
                return this.pointsFieldSpecified;
            }
            set {
                this.pointsFieldSpecified = value;
                this.RaisePropertyChanged("PointsSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string AccountStatus {
            get {
                return this.accountStatusField;
            }
            set {
                this.accountStatusField = value;
                this.RaisePropertyChanged("AccountStatus");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public CustomerLoyaltyDetails CustomerLoyaltyDetails {
            get {
                return this.customerLoyaltyDetailsField;
            }
            set {
                this.customerLoyaltyDetailsField = value;
                this.RaisePropertyChanged("CustomerLoyaltyDetails");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string LoyaltyNumber {
            get {
                return this.loyaltyNumberField;
            }
            set {
                this.loyaltyNumberField = value;
                this.RaisePropertyChanged("LoyaltyNumber");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class CustomerLoyaltyDetails : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string pINField;
        
        private string securityQuestionField;
        
        private string securityAnswerField;
        
        private string currencyCodeField;
        
        private string activationCodeField;
        
        private string statementTypeField;
        
        private string promoTrackingNumberField;
        
        private string loyaltyRefererField;
        
        private int flightFrequencyDomesticField;
        
        private bool flightFrequencyDomesticFieldSpecified;
        
        private int flightFrequencyInternationalField;
        
        private bool flightFrequencyInternationalFieldSpecified;
        
        private int noOfTripsDomesticField;
        
        private bool noOfTripsDomesticFieldSpecified;
        
        private int noOfTripsInternationalField;
        
        private bool noOfTripsInternationalFieldSpecified;
        
        private string mostCommonDestinationField;
        
        private InterestDetails[] interestDetailsField;
        
        private string creditPasswordField;
        
        private AccountLockStatus accountLockStatusField;
        
        private bool accountLockStatusFieldSpecified;
        
        private System.DateTime createDateField;
        
        private bool createDateFieldSpecified;
        
        private System.DateTime activateDateField;
        
        private bool activateDateFieldSpecified;
        
        private System.DateTime updateDateField;
        
        private bool updateDateFieldSpecified;
        
        private System.DateTime lastLoginDateField;
        
        private bool lastLoginDateFieldSpecified;
        
        private int loginCountField;
        
        private bool loginCountFieldSpecified;
        
        private int loginFailCountField;
        
        private bool loginFailCountFieldSpecified;
        
        private bool allowMarketingField;
        
        private bool allowMarketingFieldSpecified;
        
        private bool allowSurveyField;
        
        private bool allowSurveyFieldSpecified;
        
        private bool australianTaxResidentField;
        
        private bool australianTaxResidentFieldSpecified;
        
        private string loginPasswordField;
        
        private string countryOfResidenceField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string PIN {
            get {
                return this.pINField;
            }
            set {
                this.pINField = value;
                this.RaisePropertyChanged("PIN");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string SecurityQuestion {
            get {
                return this.securityQuestionField;
            }
            set {
                this.securityQuestionField = value;
                this.RaisePropertyChanged("SecurityQuestion");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string SecurityAnswer {
            get {
                return this.securityAnswerField;
            }
            set {
                this.securityAnswerField = value;
                this.RaisePropertyChanged("SecurityAnswer");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string CurrencyCode {
            get {
                return this.currencyCodeField;
            }
            set {
                this.currencyCodeField = value;
                this.RaisePropertyChanged("CurrencyCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string ActivationCode {
            get {
                return this.activationCodeField;
            }
            set {
                this.activationCodeField = value;
                this.RaisePropertyChanged("ActivationCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string StatementType {
            get {
                return this.statementTypeField;
            }
            set {
                this.statementTypeField = value;
                this.RaisePropertyChanged("StatementType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string PromoTrackingNumber {
            get {
                return this.promoTrackingNumberField;
            }
            set {
                this.promoTrackingNumberField = value;
                this.RaisePropertyChanged("PromoTrackingNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string LoyaltyReferer {
            get {
                return this.loyaltyRefererField;
            }
            set {
                this.loyaltyRefererField = value;
                this.RaisePropertyChanged("LoyaltyReferer");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public int FlightFrequencyDomestic {
            get {
                return this.flightFrequencyDomesticField;
            }
            set {
                this.flightFrequencyDomesticField = value;
                this.RaisePropertyChanged("FlightFrequencyDomestic");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool FlightFrequencyDomesticSpecified {
            get {
                return this.flightFrequencyDomesticFieldSpecified;
            }
            set {
                this.flightFrequencyDomesticFieldSpecified = value;
                this.RaisePropertyChanged("FlightFrequencyDomesticSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public int FlightFrequencyInternational {
            get {
                return this.flightFrequencyInternationalField;
            }
            set {
                this.flightFrequencyInternationalField = value;
                this.RaisePropertyChanged("FlightFrequencyInternational");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool FlightFrequencyInternationalSpecified {
            get {
                return this.flightFrequencyInternationalFieldSpecified;
            }
            set {
                this.flightFrequencyInternationalFieldSpecified = value;
                this.RaisePropertyChanged("FlightFrequencyInternationalSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=10)]
        public int NoOfTripsDomestic {
            get {
                return this.noOfTripsDomesticField;
            }
            set {
                this.noOfTripsDomesticField = value;
                this.RaisePropertyChanged("NoOfTripsDomestic");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool NoOfTripsDomesticSpecified {
            get {
                return this.noOfTripsDomesticFieldSpecified;
            }
            set {
                this.noOfTripsDomesticFieldSpecified = value;
                this.RaisePropertyChanged("NoOfTripsDomesticSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=11)]
        public int NoOfTripsInternational {
            get {
                return this.noOfTripsInternationalField;
            }
            set {
                this.noOfTripsInternationalField = value;
                this.RaisePropertyChanged("NoOfTripsInternational");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool NoOfTripsInternationalSpecified {
            get {
                return this.noOfTripsInternationalFieldSpecified;
            }
            set {
                this.noOfTripsInternationalFieldSpecified = value;
                this.RaisePropertyChanged("NoOfTripsInternationalSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=12)]
        public string MostCommonDestination {
            get {
                return this.mostCommonDestinationField;
            }
            set {
                this.mostCommonDestinationField = value;
                this.RaisePropertyChanged("MostCommonDestination");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("InterestDetails", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=13)]
        public InterestDetails[] InterestDetails {
            get {
                return this.interestDetailsField;
            }
            set {
                this.interestDetailsField = value;
                this.RaisePropertyChanged("InterestDetails");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=14)]
        public string CreditPassword {
            get {
                return this.creditPasswordField;
            }
            set {
                this.creditPasswordField = value;
                this.RaisePropertyChanged("CreditPassword");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=15)]
        public AccountLockStatus AccountLockStatus {
            get {
                return this.accountLockStatusField;
            }
            set {
                this.accountLockStatusField = value;
                this.RaisePropertyChanged("AccountLockStatus");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool AccountLockStatusSpecified {
            get {
                return this.accountLockStatusFieldSpecified;
            }
            set {
                this.accountLockStatusFieldSpecified = value;
                this.RaisePropertyChanged("AccountLockStatusSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=16)]
        public System.DateTime CreateDate {
            get {
                return this.createDateField;
            }
            set {
                this.createDateField = value;
                this.RaisePropertyChanged("CreateDate");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool CreateDateSpecified {
            get {
                return this.createDateFieldSpecified;
            }
            set {
                this.createDateFieldSpecified = value;
                this.RaisePropertyChanged("CreateDateSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=17)]
        public System.DateTime ActivateDate {
            get {
                return this.activateDateField;
            }
            set {
                this.activateDateField = value;
                this.RaisePropertyChanged("ActivateDate");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool ActivateDateSpecified {
            get {
                return this.activateDateFieldSpecified;
            }
            set {
                this.activateDateFieldSpecified = value;
                this.RaisePropertyChanged("ActivateDateSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=18)]
        public System.DateTime UpdateDate {
            get {
                return this.updateDateField;
            }
            set {
                this.updateDateField = value;
                this.RaisePropertyChanged("UpdateDate");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool UpdateDateSpecified {
            get {
                return this.updateDateFieldSpecified;
            }
            set {
                this.updateDateFieldSpecified = value;
                this.RaisePropertyChanged("UpdateDateSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=19)]
        public System.DateTime LastLoginDate {
            get {
                return this.lastLoginDateField;
            }
            set {
                this.lastLoginDateField = value;
                this.RaisePropertyChanged("LastLoginDate");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool LastLoginDateSpecified {
            get {
                return this.lastLoginDateFieldSpecified;
            }
            set {
                this.lastLoginDateFieldSpecified = value;
                this.RaisePropertyChanged("LastLoginDateSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=20)]
        public int LoginCount {
            get {
                return this.loginCountField;
            }
            set {
                this.loginCountField = value;
                this.RaisePropertyChanged("LoginCount");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool LoginCountSpecified {
            get {
                return this.loginCountFieldSpecified;
            }
            set {
                this.loginCountFieldSpecified = value;
                this.RaisePropertyChanged("LoginCountSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=21)]
        public int LoginFailCount {
            get {
                return this.loginFailCountField;
            }
            set {
                this.loginFailCountField = value;
                this.RaisePropertyChanged("LoginFailCount");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool LoginFailCountSpecified {
            get {
                return this.loginFailCountFieldSpecified;
            }
            set {
                this.loginFailCountFieldSpecified = value;
                this.RaisePropertyChanged("LoginFailCountSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=22)]
        public bool AllowMarketing {
            get {
                return this.allowMarketingField;
            }
            set {
                this.allowMarketingField = value;
                this.RaisePropertyChanged("AllowMarketing");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool AllowMarketingSpecified {
            get {
                return this.allowMarketingFieldSpecified;
            }
            set {
                this.allowMarketingFieldSpecified = value;
                this.RaisePropertyChanged("AllowMarketingSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=23)]
        public bool AllowSurvey {
            get {
                return this.allowSurveyField;
            }
            set {
                this.allowSurveyField = value;
                this.RaisePropertyChanged("AllowSurvey");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool AllowSurveySpecified {
            get {
                return this.allowSurveyFieldSpecified;
            }
            set {
                this.allowSurveyFieldSpecified = value;
                this.RaisePropertyChanged("AllowSurveySpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=24)]
        public bool AustralianTaxResident {
            get {
                return this.australianTaxResidentField;
            }
            set {
                this.australianTaxResidentField = value;
                this.RaisePropertyChanged("AustralianTaxResident");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool AustralianTaxResidentSpecified {
            get {
                return this.australianTaxResidentFieldSpecified;
            }
            set {
                this.australianTaxResidentFieldSpecified = value;
                this.RaisePropertyChanged("AustralianTaxResidentSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=25)]
        public string LoginPassword {
            get {
                return this.loginPasswordField;
            }
            set {
                this.loginPasswordField = value;
                this.RaisePropertyChanged("LoginPassword");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=26)]
        public string CountryOfResidence {
            get {
                return this.countryOfResidenceField;
            }
            set {
                this.countryOfResidenceField = value;
                this.RaisePropertyChanged("CountryOfResidence");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class InterestDetails : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string interestTypeField;
        
        private string[] interestField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string InterestType {
            get {
                return this.interestTypeField;
            }
            set {
                this.interestTypeField = value;
                this.RaisePropertyChanged("InterestType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("Interest", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string[] Interest {
            get {
                return this.interestField;
            }
            set {
                this.interestField = value;
                this.RaisePropertyChanged("Interest");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public enum AccountLockStatus {
        
        /// <remarks/>
        LOCKED,
        
        /// <remarks/>
        UNLOCKED,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class PartnerLoyaltyNumberDetails : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string airlineCodeField;
        
        private string loyaltyNumberField;
        
        private string tierLevelCodeField;
        
        private string tierLevelNameField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AirlineCode {
            get {
                return this.airlineCodeField;
            }
            set {
                this.airlineCodeField = value;
                this.RaisePropertyChanged("AirlineCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string LoyaltyNumber {
            get {
                return this.loyaltyNumberField;
            }
            set {
                this.loyaltyNumberField = value;
                this.RaisePropertyChanged("LoyaltyNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string TierLevelCode {
            get {
                return this.tierLevelCodeField;
            }
            set {
                this.tierLevelCodeField = value;
                this.RaisePropertyChanged("TierLevelCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string TierLevelName {
            get {
                return this.tierLevelNameField;
            }
            set {
                this.tierLevelNameField = value;
                this.RaisePropertyChanged("TierLevelName");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public enum NamePrefixType {
        
        /// <remarks/>
        M,
        
        /// <remarks/>
        MR,
        
        /// <remarks/>
        MRS,
        
        /// <remarks/>
        MS,
        
        /// <remarks/>
        MISS,
        
        /// <remarks/>
        MSTR,
        
        /// <remarks/>
        HH,
        
        /// <remarks/>
        HE,
        
        /// <remarks/>
        SH,
        
        /// <remarks/>
        SHA,
        
        /// <remarks/>
        DR,
        
        /// <remarks/>
        MME,
        
        /// <remarks/>
        MLLE,
        
        /// <remarks/>
        ME,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public enum GuestType {
        
        /// <remarks/>
        ADULT,
        
        /// <remarks/>
        CHILD,
        
        /// <remarks/>
        INFANT,
        
        /// <remarks/>
        MILITARY,
        
        /// <remarks/>
        SENIOR,
        
        /// <remarks/>
        YOUTH,
        
        /// <remarks/>
        CBBG,
        
        /// <remarks/>
        SROVR65,
        
        /// <remarks/>
        EXST,
        
        /// <remarks/>
        CIP,
        
        /// <remarks/>
        EMBASSY,
        
        /// <remarks/>
        BUSTRP,
        
        /// <remarks/>
        FAMEVNT,
        
        /// <remarks/>
        AFFILIATES,
        
        /// <remarks/>
        AFFCH,
        
        /// <remarks/>
        CHJERES,
        
        /// <remarks/>
        JEJURES,
        
        /// <remarks/>
        MLTRYOTRS,
        
        /// <remarks/>
        USAMIL,
        
        /// <remarks/>
        PACCINDM,
        
        /// <remarks/>
        INDM,
        
        /// <remarks/>
        PNMER,
        
        /// <remarks/>
        HNDPCH1TO4,
        
        /// <remarks/>
        HDCPADL5TO6,
        
        /// <remarks/>
        HDCPADL1TO4,
        
        /// <remarks/>
        MDISC,
        
        /// <remarks/>
        DEFOLIANTS,
        
        /// <remarks/>
        STUDENT,
        
        /// <remarks/>
        DDHDCRW,
        
        /// <remarks/>
        YOUT11TO24,
        
        /// <remarks/>
        VACATION,
        
        /// <remarks/>
        VACATIONCH,
        
        /// <remarks/>
        HNDCAPGUR,
        
        /// <remarks/>
        HNDCPGUR,
        
        /// <remarks/>
        VACATIONCH50,
        
        /// <remarks/>
        EMABSSY,
        
        /// <remarks/>
        VACATION50,
        
        /// <remarks/>
        AFFILIATE,
        
        /// <remarks/>
        AHHCH,
        
        /// <remarks/>
        JEJURESCH,
        
        /// <remarks/>
        HNDCPCH1TO4,
        
        /// <remarks/>
        DVNM1TO3,
        
        /// <remarks/>
        DVNM,
        
        /// <remarks/>
        YOU11TO24,
        
        /// <remarks/>
        ADT,
        
        /// <remarks/>
        COB,
        
        /// <remarks/>
        CD,
        
        /// <remarks/>
        CE,
        
        /// <remarks/>
        CH,
        
        /// <remarks/>
        CG00,
        
        /// <remarks/>
        DG,
        
        /// <remarks/>
        DP,
        
        /// <remarks/>
        IB00,
        
        /// <remarks/>
        IC00,
        
        /// <remarks/>
        IC50,
        
        /// <remarks/>
        ID00,
        
        /// <remarks/>
        ID50,
        
        /// <remarks/>
        IF00,
        
        /// <remarks/>
        IG20,
        
        /// <remarks/>
        II00,
        
        /// <remarks/>
        IU20,
        
        /// <remarks/>
        IZ00,
        
        /// <remarks/>
        JC,
        
        /// <remarks/>
        JU,
        
        /// <remarks/>
        MM,
        
        /// <remarks/>
        MU,
        
        /// <remarks/>
        NA,
        
        /// <remarks/>
        ND,
        
        /// <remarks/>
        NN,
        
        /// <remarks/>
        PA,
        
        /// <remarks/>
        PC,
        
        /// <remarks/>
        PO,
        
        /// <remarks/>
        PR,
        
        /// <remarks/>
        RA,
        
        /// <remarks/>
        RD,
        
        /// <remarks/>
        RG,
        
        /// <remarks/>
        RR,
        
        /// <remarks/>
        SD,
        
        /// <remarks/>
        XX,
        
        /// <remarks/>
        ZZ,
        
        /// <remarks/>
        CI,
        
        /// <remarks/>
        IC90,
        
        /// <remarks/>
        IC75,
        
        /// <remarks/>
        HC00,
        
        /// <remarks/>
        ID90,
        
        /// <remarks/>
        ID75,
        
        /// <remarks/>
        II90,
        
        /// <remarks/>
        II75,
        
        /// <remarks/>
        HANDICAPPED,
        
        /// <remarks/>
        ISLANDER,
        
        /// <remarks/>
        ZED,
        
        /// <remarks/>
        ZEA,
        
        /// <remarks/>
        ZEC,
        
        /// <remarks/>
        ZEI,
        
        /// <remarks/>
        ZSP,
        
        /// <remarks/>
        ZCS,
        
        /// <remarks/>
        ZSO,
        
        /// <remarks/>
        ZSS,
        
        /// <remarks/>
        ZCO,
        
        /// <remarks/>
        ZEP,
        
        /// <remarks/>
        ZPS,
        
        /// <remarks/>
        ZPM,
        
        /// <remarks/>
        ZES,
        
        /// <remarks/>
        ZSA,
        
        /// <remarks/>
        ZMA,
        
        /// <remarks/>
        ZEF,
        
        /// <remarks/>
        ZPC,
        
        /// <remarks/>
        ZPA,
        
        /// <remarks/>
        ZWA,
        
        /// <remarks/>
        HANDICAP,
        
        /// <remarks/>
        RB,
        
        /// <remarks/>
        ZPL,
        
        /// <remarks/>
        HDH,
        
        /// <remarks/>
        HDC,
        
        /// <remarks/>
        HDL,
        
        /// <remarks/>
        HDA,
        
        /// <remarks/>
        HDJ,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public enum GenderType {
        
        /// <remarks/>
        M,
        
        /// <remarks/>
        F,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class TravelDocuments : object, System.ComponentModel.INotifyPropertyChanged {
        
        private long travelDocumentIdField;
        
        private bool travelDocumentIdFieldSpecified;
        
        private string firstNameField;
        
        private string middleNameField;
        
        private string lastNameField;
        
        private string travelDocumentTypeField;
        
        private string travelDocumentNumberField;
        
        private System.DateTime travelDocumentIssueDateField;
        
        private bool travelDocumentIssueDateFieldSpecified;
        
        private System.DateTime travelDocumentexpirydateField;
        
        private bool travelDocumentexpirydateFieldSpecified;
        
        private string travelDocumentCountryOfIssueField;
        
        private string nationalityField;
        
        private ResidenceDetailsType residenceField;
        
        private bool isPrimaryHolderField;
        
        private bool isPrimaryHolderFieldSpecified;
        
        private string genderField;
        
        private string placeForVisaField;
        
        private System.DateTime dateOfBirthField;
        
        private bool dateOfBirthFieldSpecified;
        
        private string placeOfBirthField;
        
        private ResidenceDetailsType destinationResidenceField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public long TravelDocumentId {
            get {
                return this.travelDocumentIdField;
            }
            set {
                this.travelDocumentIdField = value;
                this.RaisePropertyChanged("TravelDocumentId");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool TravelDocumentIdSpecified {
            get {
                return this.travelDocumentIdFieldSpecified;
            }
            set {
                this.travelDocumentIdFieldSpecified = value;
                this.RaisePropertyChanged("TravelDocumentIdSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string FirstName {
            get {
                return this.firstNameField;
            }
            set {
                this.firstNameField = value;
                this.RaisePropertyChanged("FirstName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string MiddleName {
            get {
                return this.middleNameField;
            }
            set {
                this.middleNameField = value;
                this.RaisePropertyChanged("MiddleName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string LastName {
            get {
                return this.lastNameField;
            }
            set {
                this.lastNameField = value;
                this.RaisePropertyChanged("LastName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string TravelDocumentType {
            get {
                return this.travelDocumentTypeField;
            }
            set {
                this.travelDocumentTypeField = value;
                this.RaisePropertyChanged("TravelDocumentType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string TravelDocumentNumber {
            get {
                return this.travelDocumentNumberField;
            }
            set {
                this.travelDocumentNumberField = value;
                this.RaisePropertyChanged("TravelDocumentNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, DataType="date", Order=6)]
        public System.DateTime TravelDocumentIssueDate {
            get {
                return this.travelDocumentIssueDateField;
            }
            set {
                this.travelDocumentIssueDateField = value;
                this.RaisePropertyChanged("TravelDocumentIssueDate");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool TravelDocumentIssueDateSpecified {
            get {
                return this.travelDocumentIssueDateFieldSpecified;
            }
            set {
                this.travelDocumentIssueDateFieldSpecified = value;
                this.RaisePropertyChanged("TravelDocumentIssueDateSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, DataType="date", Order=7)]
        public System.DateTime TravelDocumentexpirydate {
            get {
                return this.travelDocumentexpirydateField;
            }
            set {
                this.travelDocumentexpirydateField = value;
                this.RaisePropertyChanged("TravelDocumentexpirydate");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool TravelDocumentexpirydateSpecified {
            get {
                return this.travelDocumentexpirydateFieldSpecified;
            }
            set {
                this.travelDocumentexpirydateFieldSpecified = value;
                this.RaisePropertyChanged("TravelDocumentexpirydateSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public string TravelDocumentCountryOfIssue {
            get {
                return this.travelDocumentCountryOfIssueField;
            }
            set {
                this.travelDocumentCountryOfIssueField = value;
                this.RaisePropertyChanged("TravelDocumentCountryOfIssue");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public string Nationality {
            get {
                return this.nationalityField;
            }
            set {
                this.nationalityField = value;
                this.RaisePropertyChanged("Nationality");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=10)]
        public ResidenceDetailsType Residence {
            get {
                return this.residenceField;
            }
            set {
                this.residenceField = value;
                this.RaisePropertyChanged("Residence");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=11)]
        public bool isPrimaryHolder {
            get {
                return this.isPrimaryHolderField;
            }
            set {
                this.isPrimaryHolderField = value;
                this.RaisePropertyChanged("isPrimaryHolder");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool isPrimaryHolderSpecified {
            get {
                return this.isPrimaryHolderFieldSpecified;
            }
            set {
                this.isPrimaryHolderFieldSpecified = value;
                this.RaisePropertyChanged("isPrimaryHolderSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=12)]
        public string Gender {
            get {
                return this.genderField;
            }
            set {
                this.genderField = value;
                this.RaisePropertyChanged("Gender");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=13)]
        public string PlaceForVisa {
            get {
                return this.placeForVisaField;
            }
            set {
                this.placeForVisaField = value;
                this.RaisePropertyChanged("PlaceForVisa");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=14)]
        public System.DateTime DateOfBirth {
            get {
                return this.dateOfBirthField;
            }
            set {
                this.dateOfBirthField = value;
                this.RaisePropertyChanged("DateOfBirth");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool DateOfBirthSpecified {
            get {
                return this.dateOfBirthFieldSpecified;
            }
            set {
                this.dateOfBirthFieldSpecified = value;
                this.RaisePropertyChanged("DateOfBirthSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=15)]
        public string PlaceOfBirth {
            get {
                return this.placeOfBirthField;
            }
            set {
                this.placeOfBirthField = value;
                this.RaisePropertyChanged("PlaceOfBirth");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=16)]
        public ResidenceDetailsType DestinationResidence {
            get {
                return this.destinationResidenceField;
            }
            set {
                this.destinationResidenceField = value;
                this.RaisePropertyChanged("DestinationResidence");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class ResidenceDetailsType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string countryField;
        
        private string addressOneField;
        
        private string addressTwoField;
        
        private string cityField;
        
        private string provinceOrStateField;
        
        private string zipCodeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string Country {
            get {
                return this.countryField;
            }
            set {
                this.countryField = value;
                this.RaisePropertyChanged("Country");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string AddressOne {
            get {
                return this.addressOneField;
            }
            set {
                this.addressOneField = value;
                this.RaisePropertyChanged("AddressOne");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string AddressTwo {
            get {
                return this.addressTwoField;
            }
            set {
                this.addressTwoField = value;
                this.RaisePropertyChanged("AddressTwo");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string City {
            get {
                return this.cityField;
            }
            set {
                this.cityField = value;
                this.RaisePropertyChanged("City");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string ProvinceOrState {
            get {
                return this.provinceOrStateField;
            }
            set {
                this.provinceOrStateField = value;
                this.RaisePropertyChanged("ProvinceOrState");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string ZipCode {
            get {
                return this.zipCodeField;
            }
            set {
                this.zipCodeField = value;
                this.RaisePropertyChanged("ZipCode");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class CreditCardDetails : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string creditcardNumberField;
        
        private string creditcardTypeField;
        
        private string creditcardExpiryDateField;
        
        private string expirationMonthField;
        
        private string expirationYearField;
        
        private string maskedCreditcardNumberField;
        
        private long cardSeqNumberField;
        
        private bool cardSeqNumberFieldSpecified;
        
        private string tokenIdField;
        
        private string customerCardIdField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string CreditcardNumber {
            get {
                return this.creditcardNumberField;
            }
            set {
                this.creditcardNumberField = value;
                this.RaisePropertyChanged("CreditcardNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string CreditcardType {
            get {
                return this.creditcardTypeField;
            }
            set {
                this.creditcardTypeField = value;
                this.RaisePropertyChanged("CreditcardType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string CreditcardExpiryDate {
            get {
                return this.creditcardExpiryDateField;
            }
            set {
                this.creditcardExpiryDateField = value;
                this.RaisePropertyChanged("CreditcardExpiryDate");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string ExpirationMonth {
            get {
                return this.expirationMonthField;
            }
            set {
                this.expirationMonthField = value;
                this.RaisePropertyChanged("ExpirationMonth");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string ExpirationYear {
            get {
                return this.expirationYearField;
            }
            set {
                this.expirationYearField = value;
                this.RaisePropertyChanged("ExpirationYear");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string MaskedCreditcardNumber {
            get {
                return this.maskedCreditcardNumberField;
            }
            set {
                this.maskedCreditcardNumberField = value;
                this.RaisePropertyChanged("MaskedCreditcardNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public long CardSeqNumber {
            get {
                return this.cardSeqNumberField;
            }
            set {
                this.cardSeqNumberField = value;
                this.RaisePropertyChanged("CardSeqNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool CardSeqNumberSpecified {
            get {
                return this.cardSeqNumberFieldSpecified;
            }
            set {
                this.cardSeqNumberFieldSpecified = value;
                this.RaisePropertyChanged("CardSeqNumberSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string TokenId {
            get {
                return this.tokenIdField;
            }
            set {
                this.tokenIdField = value;
                this.RaisePropertyChanged("TokenId");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public string CustomerCardId {
            get {
                return this.customerCardIdField;
            }
            set {
                this.customerCardIdField = value;
                this.RaisePropertyChanged("CustomerCardId");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class CustomerValueDetails : object, System.ComponentModel.INotifyPropertyChanged {
        
        private double cpvValueForPaxField;
        
        private bool cpvValueForPaxFieldSpecified;
        
        private string tierLevelCodeField;
        
        private string[] fareClassesField;
        
        private int noOfSegsField;
        
        private bool noOfSegsFieldSpecified;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public double cpvValueForPax {
            get {
                return this.cpvValueForPaxField;
            }
            set {
                this.cpvValueForPaxField = value;
                this.RaisePropertyChanged("cpvValueForPax");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool cpvValueForPaxSpecified {
            get {
                return this.cpvValueForPaxFieldSpecified;
            }
            set {
                this.cpvValueForPaxFieldSpecified = value;
                this.RaisePropertyChanged("cpvValueForPaxSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string tierLevelCode {
            get {
                return this.tierLevelCodeField;
            }
            set {
                this.tierLevelCodeField = value;
                this.RaisePropertyChanged("tierLevelCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("fareClasses", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string[] fareClasses {
            get {
                return this.fareClassesField;
            }
            set {
                this.fareClassesField = value;
                this.RaisePropertyChanged("fareClasses");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public int noOfSegs {
            get {
                return this.noOfSegsField;
            }
            set {
                this.noOfSegsField = value;
                this.RaisePropertyChanged("noOfSegs");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool noOfSegsSpecified {
            get {
                return this.noOfSegsFieldSpecified;
            }
            set {
                this.noOfSegsFieldSpecified = value;
                this.RaisePropertyChanged("noOfSegsSpecified");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class CustomerProfileConsentDetails : object, System.ComponentModel.INotifyPropertyChanged {
        
        private ConsentCategory categoryField;
        
        private bool isConsentActivationField;
        
        private string remarkField;
        
        private string statusField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public ConsentCategory category {
            get {
                return this.categoryField;
            }
            set {
                this.categoryField = value;
                this.RaisePropertyChanged("category");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public bool isConsentActivation {
            get {
                return this.isConsentActivationField;
            }
            set {
                this.isConsentActivationField = value;
                this.RaisePropertyChanged("isConsentActivation");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string remark {
            get {
                return this.remarkField;
            }
            set {
                this.remarkField = value;
                this.RaisePropertyChanged("remark");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string status {
            get {
                return this.statusField;
            }
            set {
                this.statusField = value;
                this.RaisePropertyChanged("status");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public enum ConsentCategory {
        
        /// <remarks/>
        PERSONALIZATION,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class CustomerProfileContactDetails : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string addressTypeField;
        
        private bool prefferedIndicatorField;
        
        private string phoneNumberField;
        
        private string cellphoneNumberField;
        
        private string businessPhoneNumberField;
        
        private string alternatePhoneNumberField;
        
        private string extensionNumberField;
        
        private string mailingAddressField;
        
        private string addressOneField;
        
        private string addressTwoField;
        
        private string provinceField;
        
        private string zipCodeField;
        
        private string cityField;
        
        private string countryField;
        
        private string faxField;
        
        private string emailIdField;
        
        private bool sendItineraryToEmailIdField;
        
        private bool sendItineraryToEmailIdFieldSpecified;
        
        private string alternateEmailIdField;
        
        private bool sendItineraryToAltEmailIdField;
        
        private bool sendItineraryToAltEmailIdFieldSpecified;
        
        private string phoneNumberCountryCodeField;
        
        private string businessPhoneNumberCountryCodeField;
        
        private string cellNumberCountryCodeField;
        
        private string alternatePhoneNumberCountryCodeField;
        
        private string smartPhoneMailIdField;
        
        private bool smartPhoneSendItineraryField;
        
        private bool smartPhoneSendItineraryFieldSpecified;
        
        private string profileGuestIdField;
        
        private bool sendItineraryToSMSField;
        
        private bool sendItineraryToSMSFieldSpecified;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AddressType {
            get {
                return this.addressTypeField;
            }
            set {
                this.addressTypeField = value;
                this.RaisePropertyChanged("AddressType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public bool PrefferedIndicator {
            get {
                return this.prefferedIndicatorField;
            }
            set {
                this.prefferedIndicatorField = value;
                this.RaisePropertyChanged("PrefferedIndicator");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string PhoneNumber {
            get {
                return this.phoneNumberField;
            }
            set {
                this.phoneNumberField = value;
                this.RaisePropertyChanged("PhoneNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string CellphoneNumber {
            get {
                return this.cellphoneNumberField;
            }
            set {
                this.cellphoneNumberField = value;
                this.RaisePropertyChanged("CellphoneNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string BusinessPhoneNumber {
            get {
                return this.businessPhoneNumberField;
            }
            set {
                this.businessPhoneNumberField = value;
                this.RaisePropertyChanged("BusinessPhoneNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string AlternatePhoneNumber {
            get {
                return this.alternatePhoneNumberField;
            }
            set {
                this.alternatePhoneNumberField = value;
                this.RaisePropertyChanged("AlternatePhoneNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string ExtensionNumber {
            get {
                return this.extensionNumberField;
            }
            set {
                this.extensionNumberField = value;
                this.RaisePropertyChanged("ExtensionNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string MailingAddress {
            get {
                return this.mailingAddressField;
            }
            set {
                this.mailingAddressField = value;
                this.RaisePropertyChanged("MailingAddress");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public string AddressOne {
            get {
                return this.addressOneField;
            }
            set {
                this.addressOneField = value;
                this.RaisePropertyChanged("AddressOne");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public string AddressTwo {
            get {
                return this.addressTwoField;
            }
            set {
                this.addressTwoField = value;
                this.RaisePropertyChanged("AddressTwo");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=10)]
        public string Province {
            get {
                return this.provinceField;
            }
            set {
                this.provinceField = value;
                this.RaisePropertyChanged("Province");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=11)]
        public string ZipCode {
            get {
                return this.zipCodeField;
            }
            set {
                this.zipCodeField = value;
                this.RaisePropertyChanged("ZipCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=12)]
        public string City {
            get {
                return this.cityField;
            }
            set {
                this.cityField = value;
                this.RaisePropertyChanged("City");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=13)]
        public string Country {
            get {
                return this.countryField;
            }
            set {
                this.countryField = value;
                this.RaisePropertyChanged("Country");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=14)]
        public string Fax {
            get {
                return this.faxField;
            }
            set {
                this.faxField = value;
                this.RaisePropertyChanged("Fax");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=15)]
        public string EmailId {
            get {
                return this.emailIdField;
            }
            set {
                this.emailIdField = value;
                this.RaisePropertyChanged("EmailId");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=16)]
        public bool SendItineraryToEmailId {
            get {
                return this.sendItineraryToEmailIdField;
            }
            set {
                this.sendItineraryToEmailIdField = value;
                this.RaisePropertyChanged("SendItineraryToEmailId");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool SendItineraryToEmailIdSpecified {
            get {
                return this.sendItineraryToEmailIdFieldSpecified;
            }
            set {
                this.sendItineraryToEmailIdFieldSpecified = value;
                this.RaisePropertyChanged("SendItineraryToEmailIdSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=17)]
        public string AlternateEmailId {
            get {
                return this.alternateEmailIdField;
            }
            set {
                this.alternateEmailIdField = value;
                this.RaisePropertyChanged("AlternateEmailId");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=18)]
        public bool SendItineraryToAltEmailId {
            get {
                return this.sendItineraryToAltEmailIdField;
            }
            set {
                this.sendItineraryToAltEmailIdField = value;
                this.RaisePropertyChanged("SendItineraryToAltEmailId");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool SendItineraryToAltEmailIdSpecified {
            get {
                return this.sendItineraryToAltEmailIdFieldSpecified;
            }
            set {
                this.sendItineraryToAltEmailIdFieldSpecified = value;
                this.RaisePropertyChanged("SendItineraryToAltEmailIdSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=19)]
        public string PhoneNumberCountryCode {
            get {
                return this.phoneNumberCountryCodeField;
            }
            set {
                this.phoneNumberCountryCodeField = value;
                this.RaisePropertyChanged("PhoneNumberCountryCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=20)]
        public string BusinessPhoneNumberCountryCode {
            get {
                return this.businessPhoneNumberCountryCodeField;
            }
            set {
                this.businessPhoneNumberCountryCodeField = value;
                this.RaisePropertyChanged("BusinessPhoneNumberCountryCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=21)]
        public string CellNumberCountryCode {
            get {
                return this.cellNumberCountryCodeField;
            }
            set {
                this.cellNumberCountryCodeField = value;
                this.RaisePropertyChanged("CellNumberCountryCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=22)]
        public string AlternatePhoneNumberCountryCode {
            get {
                return this.alternatePhoneNumberCountryCodeField;
            }
            set {
                this.alternatePhoneNumberCountryCodeField = value;
                this.RaisePropertyChanged("AlternatePhoneNumberCountryCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=23)]
        public string SmartPhoneMailId {
            get {
                return this.smartPhoneMailIdField;
            }
            set {
                this.smartPhoneMailIdField = value;
                this.RaisePropertyChanged("SmartPhoneMailId");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=24)]
        public bool SmartPhoneSendItinerary {
            get {
                return this.smartPhoneSendItineraryField;
            }
            set {
                this.smartPhoneSendItineraryField = value;
                this.RaisePropertyChanged("SmartPhoneSendItinerary");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool SmartPhoneSendItinerarySpecified {
            get {
                return this.smartPhoneSendItineraryFieldSpecified;
            }
            set {
                this.smartPhoneSendItineraryFieldSpecified = value;
                this.RaisePropertyChanged("SmartPhoneSendItinerarySpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=25)]
        public string ProfileGuestId {
            get {
                return this.profileGuestIdField;
            }
            set {
                this.profileGuestIdField = value;
                this.RaisePropertyChanged("ProfileGuestId");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=26)]
        public bool SendItineraryToSMS {
            get {
                return this.sendItineraryToSMSField;
            }
            set {
                this.sendItineraryToSMSField = value;
                this.RaisePropertyChanged("SendItineraryToSMS");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool SendItineraryToSMSSpecified {
            get {
                return this.sendItineraryToSMSFieldSpecified;
            }
            set {
                this.sendItineraryToSMSFieldSpecified = value;
                this.RaisePropertyChanged("SendItineraryToSMSSpecified");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class FeeWaiveOrOverrideDetails : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string feeNameField;
        
        private string feeCodeField;
        
        private WaiveOrOverride isWaiveOrOverrideField;
        
        private double percentOfOverrideField;
        
        private bool percentOfOverrideFieldSpecified;
        
        private double absoluteValueField;
        
        private bool absoluteValueFieldSpecified;
        
        private string currencyField;
        
        private string overrideReasonField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string FeeName {
            get {
                return this.feeNameField;
            }
            set {
                this.feeNameField = value;
                this.RaisePropertyChanged("FeeName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string FeeCode {
            get {
                return this.feeCodeField;
            }
            set {
                this.feeCodeField = value;
                this.RaisePropertyChanged("FeeCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public WaiveOrOverride IsWaiveOrOverride {
            get {
                return this.isWaiveOrOverrideField;
            }
            set {
                this.isWaiveOrOverrideField = value;
                this.RaisePropertyChanged("IsWaiveOrOverride");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public double PercentOfOverride {
            get {
                return this.percentOfOverrideField;
            }
            set {
                this.percentOfOverrideField = value;
                this.RaisePropertyChanged("PercentOfOverride");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool PercentOfOverrideSpecified {
            get {
                return this.percentOfOverrideFieldSpecified;
            }
            set {
                this.percentOfOverrideFieldSpecified = value;
                this.RaisePropertyChanged("PercentOfOverrideSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public double AbsoluteValue {
            get {
                return this.absoluteValueField;
            }
            set {
                this.absoluteValueField = value;
                this.RaisePropertyChanged("AbsoluteValue");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool AbsoluteValueSpecified {
            get {
                return this.absoluteValueFieldSpecified;
            }
            set {
                this.absoluteValueFieldSpecified = value;
                this.RaisePropertyChanged("AbsoluteValueSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string Currency {
            get {
                return this.currencyField;
            }
            set {
                this.currencyField = value;
                this.RaisePropertyChanged("Currency");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string OverrideReason {
            get {
                return this.overrideReasonField;
            }
            set {
                this.overrideReasonField = value;
                this.RaisePropertyChanged("OverrideReason");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public enum WaiveOrOverride {
        
        /// <remarks/>
        WAIVE,
        
        /// <remarks/>
        OVERRIDE,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class ErrorType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string errorCodeField;
        
        private string errorValueField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string errorCode {
            get {
                return this.errorCodeField;
            }
            set {
                this.errorCodeField = value;
                this.RaisePropertyChanged("errorCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string errorValue {
            get {
                return this.errorValueField;
            }
            set {
                this.errorValueField = value;
                this.RaisePropertyChanged("errorValue");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class ListCustomerProfileDetails : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string profileTypeField;
        
        private string groupNameField;
        
        private string firstNameField;
        
        private string localFirstName1Field;
        
        private string localFirstName2Field;
        
        private string localFirstNameField;
        
        private string phoneNumberCountryCodeField;
        
        private string phoneNumberField;
        
        private string phoneNumberLocalLangField;
        
        private string provinceNameField;
        
        private string provinceNameLocalLangField;
        
        private string zipCodeField;
        
        private string zipCodeLocalLangField;
        
        private string cityField;
        
        private string cityLocalLangField;
        
        private string profileIDField;
        
        private string countryField;
        
        private string countryLocalLangField;
        
        private string statusField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string ProfileType {
            get {
                return this.profileTypeField;
            }
            set {
                this.profileTypeField = value;
                this.RaisePropertyChanged("ProfileType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string GroupName {
            get {
                return this.groupNameField;
            }
            set {
                this.groupNameField = value;
                this.RaisePropertyChanged("GroupName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string FirstName {
            get {
                return this.firstNameField;
            }
            set {
                this.firstNameField = value;
                this.RaisePropertyChanged("FirstName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string LocalFirstName1 {
            get {
                return this.localFirstName1Field;
            }
            set {
                this.localFirstName1Field = value;
                this.RaisePropertyChanged("LocalFirstName1");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string LocalFirstName2 {
            get {
                return this.localFirstName2Field;
            }
            set {
                this.localFirstName2Field = value;
                this.RaisePropertyChanged("LocalFirstName2");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string LocalFirstName {
            get {
                return this.localFirstNameField;
            }
            set {
                this.localFirstNameField = value;
                this.RaisePropertyChanged("LocalFirstName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string PhoneNumberCountryCode {
            get {
                return this.phoneNumberCountryCodeField;
            }
            set {
                this.phoneNumberCountryCodeField = value;
                this.RaisePropertyChanged("PhoneNumberCountryCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string PhoneNumber {
            get {
                return this.phoneNumberField;
            }
            set {
                this.phoneNumberField = value;
                this.RaisePropertyChanged("PhoneNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public string PhoneNumberLocalLang {
            get {
                return this.phoneNumberLocalLangField;
            }
            set {
                this.phoneNumberLocalLangField = value;
                this.RaisePropertyChanged("PhoneNumberLocalLang");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public string provinceName {
            get {
                return this.provinceNameField;
            }
            set {
                this.provinceNameField = value;
                this.RaisePropertyChanged("provinceName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=10)]
        public string provinceNameLocalLang {
            get {
                return this.provinceNameLocalLangField;
            }
            set {
                this.provinceNameLocalLangField = value;
                this.RaisePropertyChanged("provinceNameLocalLang");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=11)]
        public string ZipCode {
            get {
                return this.zipCodeField;
            }
            set {
                this.zipCodeField = value;
                this.RaisePropertyChanged("ZipCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=12)]
        public string ZipCodeLocalLang {
            get {
                return this.zipCodeLocalLangField;
            }
            set {
                this.zipCodeLocalLangField = value;
                this.RaisePropertyChanged("ZipCodeLocalLang");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=13)]
        public string City {
            get {
                return this.cityField;
            }
            set {
                this.cityField = value;
                this.RaisePropertyChanged("City");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=14)]
        public string CityLocalLang {
            get {
                return this.cityLocalLangField;
            }
            set {
                this.cityLocalLangField = value;
                this.RaisePropertyChanged("CityLocalLang");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=15)]
        public string ProfileID {
            get {
                return this.profileIDField;
            }
            set {
                this.profileIDField = value;
                this.RaisePropertyChanged("ProfileID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=16)]
        public string Country {
            get {
                return this.countryField;
            }
            set {
                this.countryField = value;
                this.RaisePropertyChanged("Country");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=17)]
        public string CountryLocalLang {
            get {
                return this.countryLocalLangField;
            }
            set {
                this.countryLocalLangField = value;
                this.RaisePropertyChanged("CountryLocalLang");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=18)]
        public string Status {
            get {
                return this.statusField;
            }
            set {
                this.statusField = value;
                this.RaisePropertyChanged("Status");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class AirlineNewsletter : object, System.ComponentModel.INotifyPropertyChanged {
        
        private NamePrefixType namePrefixField;
        
        private string givenNameField;
        
        private string surNameField;
        
        private string middleNameField;
        
        private System.DateTime dateOfBirthField;
        
        private bool dateOfBirthFieldSpecified;
        
        private string genderField;
        
        private string emailIDField;
        
        private string zipCodeField;
        
        private string countryNameField;
        
        private string nationalityField;
        
        private string languageField;
        
        private bool canSubscribeArlnNewsLetterField;
        
        private string phoneNumberField;
        
        private System.DateTime subscriptionDateField;
        
        private string subsriptionTimeZoneField;
        
        private string ruleIdField;
        
        private string versionIdField;
        
        private string remarksField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public NamePrefixType NamePrefix {
            get {
                return this.namePrefixField;
            }
            set {
                this.namePrefixField = value;
                this.RaisePropertyChanged("NamePrefix");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string GivenName {
            get {
                return this.givenNameField;
            }
            set {
                this.givenNameField = value;
                this.RaisePropertyChanged("GivenName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string SurName {
            get {
                return this.surNameField;
            }
            set {
                this.surNameField = value;
                this.RaisePropertyChanged("SurName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string MiddleName {
            get {
                return this.middleNameField;
            }
            set {
                this.middleNameField = value;
                this.RaisePropertyChanged("MiddleName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public System.DateTime DateOfBirth {
            get {
                return this.dateOfBirthField;
            }
            set {
                this.dateOfBirthField = value;
                this.RaisePropertyChanged("DateOfBirth");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool DateOfBirthSpecified {
            get {
                return this.dateOfBirthFieldSpecified;
            }
            set {
                this.dateOfBirthFieldSpecified = value;
                this.RaisePropertyChanged("DateOfBirthSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string Gender {
            get {
                return this.genderField;
            }
            set {
                this.genderField = value;
                this.RaisePropertyChanged("Gender");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string EmailID {
            get {
                return this.emailIDField;
            }
            set {
                this.emailIDField = value;
                this.RaisePropertyChanged("EmailID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string ZipCode {
            get {
                return this.zipCodeField;
            }
            set {
                this.zipCodeField = value;
                this.RaisePropertyChanged("ZipCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public string CountryName {
            get {
                return this.countryNameField;
            }
            set {
                this.countryNameField = value;
                this.RaisePropertyChanged("CountryName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public string Nationality {
            get {
                return this.nationalityField;
            }
            set {
                this.nationalityField = value;
                this.RaisePropertyChanged("Nationality");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=10)]
        public string Language {
            get {
                return this.languageField;
            }
            set {
                this.languageField = value;
                this.RaisePropertyChanged("Language");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=11)]
        public bool canSubscribeArlnNewsLetter {
            get {
                return this.canSubscribeArlnNewsLetterField;
            }
            set {
                this.canSubscribeArlnNewsLetterField = value;
                this.RaisePropertyChanged("canSubscribeArlnNewsLetter");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=12)]
        public string PhoneNumber {
            get {
                return this.phoneNumberField;
            }
            set {
                this.phoneNumberField = value;
                this.RaisePropertyChanged("PhoneNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=13)]
        public System.DateTime subscriptionDate {
            get {
                return this.subscriptionDateField;
            }
            set {
                this.subscriptionDateField = value;
                this.RaisePropertyChanged("subscriptionDate");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=14)]
        public string subsriptionTimeZone {
            get {
                return this.subsriptionTimeZoneField;
            }
            set {
                this.subsriptionTimeZoneField = value;
                this.RaisePropertyChanged("subsriptionTimeZone");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=15)]
        public string RuleId {
            get {
                return this.ruleIdField;
            }
            set {
                this.ruleIdField = value;
                this.RaisePropertyChanged("RuleId");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=16)]
        public string VersionId {
            get {
                return this.versionIdField;
            }
            set {
                this.versionIdField = value;
                this.RaisePropertyChanged("VersionId");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=17)]
        public string Remarks {
            get {
                return this.remarksField;
            }
            set {
                this.remarksField = value;
                this.RaisePropertyChanged("Remarks");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class CustomerDetails : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string customerNameField;
        
        private string paxIDField;
        
        private string ticketingStatusField;
        
        private System.DateTime purchaseDateTimeField;
        
        private bool purchaseDateTimeFieldSpecified;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string CustomerName {
            get {
                return this.customerNameField;
            }
            set {
                this.customerNameField = value;
                this.RaisePropertyChanged("CustomerName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string PaxID {
            get {
                return this.paxIDField;
            }
            set {
                this.paxIDField = value;
                this.RaisePropertyChanged("PaxID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string TicketingStatus {
            get {
                return this.ticketingStatusField;
            }
            set {
                this.ticketingStatusField = value;
                this.RaisePropertyChanged("TicketingStatus");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public System.DateTime PurchaseDateTime {
            get {
                return this.purchaseDateTimeField;
            }
            set {
                this.purchaseDateTimeField = value;
                this.RaisePropertyChanged("PurchaseDateTime");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool PurchaseDateTimeSpecified {
            get {
                return this.purchaseDateTimeFieldSpecified;
            }
            set {
                this.purchaseDateTimeFieldSpecified = value;
                this.RaisePropertyChanged("PurchaseDateTimeSpecified");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class PNRList : object, System.ComponentModel.INotifyPropertyChanged {
        
        private CustomerDetails[] customerDetailsField;
        
        private string pnrNumberField;
        
        private long numericPnrNumberField;
        
        private System.DateTime creationDateAndTimeField;
        
        private string carrierCodeField;
        
        private int flightNumberField;
        
        private bool flightNumberFieldSpecified;
        
        private System.DateTime depDateTimeField;
        
        private bool depDateTimeFieldSpecified;
        
        private string departureTimeZoneField;
        
        private System.DateTime arrDateTimeField;
        
        private bool arrDateTimeFieldSpecified;
        
        private string arrivalTimeZoneField;
        
        private string originField;
        
        private string destinationField;
        
        private string pnrStatusField;
        
        private System.DateTime changeDateTimeField;
        
        private int seatCountField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("CustomerDetails", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public CustomerDetails[] CustomerDetails {
            get {
                return this.customerDetailsField;
            }
            set {
                this.customerDetailsField = value;
                this.RaisePropertyChanged("CustomerDetails");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string PnrNumber {
            get {
                return this.pnrNumberField;
            }
            set {
                this.pnrNumberField = value;
                this.RaisePropertyChanged("PnrNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public long NumericPnrNumber {
            get {
                return this.numericPnrNumberField;
            }
            set {
                this.numericPnrNumberField = value;
                this.RaisePropertyChanged("NumericPnrNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public System.DateTime CreationDateAndTime {
            get {
                return this.creationDateAndTimeField;
            }
            set {
                this.creationDateAndTimeField = value;
                this.RaisePropertyChanged("CreationDateAndTime");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string CarrierCode {
            get {
                return this.carrierCodeField;
            }
            set {
                this.carrierCodeField = value;
                this.RaisePropertyChanged("CarrierCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public int FlightNumber {
            get {
                return this.flightNumberField;
            }
            set {
                this.flightNumberField = value;
                this.RaisePropertyChanged("FlightNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool FlightNumberSpecified {
            get {
                return this.flightNumberFieldSpecified;
            }
            set {
                this.flightNumberFieldSpecified = value;
                this.RaisePropertyChanged("FlightNumberSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public System.DateTime DepDateTime {
            get {
                return this.depDateTimeField;
            }
            set {
                this.depDateTimeField = value;
                this.RaisePropertyChanged("DepDateTime");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool DepDateTimeSpecified {
            get {
                return this.depDateTimeFieldSpecified;
            }
            set {
                this.depDateTimeFieldSpecified = value;
                this.RaisePropertyChanged("DepDateTimeSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string DepartureTimeZone {
            get {
                return this.departureTimeZoneField;
            }
            set {
                this.departureTimeZoneField = value;
                this.RaisePropertyChanged("DepartureTimeZone");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public System.DateTime ArrDateTime {
            get {
                return this.arrDateTimeField;
            }
            set {
                this.arrDateTimeField = value;
                this.RaisePropertyChanged("ArrDateTime");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool ArrDateTimeSpecified {
            get {
                return this.arrDateTimeFieldSpecified;
            }
            set {
                this.arrDateTimeFieldSpecified = value;
                this.RaisePropertyChanged("ArrDateTimeSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public string ArrivalTimeZone {
            get {
                return this.arrivalTimeZoneField;
            }
            set {
                this.arrivalTimeZoneField = value;
                this.RaisePropertyChanged("ArrivalTimeZone");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=10)]
        public string Origin {
            get {
                return this.originField;
            }
            set {
                this.originField = value;
                this.RaisePropertyChanged("Origin");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=11)]
        public string Destination {
            get {
                return this.destinationField;
            }
            set {
                this.destinationField = value;
                this.RaisePropertyChanged("Destination");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=12)]
        public string PnrStatus {
            get {
                return this.pnrStatusField;
            }
            set {
                this.pnrStatusField = value;
                this.RaisePropertyChanged("PnrStatus");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=13)]
        public System.DateTime ChangeDateTime {
            get {
                return this.changeDateTimeField;
            }
            set {
                this.changeDateTimeField = value;
                this.RaisePropertyChanged("ChangeDateTime");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=14)]
        public int SeatCount {
            get {
                return this.seatCountField;
            }
            set {
                this.seatCountField = value;
                this.RaisePropertyChanged("SeatCount");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class WarningType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string warningMessageField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string warningMessage {
            get {
                return this.warningMessageField;
            }
            set {
                this.warningMessageField = value;
                this.RaisePropertyChanged("warningMessage");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class ProviderDetails : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string providerIdField;
        
        private ProviderType providerTypeField;
        
        private string emailField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string ProviderId {
            get {
                return this.providerIdField;
            }
            set {
                this.providerIdField = value;
                this.RaisePropertyChanged("ProviderId");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public ProviderType ProviderType {
            get {
                return this.providerTypeField;
            }
            set {
                this.providerTypeField = value;
                this.RaisePropertyChanged("ProviderType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string Email {
            get {
                return this.emailField;
            }
            set {
                this.emailField = value;
                this.RaisePropertyChanged("Email");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public enum ProviderType {
        
        /// <remarks/>
        GOOGLE,
        
        /// <remarks/>
        FACEBOOK,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class CreateCustomerProfileRS : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string loyaltyNumberField;
        
        private string profileIdField;
        
        private string loginStatusField;
        
        private ErrorType errorTypeField;
        
        private WarningType warningsField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string LoyaltyNumber {
            get {
                return this.loyaltyNumberField;
            }
            set {
                this.loyaltyNumberField = value;
                this.RaisePropertyChanged("LoyaltyNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string ProfileId {
            get {
                return this.profileIdField;
            }
            set {
                this.profileIdField = value;
                this.RaisePropertyChanged("ProfileId");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string LoginStatus {
            get {
                return this.loginStatusField;
            }
            set {
                this.loginStatusField = value;
                this.RaisePropertyChanged("LoginStatus");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public ErrorType ErrorType {
            get {
                return this.errorTypeField;
            }
            set {
                this.errorTypeField = value;
                this.RaisePropertyChanged("ErrorType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public WarningType Warnings {
            get {
                return this.warningsField;
            }
            set {
                this.warningsField = value;
                this.RaisePropertyChanged("Warnings");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class createCustomerProfileRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.CheckLoyaltyNumberService.CreateCustomerProfileRQ CreateCustomerProfileRQ;
        
        public createCustomerProfileRequest() {
        }
        
        public createCustomerProfileRequest(Ubimecs.IBS.Services.CheckLoyaltyNumberService.CreateCustomerProfileRQ CreateCustomerProfileRQ) {
            this.CreateCustomerProfileRQ = CreateCustomerProfileRQ;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class createCustomerProfileResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.CheckLoyaltyNumberService.CreateCustomerProfileRS CreateCustomerProfileRS;
        
        public createCustomerProfileResponse() {
        }
        
        public createCustomerProfileResponse(Ubimecs.IBS.Services.CheckLoyaltyNumberService.CreateCustomerProfileRS CreateCustomerProfileRS) {
            this.CreateCustomerProfileRS = CreateCustomerProfileRS;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class CustomerBookedListRQ : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string bookerProfileIdField;
        
        private BookingChannelKeyType bookingChannelField;
        
        private System.DateTime startDateField;
        
        private bool startDateFieldSpecified;
        
        private System.DateTime endDateField;
        
        private bool endDateFieldSpecified;
        
        private string airlineCodeField;
        
        private string pnrNumberField;
        
        private long numericPnrNumberField;
        
        private bool numericPnrNumberFieldSpecified;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string BookerProfileId {
            get {
                return this.bookerProfileIdField;
            }
            set {
                this.bookerProfileIdField = value;
                this.RaisePropertyChanged("BookerProfileId");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public BookingChannelKeyType BookingChannel {
            get {
                return this.bookingChannelField;
            }
            set {
                this.bookingChannelField = value;
                this.RaisePropertyChanged("BookingChannel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, DataType="date", Order=2)]
        public System.DateTime StartDate {
            get {
                return this.startDateField;
            }
            set {
                this.startDateField = value;
                this.RaisePropertyChanged("StartDate");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool StartDateSpecified {
            get {
                return this.startDateFieldSpecified;
            }
            set {
                this.startDateFieldSpecified = value;
                this.RaisePropertyChanged("StartDateSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, DataType="date", Order=3)]
        public System.DateTime EndDate {
            get {
                return this.endDateField;
            }
            set {
                this.endDateField = value;
                this.RaisePropertyChanged("EndDate");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool EndDateSpecified {
            get {
                return this.endDateFieldSpecified;
            }
            set {
                this.endDateFieldSpecified = value;
                this.RaisePropertyChanged("EndDateSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string AirlineCode {
            get {
                return this.airlineCodeField;
            }
            set {
                this.airlineCodeField = value;
                this.RaisePropertyChanged("AirlineCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string PnrNumber {
            get {
                return this.pnrNumberField;
            }
            set {
                this.pnrNumberField = value;
                this.RaisePropertyChanged("PnrNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public long NumericPnrNumber {
            get {
                return this.numericPnrNumberField;
            }
            set {
                this.numericPnrNumberField = value;
                this.RaisePropertyChanged("NumericPnrNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool NumericPnrNumberSpecified {
            get {
                return this.numericPnrNumberFieldSpecified;
            }
            set {
                this.numericPnrNumberFieldSpecified = value;
                this.RaisePropertyChanged("NumericPnrNumberSpecified");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class CustomerBookedListRS : object, System.ComponentModel.INotifyPropertyChanged {
        
        private PNRList[] pNRListField;
        
        private ErrorType errorTypeField;
        
        private string airlineCodeField;
        
        private string bookerProfileIDField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("PNRList", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public PNRList[] PNRList {
            get {
                return this.pNRListField;
            }
            set {
                this.pNRListField = value;
                this.RaisePropertyChanged("PNRList");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public ErrorType ErrorType {
            get {
                return this.errorTypeField;
            }
            set {
                this.errorTypeField = value;
                this.RaisePropertyChanged("ErrorType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string AirlineCode {
            get {
                return this.airlineCodeField;
            }
            set {
                this.airlineCodeField = value;
                this.RaisePropertyChanged("AirlineCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string BookerProfileID {
            get {
                return this.bookerProfileIDField;
            }
            set {
                this.bookerProfileIDField = value;
                this.RaisePropertyChanged("BookerProfileID");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class retrieveCustomerBookedListRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerBookedListRQ CustomerBookedListRQ;
        
        public retrieveCustomerBookedListRequest() {
        }
        
        public retrieveCustomerBookedListRequest(Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerBookedListRQ CustomerBookedListRQ) {
            this.CustomerBookedListRQ = CustomerBookedListRQ;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class retrieveCustomerBookedListResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerBookedListRS CustomerBookedListRS;
        
        public retrieveCustomerBookedListResponse() {
        }
        
        public retrieveCustomerBookedListResponse(Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerBookedListRS CustomerBookedListRS) {
            this.CustomerBookedListRS = CustomerBookedListRS;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class DeactivateCustomerProfileRQ : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string airlineCodeField;
        
        private BookingChannelKeyType bookingChannelField;
        
        private string profileIDField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AirlineCode {
            get {
                return this.airlineCodeField;
            }
            set {
                this.airlineCodeField = value;
                this.RaisePropertyChanged("AirlineCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public BookingChannelKeyType BookingChannel {
            get {
                return this.bookingChannelField;
            }
            set {
                this.bookingChannelField = value;
                this.RaisePropertyChanged("BookingChannel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string ProfileID {
            get {
                return this.profileIDField;
            }
            set {
                this.profileIDField = value;
                this.RaisePropertyChanged("ProfileID");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class DeactivateCustomerProfileRS : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string airlineCodeField;
        
        private string profileStatusField;
        
        private ErrorType errorTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AirlineCode {
            get {
                return this.airlineCodeField;
            }
            set {
                this.airlineCodeField = value;
                this.RaisePropertyChanged("AirlineCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string ProfileStatus {
            get {
                return this.profileStatusField;
            }
            set {
                this.profileStatusField = value;
                this.RaisePropertyChanged("ProfileStatus");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public ErrorType ErrorType {
            get {
                return this.errorTypeField;
            }
            set {
                this.errorTypeField = value;
                this.RaisePropertyChanged("ErrorType");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class deactivateCustomerProfileRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.CheckLoyaltyNumberService.DeactivateCustomerProfileRQ DeactivateCustomerProfileRQ;
        
        public deactivateCustomerProfileRequest() {
        }
        
        public deactivateCustomerProfileRequest(Ubimecs.IBS.Services.CheckLoyaltyNumberService.DeactivateCustomerProfileRQ DeactivateCustomerProfileRQ) {
            this.DeactivateCustomerProfileRQ = DeactivateCustomerProfileRQ;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class deactivateCustomerProfileResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.CheckLoyaltyNumberService.DeactivateCustomerProfileRS DeactivateCustomerProfileRS;
        
        public deactivateCustomerProfileResponse() {
        }
        
        public deactivateCustomerProfileResponse(Ubimecs.IBS.Services.CheckLoyaltyNumberService.DeactivateCustomerProfileRS DeactivateCustomerProfileRS) {
            this.DeactivateCustomerProfileRS = DeactivateCustomerProfileRS;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class GetNewsLetterSubscriptionInfoRQ : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string airlineCodeField;
        
        private BookingChannelKeyType bookingChannelField;
        
        private string emailIDField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AirlineCode {
            get {
                return this.airlineCodeField;
            }
            set {
                this.airlineCodeField = value;
                this.RaisePropertyChanged("AirlineCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public BookingChannelKeyType BookingChannel {
            get {
                return this.bookingChannelField;
            }
            set {
                this.bookingChannelField = value;
                this.RaisePropertyChanged("BookingChannel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string EmailID {
            get {
                return this.emailIDField;
            }
            set {
                this.emailIDField = value;
                this.RaisePropertyChanged("EmailID");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class GetNewsLetterSubscriptionInfoRS : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string airlineCodeField;
        
        private AirlineNewsletter airlineNewsletterField;
        
        private string statusField;
        
        private ErrorType errorTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AirlineCode {
            get {
                return this.airlineCodeField;
            }
            set {
                this.airlineCodeField = value;
                this.RaisePropertyChanged("AirlineCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public AirlineNewsletter AirlineNewsletter {
            get {
                return this.airlineNewsletterField;
            }
            set {
                this.airlineNewsletterField = value;
                this.RaisePropertyChanged("AirlineNewsletter");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string Status {
            get {
                return this.statusField;
            }
            set {
                this.statusField = value;
                this.RaisePropertyChanged("Status");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public ErrorType ErrorType {
            get {
                return this.errorTypeField;
            }
            set {
                this.errorTypeField = value;
                this.RaisePropertyChanged("ErrorType");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class getNewsLetterSubscriptionInfoRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.CheckLoyaltyNumberService.GetNewsLetterSubscriptionInfoRQ GetNewsLetterSubscriptionInfoRQ;
        
        public getNewsLetterSubscriptionInfoRequest() {
        }
        
        public getNewsLetterSubscriptionInfoRequest(Ubimecs.IBS.Services.CheckLoyaltyNumberService.GetNewsLetterSubscriptionInfoRQ GetNewsLetterSubscriptionInfoRQ) {
            this.GetNewsLetterSubscriptionInfoRQ = GetNewsLetterSubscriptionInfoRQ;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class getNewsLetterSubscriptionInfoResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.CheckLoyaltyNumberService.GetNewsLetterSubscriptionInfoRS GetNewsLetterSubscriptionInfoRS;
        
        public getNewsLetterSubscriptionInfoResponse() {
        }
        
        public getNewsLetterSubscriptionInfoResponse(Ubimecs.IBS.Services.CheckLoyaltyNumberService.GetNewsLetterSubscriptionInfoRS GetNewsLetterSubscriptionInfoRS) {
            this.GetNewsLetterSubscriptionInfoRS = GetNewsLetterSubscriptionInfoRS;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class ListCustomerProfileRQ : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string profileTypeField;
        
        private BookingChannelKeyType bookingChannelField;
        
        private string profileAliasField;
        
        private string airlineCodeField;
        
        private string lastNameField;
        
        private string firstNameField;
        
        private string phoneNumberCountryCodeField;
        
        private string phoneNumberField;
        
        private string emailIDField;
        
        private System.DateTime dateofBirthField;
        
        private bool dateofBirthFieldSpecified;
        
        private string loyaltynumberField;
        
        private string creditcardNumberField;
        
        private string zipCodeField;
        
        private string cityField;
        
        private string pNRnumberField;
        
        private string profileIDField;
        
        private string countryField;
        
        private string statusField;
        
        private System.DateTime lastSyncDateField;
        
        private bool lastSyncDateFieldSpecified;
        
        private string localLastName1Field;
        
        private string localFirstName1Field;
        
        private string localLastNameField;
        
        private string localFirstNameField;
        
        private string localAddress1Field;
        
        private string localAddress2Field;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string ProfileType {
            get {
                return this.profileTypeField;
            }
            set {
                this.profileTypeField = value;
                this.RaisePropertyChanged("ProfileType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public BookingChannelKeyType BookingChannel {
            get {
                return this.bookingChannelField;
            }
            set {
                this.bookingChannelField = value;
                this.RaisePropertyChanged("BookingChannel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string ProfileAlias {
            get {
                return this.profileAliasField;
            }
            set {
                this.profileAliasField = value;
                this.RaisePropertyChanged("ProfileAlias");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string AirlineCode {
            get {
                return this.airlineCodeField;
            }
            set {
                this.airlineCodeField = value;
                this.RaisePropertyChanged("AirlineCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string LastName {
            get {
                return this.lastNameField;
            }
            set {
                this.lastNameField = value;
                this.RaisePropertyChanged("LastName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string FirstName {
            get {
                return this.firstNameField;
            }
            set {
                this.firstNameField = value;
                this.RaisePropertyChanged("FirstName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string PhoneNumberCountryCode {
            get {
                return this.phoneNumberCountryCodeField;
            }
            set {
                this.phoneNumberCountryCodeField = value;
                this.RaisePropertyChanged("PhoneNumberCountryCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string PhoneNumber {
            get {
                return this.phoneNumberField;
            }
            set {
                this.phoneNumberField = value;
                this.RaisePropertyChanged("PhoneNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public string EmailID {
            get {
                return this.emailIDField;
            }
            set {
                this.emailIDField = value;
                this.RaisePropertyChanged("EmailID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, DataType="date", Order=9)]
        public System.DateTime DateofBirth {
            get {
                return this.dateofBirthField;
            }
            set {
                this.dateofBirthField = value;
                this.RaisePropertyChanged("DateofBirth");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool DateofBirthSpecified {
            get {
                return this.dateofBirthFieldSpecified;
            }
            set {
                this.dateofBirthFieldSpecified = value;
                this.RaisePropertyChanged("DateofBirthSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=10)]
        public string Loyaltynumber {
            get {
                return this.loyaltynumberField;
            }
            set {
                this.loyaltynumberField = value;
                this.RaisePropertyChanged("Loyaltynumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=11)]
        public string CreditcardNumber {
            get {
                return this.creditcardNumberField;
            }
            set {
                this.creditcardNumberField = value;
                this.RaisePropertyChanged("CreditcardNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=12)]
        public string ZipCode {
            get {
                return this.zipCodeField;
            }
            set {
                this.zipCodeField = value;
                this.RaisePropertyChanged("ZipCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=13)]
        public string City {
            get {
                return this.cityField;
            }
            set {
                this.cityField = value;
                this.RaisePropertyChanged("City");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=14)]
        public string PNRnumber {
            get {
                return this.pNRnumberField;
            }
            set {
                this.pNRnumberField = value;
                this.RaisePropertyChanged("PNRnumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=15)]
        public string ProfileID {
            get {
                return this.profileIDField;
            }
            set {
                this.profileIDField = value;
                this.RaisePropertyChanged("ProfileID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=16)]
        public string Country {
            get {
                return this.countryField;
            }
            set {
                this.countryField = value;
                this.RaisePropertyChanged("Country");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=17)]
        public string Status {
            get {
                return this.statusField;
            }
            set {
                this.statusField = value;
                this.RaisePropertyChanged("Status");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=18)]
        public System.DateTime LastSyncDate {
            get {
                return this.lastSyncDateField;
            }
            set {
                this.lastSyncDateField = value;
                this.RaisePropertyChanged("LastSyncDate");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool LastSyncDateSpecified {
            get {
                return this.lastSyncDateFieldSpecified;
            }
            set {
                this.lastSyncDateFieldSpecified = value;
                this.RaisePropertyChanged("LastSyncDateSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=19)]
        public string LocalLastName1 {
            get {
                return this.localLastName1Field;
            }
            set {
                this.localLastName1Field = value;
                this.RaisePropertyChanged("LocalLastName1");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=20)]
        public string LocalFirstName1 {
            get {
                return this.localFirstName1Field;
            }
            set {
                this.localFirstName1Field = value;
                this.RaisePropertyChanged("LocalFirstName1");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=21)]
        public string LocalLastName {
            get {
                return this.localLastNameField;
            }
            set {
                this.localLastNameField = value;
                this.RaisePropertyChanged("LocalLastName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=22)]
        public string LocalFirstName {
            get {
                return this.localFirstNameField;
            }
            set {
                this.localFirstNameField = value;
                this.RaisePropertyChanged("LocalFirstName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=23)]
        public string LocalAddress1 {
            get {
                return this.localAddress1Field;
            }
            set {
                this.localAddress1Field = value;
                this.RaisePropertyChanged("LocalAddress1");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=24)]
        public string LocalAddress2 {
            get {
                return this.localAddress2Field;
            }
            set {
                this.localAddress2Field = value;
                this.RaisePropertyChanged("LocalAddress2");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class ListCustomerProfileRS : object, System.ComponentModel.INotifyPropertyChanged {
        
        private ListCustomerProfileDetails[] listProfileField;
        
        private ErrorType errorTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("ListProfile", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public ListCustomerProfileDetails[] ListProfile {
            get {
                return this.listProfileField;
            }
            set {
                this.listProfileField = value;
                this.RaisePropertyChanged("ListProfile");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public ErrorType ErrorType {
            get {
                return this.errorTypeField;
            }
            set {
                this.errorTypeField = value;
                this.RaisePropertyChanged("ErrorType");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class listCustomerProfileRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.CheckLoyaltyNumberService.ListCustomerProfileRQ ListCustomerProfileRQ;
        
        public listCustomerProfileRequest() {
        }
        
        public listCustomerProfileRequest(Ubimecs.IBS.Services.CheckLoyaltyNumberService.ListCustomerProfileRQ ListCustomerProfileRQ) {
            this.ListCustomerProfileRQ = ListCustomerProfileRQ;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class listCustomerProfileResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.CheckLoyaltyNumberService.ListCustomerProfileRS ListCustomerProfileRS;
        
        public listCustomerProfileResponse() {
        }
        
        public listCustomerProfileResponse(Ubimecs.IBS.Services.CheckLoyaltyNumberService.ListCustomerProfileRS ListCustomerProfileRS) {
            this.ListCustomerProfileRS = ListCustomerProfileRS;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class ModifyCustomerProfileRQ : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string airlineCodeField;
        
        private BookingChannelKeyType bookingChannelField;
        
        private string profileAliasField;
        
        private string profileTypeField;
        
        private GuestDetails[] guestDetailsField;
        
        private CustomerProfileContactDetails homeContactField;
        
        private CustomerProfileContactDetails businessContactField;
        
        private CustomerProfileContactDetails alternateContactField;
        
        private string viewModeField;
        
        private FeeWaiveOrOverrideDetails[] feeWaiveOrOverrideDetailsField;
        
        private string profileIDField;
        
        private string externalProfileIDField;
        
        private CustomerProfileContactDetails localLanguageContactField;
        
        private CustomerProfileContactDetails[] paxSpecificContactsField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AirlineCode {
            get {
                return this.airlineCodeField;
            }
            set {
                this.airlineCodeField = value;
                this.RaisePropertyChanged("AirlineCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public BookingChannelKeyType BookingChannel {
            get {
                return this.bookingChannelField;
            }
            set {
                this.bookingChannelField = value;
                this.RaisePropertyChanged("BookingChannel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string ProfileAlias {
            get {
                return this.profileAliasField;
            }
            set {
                this.profileAliasField = value;
                this.RaisePropertyChanged("ProfileAlias");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string ProfileType {
            get {
                return this.profileTypeField;
            }
            set {
                this.profileTypeField = value;
                this.RaisePropertyChanged("ProfileType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("GuestDetails", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public GuestDetails[] GuestDetails {
            get {
                return this.guestDetailsField;
            }
            set {
                this.guestDetailsField = value;
                this.RaisePropertyChanged("GuestDetails");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public CustomerProfileContactDetails HomeContact {
            get {
                return this.homeContactField;
            }
            set {
                this.homeContactField = value;
                this.RaisePropertyChanged("HomeContact");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public CustomerProfileContactDetails BusinessContact {
            get {
                return this.businessContactField;
            }
            set {
                this.businessContactField = value;
                this.RaisePropertyChanged("BusinessContact");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public CustomerProfileContactDetails AlternateContact {
            get {
                return this.alternateContactField;
            }
            set {
                this.alternateContactField = value;
                this.RaisePropertyChanged("AlternateContact");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public string ViewMode {
            get {
                return this.viewModeField;
            }
            set {
                this.viewModeField = value;
                this.RaisePropertyChanged("ViewMode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("FeeWaiveOrOverrideDetails", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public FeeWaiveOrOverrideDetails[] FeeWaiveOrOverrideDetails {
            get {
                return this.feeWaiveOrOverrideDetailsField;
            }
            set {
                this.feeWaiveOrOverrideDetailsField = value;
                this.RaisePropertyChanged("FeeWaiveOrOverrideDetails");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=10)]
        public string ProfileID {
            get {
                return this.profileIDField;
            }
            set {
                this.profileIDField = value;
                this.RaisePropertyChanged("ProfileID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=11)]
        public string ExternalProfileID {
            get {
                return this.externalProfileIDField;
            }
            set {
                this.externalProfileIDField = value;
                this.RaisePropertyChanged("ExternalProfileID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=12)]
        public CustomerProfileContactDetails LocalLanguageContact {
            get {
                return this.localLanguageContactField;
            }
            set {
                this.localLanguageContactField = value;
                this.RaisePropertyChanged("LocalLanguageContact");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("PaxSpecificContacts", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=13)]
        public CustomerProfileContactDetails[] PaxSpecificContacts {
            get {
                return this.paxSpecificContactsField;
            }
            set {
                this.paxSpecificContactsField = value;
                this.RaisePropertyChanged("PaxSpecificContacts");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class ModifyCustomerProfileRS : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string profileAliasField;
        
        private string profileIdField;
        
        private ErrorType errorTypeField;
        
        private WarningType warningsField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string ProfileAlias {
            get {
                return this.profileAliasField;
            }
            set {
                this.profileAliasField = value;
                this.RaisePropertyChanged("ProfileAlias");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string ProfileId {
            get {
                return this.profileIdField;
            }
            set {
                this.profileIdField = value;
                this.RaisePropertyChanged("ProfileId");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public ErrorType ErrorType {
            get {
                return this.errorTypeField;
            }
            set {
                this.errorTypeField = value;
                this.RaisePropertyChanged("ErrorType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public WarningType Warnings {
            get {
                return this.warningsField;
            }
            set {
                this.warningsField = value;
                this.RaisePropertyChanged("Warnings");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class modifyCustomerProfileRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.CheckLoyaltyNumberService.ModifyCustomerProfileRQ ModifyCustomerProfileRQ;
        
        public modifyCustomerProfileRequest() {
        }
        
        public modifyCustomerProfileRequest(Ubimecs.IBS.Services.CheckLoyaltyNumberService.ModifyCustomerProfileRQ ModifyCustomerProfileRQ) {
            this.ModifyCustomerProfileRQ = ModifyCustomerProfileRQ;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class modifyCustomerProfileResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.CheckLoyaltyNumberService.ModifyCustomerProfileRS ModifyCustomerProfileRS;
        
        public modifyCustomerProfileResponse() {
        }
        
        public modifyCustomerProfileResponse(Ubimecs.IBS.Services.CheckLoyaltyNumberService.ModifyCustomerProfileRS ModifyCustomerProfileRS) {
            this.ModifyCustomerProfileRS = ModifyCustomerProfileRS;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class ResetLoyaltyPasswordRQ : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string airlineCodeField;
        
        private string loyaltyNumberField;
        
        private BookingChannelKeyType bookingChannelField;
        
        private string emailIDField;
        
        private System.DateTime dateofBirthField;
        
        private bool dateofBirthFieldSpecified;
        
        private string securityQuestionField;
        
        private string securityAnswerField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AirlineCode {
            get {
                return this.airlineCodeField;
            }
            set {
                this.airlineCodeField = value;
                this.RaisePropertyChanged("AirlineCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string LoyaltyNumber {
            get {
                return this.loyaltyNumberField;
            }
            set {
                this.loyaltyNumberField = value;
                this.RaisePropertyChanged("LoyaltyNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public BookingChannelKeyType BookingChannel {
            get {
                return this.bookingChannelField;
            }
            set {
                this.bookingChannelField = value;
                this.RaisePropertyChanged("BookingChannel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string EmailID {
            get {
                return this.emailIDField;
            }
            set {
                this.emailIDField = value;
                this.RaisePropertyChanged("EmailID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, DataType="date", Order=4)]
        public System.DateTime DateofBirth {
            get {
                return this.dateofBirthField;
            }
            set {
                this.dateofBirthField = value;
                this.RaisePropertyChanged("DateofBirth");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool DateofBirthSpecified {
            get {
                return this.dateofBirthFieldSpecified;
            }
            set {
                this.dateofBirthFieldSpecified = value;
                this.RaisePropertyChanged("DateofBirthSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string SecurityQuestion {
            get {
                return this.securityQuestionField;
            }
            set {
                this.securityQuestionField = value;
                this.RaisePropertyChanged("SecurityQuestion");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string SecurityAnswer {
            get {
                return this.securityAnswerField;
            }
            set {
                this.securityAnswerField = value;
                this.RaisePropertyChanged("SecurityAnswer");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class ResetLoyaltyPasswordRS : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string statusField;
        
        private ErrorType errorTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string Status {
            get {
                return this.statusField;
            }
            set {
                this.statusField = value;
                this.RaisePropertyChanged("Status");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public ErrorType ErrorType {
            get {
                return this.errorTypeField;
            }
            set {
                this.errorTypeField = value;
                this.RaisePropertyChanged("ErrorType");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class resetLoyaltyPasswordRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.CheckLoyaltyNumberService.ResetLoyaltyPasswordRQ ResetLoyaltyPasswordRQ;
        
        public resetLoyaltyPasswordRequest() {
        }
        
        public resetLoyaltyPasswordRequest(Ubimecs.IBS.Services.CheckLoyaltyNumberService.ResetLoyaltyPasswordRQ ResetLoyaltyPasswordRQ) {
            this.ResetLoyaltyPasswordRQ = ResetLoyaltyPasswordRQ;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class resetLoyaltyPasswordResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.CheckLoyaltyNumberService.ResetLoyaltyPasswordRS ResetLoyaltyPasswordRS;
        
        public resetLoyaltyPasswordResponse() {
        }
        
        public resetLoyaltyPasswordResponse(Ubimecs.IBS.Services.CheckLoyaltyNumberService.ResetLoyaltyPasswordRS ResetLoyaltyPasswordRS) {
            this.ResetLoyaltyPasswordRS = ResetLoyaltyPasswordRS;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class SetLoyaltyPasswordRQ : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string airlineCodeField;
        
        private string loyaltyNumberField;
        
        private BookingChannelKeyType bookingChannelField;
        
        private string newLoginPasswordField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AirlineCode {
            get {
                return this.airlineCodeField;
            }
            set {
                this.airlineCodeField = value;
                this.RaisePropertyChanged("AirlineCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string LoyaltyNumber {
            get {
                return this.loyaltyNumberField;
            }
            set {
                this.loyaltyNumberField = value;
                this.RaisePropertyChanged("LoyaltyNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public BookingChannelKeyType BookingChannel {
            get {
                return this.bookingChannelField;
            }
            set {
                this.bookingChannelField = value;
                this.RaisePropertyChanged("BookingChannel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string NewLoginPassword {
            get {
                return this.newLoginPasswordField;
            }
            set {
                this.newLoginPasswordField = value;
                this.RaisePropertyChanged("NewLoginPassword");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class SetLoyaltyPasswordRS : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string statusField;
        
        private ErrorType errorTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string Status {
            get {
                return this.statusField;
            }
            set {
                this.statusField = value;
                this.RaisePropertyChanged("Status");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public ErrorType ErrorType {
            get {
                return this.errorTypeField;
            }
            set {
                this.errorTypeField = value;
                this.RaisePropertyChanged("ErrorType");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class setLoyaltyPasswordRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.CheckLoyaltyNumberService.SetLoyaltyPasswordRQ SetLoyaltyPasswordRQ;
        
        public setLoyaltyPasswordRequest() {
        }
        
        public setLoyaltyPasswordRequest(Ubimecs.IBS.Services.CheckLoyaltyNumberService.SetLoyaltyPasswordRQ SetLoyaltyPasswordRQ) {
            this.SetLoyaltyPasswordRQ = SetLoyaltyPasswordRQ;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class setLoyaltyPasswordResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.CheckLoyaltyNumberService.SetLoyaltyPasswordRS SetLoyaltyPasswordRS;
        
        public setLoyaltyPasswordResponse() {
        }
        
        public setLoyaltyPasswordResponse(Ubimecs.IBS.Services.CheckLoyaltyNumberService.SetLoyaltyPasswordRS SetLoyaltyPasswordRS) {
            this.SetLoyaltyPasswordRS = SetLoyaltyPasswordRS;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class SubscribeAirlineNewsletterRQ : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string airlineCodeField;
        
        private BookingChannelKeyType bookingChannelField;
        
        private AirlineNewsletter airlineNewsletterField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AirlineCode {
            get {
                return this.airlineCodeField;
            }
            set {
                this.airlineCodeField = value;
                this.RaisePropertyChanged("AirlineCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public BookingChannelKeyType BookingChannel {
            get {
                return this.bookingChannelField;
            }
            set {
                this.bookingChannelField = value;
                this.RaisePropertyChanged("BookingChannel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public AirlineNewsletter AirlineNewsletter {
            get {
                return this.airlineNewsletterField;
            }
            set {
                this.airlineNewsletterField = value;
                this.RaisePropertyChanged("AirlineNewsletter");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class SubscribeAirlineNewsletterRS : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string statusField;
        
        private ErrorType errorTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string Status {
            get {
                return this.statusField;
            }
            set {
                this.statusField = value;
                this.RaisePropertyChanged("Status");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public ErrorType ErrorType {
            get {
                return this.errorTypeField;
            }
            set {
                this.errorTypeField = value;
                this.RaisePropertyChanged("ErrorType");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class subscribeAirlineNewsletterRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.CheckLoyaltyNumberService.SubscribeAirlineNewsletterRQ SubscribeAirlineNewsletterRQ;
        
        public subscribeAirlineNewsletterRequest() {
        }
        
        public subscribeAirlineNewsletterRequest(Ubimecs.IBS.Services.CheckLoyaltyNumberService.SubscribeAirlineNewsletterRQ SubscribeAirlineNewsletterRQ) {
            this.SubscribeAirlineNewsletterRQ = SubscribeAirlineNewsletterRQ;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class subscribeAirlineNewsletterResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.CheckLoyaltyNumberService.SubscribeAirlineNewsletterRS SubscribeAirlineNewsletterRS;
        
        public subscribeAirlineNewsletterResponse() {
        }
        
        public subscribeAirlineNewsletterResponse(Ubimecs.IBS.Services.CheckLoyaltyNumberService.SubscribeAirlineNewsletterRS SubscribeAirlineNewsletterRS) {
            this.SubscribeAirlineNewsletterRS = SubscribeAirlineNewsletterRS;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class ValidateLoyaltyRQ : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string airlineCodeField;
        
        private string loyaltyNumberField;
        
        private BookingChannelKeyType bookingChannelField;
        
        private string emailIDField;
        
        private System.DateTime dateofBirthField;
        
        private bool dateofBirthFieldSpecified;
        
        private string securityQuestionField;
        
        private string securityAnswerField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AirlineCode {
            get {
                return this.airlineCodeField;
            }
            set {
                this.airlineCodeField = value;
                this.RaisePropertyChanged("AirlineCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string LoyaltyNumber {
            get {
                return this.loyaltyNumberField;
            }
            set {
                this.loyaltyNumberField = value;
                this.RaisePropertyChanged("LoyaltyNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public BookingChannelKeyType BookingChannel {
            get {
                return this.bookingChannelField;
            }
            set {
                this.bookingChannelField = value;
                this.RaisePropertyChanged("BookingChannel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string EmailID {
            get {
                return this.emailIDField;
            }
            set {
                this.emailIDField = value;
                this.RaisePropertyChanged("EmailID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, DataType="date", Order=4)]
        public System.DateTime DateofBirth {
            get {
                return this.dateofBirthField;
            }
            set {
                this.dateofBirthField = value;
                this.RaisePropertyChanged("DateofBirth");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool DateofBirthSpecified {
            get {
                return this.dateofBirthFieldSpecified;
            }
            set {
                this.dateofBirthFieldSpecified = value;
                this.RaisePropertyChanged("DateofBirthSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string SecurityQuestion {
            get {
                return this.securityQuestionField;
            }
            set {
                this.securityQuestionField = value;
                this.RaisePropertyChanged("SecurityQuestion");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string SecurityAnswer {
            get {
                return this.securityAnswerField;
            }
            set {
                this.securityAnswerField = value;
                this.RaisePropertyChanged("SecurityAnswer");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class ValidateLoyaltyRS : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string statusField;
        
        private ErrorType errorTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string Status {
            get {
                return this.statusField;
            }
            set {
                this.statusField = value;
                this.RaisePropertyChanged("Status");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public ErrorType ErrorType {
            get {
                return this.errorTypeField;
            }
            set {
                this.errorTypeField = value;
                this.RaisePropertyChanged("ErrorType");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class validateLoyaltyRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.CheckLoyaltyNumberService.ValidateLoyaltyRQ ValidateLoyaltyRQ;
        
        public validateLoyaltyRequest() {
        }
        
        public validateLoyaltyRequest(Ubimecs.IBS.Services.CheckLoyaltyNumberService.ValidateLoyaltyRQ ValidateLoyaltyRQ) {
            this.ValidateLoyaltyRQ = ValidateLoyaltyRQ;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class validateLoyaltyResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.CheckLoyaltyNumberService.ValidateLoyaltyRS ValidateLoyaltyRS;
        
        public validateLoyaltyResponse() {
        }
        
        public validateLoyaltyResponse(Ubimecs.IBS.Services.CheckLoyaltyNumberService.ValidateLoyaltyRS ValidateLoyaltyRS) {
            this.ValidateLoyaltyRS = ValidateLoyaltyRS;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class ViewCustomerProfileRQ : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string profileIdField;
        
        private string airlineCodeField;
        
        private BookingChannelKeyType bookingChannelField;
        
        private string profileAliasField;
        
        private string externalProfileIDField;
        
        private System.DateTime lastSyncTimeField;
        
        private bool lastSyncTimeFieldSpecified;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string ProfileId {
            get {
                return this.profileIdField;
            }
            set {
                this.profileIdField = value;
                this.RaisePropertyChanged("ProfileId");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string AirlineCode {
            get {
                return this.airlineCodeField;
            }
            set {
                this.airlineCodeField = value;
                this.RaisePropertyChanged("AirlineCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public BookingChannelKeyType BookingChannel {
            get {
                return this.bookingChannelField;
            }
            set {
                this.bookingChannelField = value;
                this.RaisePropertyChanged("BookingChannel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string ProfileAlias {
            get {
                return this.profileAliasField;
            }
            set {
                this.profileAliasField = value;
                this.RaisePropertyChanged("ProfileAlias");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string ExternalProfileID {
            get {
                return this.externalProfileIDField;
            }
            set {
                this.externalProfileIDField = value;
                this.RaisePropertyChanged("ExternalProfileID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public System.DateTime LastSyncTime {
            get {
                return this.lastSyncTimeField;
            }
            set {
                this.lastSyncTimeField = value;
                this.RaisePropertyChanged("LastSyncTime");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool LastSyncTimeSpecified {
            get {
                return this.lastSyncTimeFieldSpecified;
            }
            set {
                this.lastSyncTimeFieldSpecified = value;
                this.RaisePropertyChanged("LastSyncTimeSpecified");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class ViewCustomerProfileRS : object, System.ComponentModel.INotifyPropertyChanged {
        
        private ViewCustomerProfileDetails[] viewCustomerProfileDetailsField;
        
        private ErrorType errorTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("ViewCustomerProfileDetails", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public ViewCustomerProfileDetails[] ViewCustomerProfileDetails {
            get {
                return this.viewCustomerProfileDetailsField;
            }
            set {
                this.viewCustomerProfileDetailsField = value;
                this.RaisePropertyChanged("ViewCustomerProfileDetails");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public ErrorType ErrorType {
            get {
                return this.errorTypeField;
            }
            set {
                this.errorTypeField = value;
                this.RaisePropertyChanged("ErrorType");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class viewCustomerProfileRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.CheckLoyaltyNumberService.ViewCustomerProfileRQ ViewCustomerProfileRQ;
        
        public viewCustomerProfileRequest() {
        }
        
        public viewCustomerProfileRequest(Ubimecs.IBS.Services.CheckLoyaltyNumberService.ViewCustomerProfileRQ ViewCustomerProfileRQ) {
            this.ViewCustomerProfileRQ = ViewCustomerProfileRQ;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class viewCustomerProfileResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.CheckLoyaltyNumberService.ViewCustomerProfileRS ViewCustomerProfileRS;
        
        public viewCustomerProfileResponse() {
        }
        
        public viewCustomerProfileResponse(Ubimecs.IBS.Services.CheckLoyaltyNumberService.ViewCustomerProfileRS ViewCustomerProfileRS) {
            this.ViewCustomerProfileRS = ViewCustomerProfileRS;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class ViewCustomerProfileViaWebRQ : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string loyaltyNumberField;
        
        private string airlineCodeField;
        
        private BookingChannelKeyType bookingChannelField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string LoyaltyNumber {
            get {
                return this.loyaltyNumberField;
            }
            set {
                this.loyaltyNumberField = value;
                this.RaisePropertyChanged("LoyaltyNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string AirlineCode {
            get {
                return this.airlineCodeField;
            }
            set {
                this.airlineCodeField = value;
                this.RaisePropertyChanged("AirlineCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public BookingChannelKeyType BookingChannel {
            get {
                return this.bookingChannelField;
            }
            set {
                this.bookingChannelField = value;
                this.RaisePropertyChanged("BookingChannel");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class ViewCustomerProfileViaWebRS : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string profileAliasField;
        
        private string profileIdField;
        
        private string groupNameField;
        
        private string groupTypeField;
        
        private string groupLeaderFirstNameField;
        
        private string groupLeaderLastNameField;
        
        private string profileTypeField;
        
        private GuestDetails[] guestDetailsField;
        
        private CustomerProfileContactDetails homeContactField;
        
        private CustomerProfileContactDetails businessContactField;
        
        private CustomerProfileContactDetails alternateContactField;
        
        private string viewModeField;
        
        private ErrorType errorTypeField;
        
        private FeeWaiveOrOverrideDetails[] feeWaiveOrOverrideDetailsField;
        
        private CustomerProfileContactDetails localLanguageContactField;
        
        private CustomerProfileContactDetails[] paxSpecificContactsField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string ProfileAlias {
            get {
                return this.profileAliasField;
            }
            set {
                this.profileAliasField = value;
                this.RaisePropertyChanged("ProfileAlias");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string ProfileId {
            get {
                return this.profileIdField;
            }
            set {
                this.profileIdField = value;
                this.RaisePropertyChanged("ProfileId");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string GroupName {
            get {
                return this.groupNameField;
            }
            set {
                this.groupNameField = value;
                this.RaisePropertyChanged("GroupName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string GroupType {
            get {
                return this.groupTypeField;
            }
            set {
                this.groupTypeField = value;
                this.RaisePropertyChanged("GroupType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string GroupLeaderFirstName {
            get {
                return this.groupLeaderFirstNameField;
            }
            set {
                this.groupLeaderFirstNameField = value;
                this.RaisePropertyChanged("GroupLeaderFirstName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string GroupLeaderLastName {
            get {
                return this.groupLeaderLastNameField;
            }
            set {
                this.groupLeaderLastNameField = value;
                this.RaisePropertyChanged("GroupLeaderLastName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string ProfileType {
            get {
                return this.profileTypeField;
            }
            set {
                this.profileTypeField = value;
                this.RaisePropertyChanged("ProfileType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("GuestDetails", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public GuestDetails[] GuestDetails {
            get {
                return this.guestDetailsField;
            }
            set {
                this.guestDetailsField = value;
                this.RaisePropertyChanged("GuestDetails");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public CustomerProfileContactDetails HomeContact {
            get {
                return this.homeContactField;
            }
            set {
                this.homeContactField = value;
                this.RaisePropertyChanged("HomeContact");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public CustomerProfileContactDetails BusinessContact {
            get {
                return this.businessContactField;
            }
            set {
                this.businessContactField = value;
                this.RaisePropertyChanged("BusinessContact");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=10)]
        public CustomerProfileContactDetails AlternateContact {
            get {
                return this.alternateContactField;
            }
            set {
                this.alternateContactField = value;
                this.RaisePropertyChanged("AlternateContact");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=11)]
        public string ViewMode {
            get {
                return this.viewModeField;
            }
            set {
                this.viewModeField = value;
                this.RaisePropertyChanged("ViewMode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=12)]
        public ErrorType ErrorType {
            get {
                return this.errorTypeField;
            }
            set {
                this.errorTypeField = value;
                this.RaisePropertyChanged("ErrorType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("FeeWaiveOrOverrideDetails", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=13)]
        public FeeWaiveOrOverrideDetails[] FeeWaiveOrOverrideDetails {
            get {
                return this.feeWaiveOrOverrideDetailsField;
            }
            set {
                this.feeWaiveOrOverrideDetailsField = value;
                this.RaisePropertyChanged("FeeWaiveOrOverrideDetails");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=14)]
        public CustomerProfileContactDetails LocalLanguageContact {
            get {
                return this.localLanguageContactField;
            }
            set {
                this.localLanguageContactField = value;
                this.RaisePropertyChanged("LocalLanguageContact");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("PaxSpecificContacts", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=15)]
        public CustomerProfileContactDetails[] PaxSpecificContacts {
            get {
                return this.paxSpecificContactsField;
            }
            set {
                this.paxSpecificContactsField = value;
                this.RaisePropertyChanged("PaxSpecificContacts");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class viewCustomerProfileViaWebRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.CheckLoyaltyNumberService.ViewCustomerProfileViaWebRQ ViewCustomerProfileViaWebRQ;
        
        public viewCustomerProfileViaWebRequest() {
        }
        
        public viewCustomerProfileViaWebRequest(Ubimecs.IBS.Services.CheckLoyaltyNumberService.ViewCustomerProfileViaWebRQ ViewCustomerProfileViaWebRQ) {
            this.ViewCustomerProfileViaWebRQ = ViewCustomerProfileViaWebRQ;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class viewCustomerProfileViaWebResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.CheckLoyaltyNumberService.ViewCustomerProfileViaWebRS ViewCustomerProfileViaWebRS;
        
        public viewCustomerProfileViaWebResponse() {
        }
        
        public viewCustomerProfileViaWebResponse(Ubimecs.IBS.Services.CheckLoyaltyNumberService.ViewCustomerProfileViaWebRS ViewCustomerProfileViaWebRS) {
            this.ViewCustomerProfileViaWebRS = ViewCustomerProfileViaWebRS;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class ValidateDuplicateCustomerProfileRQ : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string airlineCodeField;
        
        private BookingChannelKeyType bookingChannelField;
        
        private string profileAliasField;
        
        private string profileTypeField;
        
        private GuestDetails[] guestDetailsField;
        
        private CustomerProfileContactDetails homeContactField;
        
        private CustomerProfileContactDetails businessContactField;
        
        private CustomerProfileContactDetails alternateContactField;
        
        private CustomerProfileContactDetails[] paxSpecificContactsField;
        
        private string viewModeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AirlineCode {
            get {
                return this.airlineCodeField;
            }
            set {
                this.airlineCodeField = value;
                this.RaisePropertyChanged("AirlineCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public BookingChannelKeyType BookingChannel {
            get {
                return this.bookingChannelField;
            }
            set {
                this.bookingChannelField = value;
                this.RaisePropertyChanged("BookingChannel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string ProfileAlias {
            get {
                return this.profileAliasField;
            }
            set {
                this.profileAliasField = value;
                this.RaisePropertyChanged("ProfileAlias");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string ProfileType {
            get {
                return this.profileTypeField;
            }
            set {
                this.profileTypeField = value;
                this.RaisePropertyChanged("ProfileType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("GuestDetails", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public GuestDetails[] GuestDetails {
            get {
                return this.guestDetailsField;
            }
            set {
                this.guestDetailsField = value;
                this.RaisePropertyChanged("GuestDetails");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public CustomerProfileContactDetails HomeContact {
            get {
                return this.homeContactField;
            }
            set {
                this.homeContactField = value;
                this.RaisePropertyChanged("HomeContact");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public CustomerProfileContactDetails BusinessContact {
            get {
                return this.businessContactField;
            }
            set {
                this.businessContactField = value;
                this.RaisePropertyChanged("BusinessContact");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public CustomerProfileContactDetails AlternateContact {
            get {
                return this.alternateContactField;
            }
            set {
                this.alternateContactField = value;
                this.RaisePropertyChanged("AlternateContact");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("PaxSpecificContacts", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public CustomerProfileContactDetails[] PaxSpecificContacts {
            get {
                return this.paxSpecificContactsField;
            }
            set {
                this.paxSpecificContactsField = value;
                this.RaisePropertyChanged("PaxSpecificContacts");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public string ViewMode {
            get {
                return this.viewModeField;
            }
            set {
                this.viewModeField = value;
                this.RaisePropertyChanged("ViewMode");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class ValidateDuplicateCustomerProfileRS : object, System.ComponentModel.INotifyPropertyChanged {
        
        private DupeProfileDetails[] dupeProfileDetailsField;
        
        private ErrorType errorTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("DupeProfileDetails", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public DupeProfileDetails[] DupeProfileDetails {
            get {
                return this.dupeProfileDetailsField;
            }
            set {
                this.dupeProfileDetailsField = value;
                this.RaisePropertyChanged("DupeProfileDetails");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public ErrorType ErrorType {
            get {
                return this.errorTypeField;
            }
            set {
                this.errorTypeField = value;
                this.RaisePropertyChanged("ErrorType");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class validateDuplicateCustomerProfileRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.CheckLoyaltyNumberService.ValidateDuplicateCustomerProfileRQ ValidateDuplicateCustomerProfileRQ;
        
        public validateDuplicateCustomerProfileRequest() {
        }
        
        public validateDuplicateCustomerProfileRequest(Ubimecs.IBS.Services.CheckLoyaltyNumberService.ValidateDuplicateCustomerProfileRQ ValidateDuplicateCustomerProfileRQ) {
            this.ValidateDuplicateCustomerProfileRQ = ValidateDuplicateCustomerProfileRQ;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class validateDuplicateCustomerProfileResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.CheckLoyaltyNumberService.ValidateDuplicateCustomerProfileRS ValidateDuplicateCustomerProfileRS;
        
        public validateDuplicateCustomerProfileResponse() {
        }
        
        public validateDuplicateCustomerProfileResponse(Ubimecs.IBS.Services.CheckLoyaltyNumberService.ValidateDuplicateCustomerProfileRS ValidateDuplicateCustomerProfileRS) {
            this.ValidateDuplicateCustomerProfileRS = ValidateDuplicateCustomerProfileRS;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class ValidateCustomerProfileListRQ : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string airlineCodeField;
        
        private BookingChannelKeyType bookingChannelField;
        
        private CustomerProfileDetailsType[] customerProfileDetailsField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AirlineCode {
            get {
                return this.airlineCodeField;
            }
            set {
                this.airlineCodeField = value;
                this.RaisePropertyChanged("AirlineCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public BookingChannelKeyType BookingChannel {
            get {
                return this.bookingChannelField;
            }
            set {
                this.bookingChannelField = value;
                this.RaisePropertyChanged("BookingChannel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("CustomerProfileDetails", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public CustomerProfileDetailsType[] CustomerProfileDetails {
            get {
                return this.customerProfileDetailsField;
            }
            set {
                this.customerProfileDetailsField = value;
                this.RaisePropertyChanged("CustomerProfileDetails");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class ValidateCustomerProfileListRS : object, System.ComponentModel.INotifyPropertyChanged {
        
        private CustomerProfileValidateResp[] customerProfileDetailsField;
        
        private ErrorType errorTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("CustomerProfileDetails", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public CustomerProfileValidateResp[] CustomerProfileDetails {
            get {
                return this.customerProfileDetailsField;
            }
            set {
                this.customerProfileDetailsField = value;
                this.RaisePropertyChanged("CustomerProfileDetails");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public ErrorType ErrorType {
            get {
                return this.errorTypeField;
            }
            set {
                this.errorTypeField = value;
                this.RaisePropertyChanged("ErrorType");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class validateCustomerProfileListRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.CheckLoyaltyNumberService.ValidateCustomerProfileListRQ ValidateCustomerProfileListRQ;
        
        public validateCustomerProfileListRequest() {
        }
        
        public validateCustomerProfileListRequest(Ubimecs.IBS.Services.CheckLoyaltyNumberService.ValidateCustomerProfileListRQ ValidateCustomerProfileListRQ) {
            this.ValidateCustomerProfileListRQ = ValidateCustomerProfileListRQ;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class validateCustomerProfileListResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.CheckLoyaltyNumberService.ValidateCustomerProfileListRS ValidateCustomerProfileListRS;
        
        public validateCustomerProfileListResponse() {
        }
        
        public validateCustomerProfileListResponse(Ubimecs.IBS.Services.CheckLoyaltyNumberService.ValidateCustomerProfileListRS ValidateCustomerProfileListRS) {
            this.ValidateCustomerProfileListRS = ValidateCustomerProfileListRS;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class ActivateCustomerProfileLoginRQ : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string airlineCodeField;
        
        private string profileIdField;
        
        private string userNameField;
        
        private string linkExpiryDateField;
        
        private string activationProfileField;
        
        private BookingChannelKeyType bookingChannelField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AirlineCode {
            get {
                return this.airlineCodeField;
            }
            set {
                this.airlineCodeField = value;
                this.RaisePropertyChanged("AirlineCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string ProfileId {
            get {
                return this.profileIdField;
            }
            set {
                this.profileIdField = value;
                this.RaisePropertyChanged("ProfileId");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string UserName {
            get {
                return this.userNameField;
            }
            set {
                this.userNameField = value;
                this.RaisePropertyChanged("UserName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string LinkExpiryDate {
            get {
                return this.linkExpiryDateField;
            }
            set {
                this.linkExpiryDateField = value;
                this.RaisePropertyChanged("LinkExpiryDate");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string ActivationProfile {
            get {
                return this.activationProfileField;
            }
            set {
                this.activationProfileField = value;
                this.RaisePropertyChanged("ActivationProfile");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public BookingChannelKeyType BookingChannel {
            get {
                return this.bookingChannelField;
            }
            set {
                this.bookingChannelField = value;
                this.RaisePropertyChanged("BookingChannel");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class ActivateCustomerProfileLoginRS : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string airlineCodeField;
        
        private string statusField;
        
        private ErrorType errorTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AirlineCode {
            get {
                return this.airlineCodeField;
            }
            set {
                this.airlineCodeField = value;
                this.RaisePropertyChanged("AirlineCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string Status {
            get {
                return this.statusField;
            }
            set {
                this.statusField = value;
                this.RaisePropertyChanged("Status");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public ErrorType ErrorType {
            get {
                return this.errorTypeField;
            }
            set {
                this.errorTypeField = value;
                this.RaisePropertyChanged("ErrorType");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class activateCustomerProfileLoginRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.CheckLoyaltyNumberService.ActivateCustomerProfileLoginRQ ActivateCustomerProfileLoginRQ;
        
        public activateCustomerProfileLoginRequest() {
        }
        
        public activateCustomerProfileLoginRequest(Ubimecs.IBS.Services.CheckLoyaltyNumberService.ActivateCustomerProfileLoginRQ ActivateCustomerProfileLoginRQ) {
            this.ActivateCustomerProfileLoginRQ = ActivateCustomerProfileLoginRQ;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class activateCustomerProfileLoginResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.CheckLoyaltyNumberService.ActivateCustomerProfileLoginRS ActivateCustomerProfileLoginRS;
        
        public activateCustomerProfileLoginResponse() {
        }
        
        public activateCustomerProfileLoginResponse(Ubimecs.IBS.Services.CheckLoyaltyNumberService.ActivateCustomerProfileLoginRS ActivateCustomerProfileLoginRS) {
            this.ActivateCustomerProfileLoginRS = ActivateCustomerProfileLoginRS;
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    public interface CustomerProfilePortChannel : Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort, System.ServiceModel.IClientChannel {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    public partial class CustomerProfilePortClient : System.ServiceModel.ClientBase<Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort>, Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort {
        
        public CustomerProfilePortClient() {
        }
        
        public CustomerProfilePortClient(string endpointConfigurationName) : 
                base(endpointConfigurationName) {
        }
        
        public CustomerProfilePortClient(string endpointConfigurationName, string remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public CustomerProfilePortClient(string endpointConfigurationName, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public CustomerProfilePortClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress) {
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Ubimecs.IBS.Services.CheckLoyaltyNumberService.createCustomerProfileResponse Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort.createCustomerProfile(Ubimecs.IBS.Services.CheckLoyaltyNumberService.createCustomerProfileRequest request) {
            return base.Channel.createCustomerProfile(request);
        }
        
        public Ubimecs.IBS.Services.CheckLoyaltyNumberService.CreateCustomerProfileRS createCustomerProfile(Ubimecs.IBS.Services.CheckLoyaltyNumberService.CreateCustomerProfileRQ CreateCustomerProfileRQ) {
            Ubimecs.IBS.Services.CheckLoyaltyNumberService.createCustomerProfileRequest inValue = new Ubimecs.IBS.Services.CheckLoyaltyNumberService.createCustomerProfileRequest();
            inValue.CreateCustomerProfileRQ = CreateCustomerProfileRQ;
            Ubimecs.IBS.Services.CheckLoyaltyNumberService.createCustomerProfileResponse retVal = ((Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort)(this)).createCustomerProfile(inValue);
            return retVal.CreateCustomerProfileRS;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.CheckLoyaltyNumberService.createCustomerProfileResponse> Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort.createCustomerProfileAsync(Ubimecs.IBS.Services.CheckLoyaltyNumberService.createCustomerProfileRequest request) {
            return base.Channel.createCustomerProfileAsync(request);
        }
        
        public System.Threading.Tasks.Task<Ubimecs.IBS.Services.CheckLoyaltyNumberService.createCustomerProfileResponse> createCustomerProfileAsync(Ubimecs.IBS.Services.CheckLoyaltyNumberService.CreateCustomerProfileRQ CreateCustomerProfileRQ) {
            Ubimecs.IBS.Services.CheckLoyaltyNumberService.createCustomerProfileRequest inValue = new Ubimecs.IBS.Services.CheckLoyaltyNumberService.createCustomerProfileRequest();
            inValue.CreateCustomerProfileRQ = CreateCustomerProfileRQ;
            return ((Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort)(this)).createCustomerProfileAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Ubimecs.IBS.Services.CheckLoyaltyNumberService.retrieveCustomerBookedListResponse Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort.retrieveCustomerBookedList(Ubimecs.IBS.Services.CheckLoyaltyNumberService.retrieveCustomerBookedListRequest request) {
            return base.Channel.retrieveCustomerBookedList(request);
        }
        
        public Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerBookedListRS retrieveCustomerBookedList(Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerBookedListRQ CustomerBookedListRQ) {
            Ubimecs.IBS.Services.CheckLoyaltyNumberService.retrieveCustomerBookedListRequest inValue = new Ubimecs.IBS.Services.CheckLoyaltyNumberService.retrieveCustomerBookedListRequest();
            inValue.CustomerBookedListRQ = CustomerBookedListRQ;
            Ubimecs.IBS.Services.CheckLoyaltyNumberService.retrieveCustomerBookedListResponse retVal = ((Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort)(this)).retrieveCustomerBookedList(inValue);
            return retVal.CustomerBookedListRS;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.CheckLoyaltyNumberService.retrieveCustomerBookedListResponse> Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort.retrieveCustomerBookedListAsync(Ubimecs.IBS.Services.CheckLoyaltyNumberService.retrieveCustomerBookedListRequest request) {
            return base.Channel.retrieveCustomerBookedListAsync(request);
        }
        
        public System.Threading.Tasks.Task<Ubimecs.IBS.Services.CheckLoyaltyNumberService.retrieveCustomerBookedListResponse> retrieveCustomerBookedListAsync(Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerBookedListRQ CustomerBookedListRQ) {
            Ubimecs.IBS.Services.CheckLoyaltyNumberService.retrieveCustomerBookedListRequest inValue = new Ubimecs.IBS.Services.CheckLoyaltyNumberService.retrieveCustomerBookedListRequest();
            inValue.CustomerBookedListRQ = CustomerBookedListRQ;
            return ((Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort)(this)).retrieveCustomerBookedListAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Ubimecs.IBS.Services.CheckLoyaltyNumberService.deactivateCustomerProfileResponse Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort.deactivateCustomerProfile(Ubimecs.IBS.Services.CheckLoyaltyNumberService.deactivateCustomerProfileRequest request) {
            return base.Channel.deactivateCustomerProfile(request);
        }
        
        public Ubimecs.IBS.Services.CheckLoyaltyNumberService.DeactivateCustomerProfileRS deactivateCustomerProfile(Ubimecs.IBS.Services.CheckLoyaltyNumberService.DeactivateCustomerProfileRQ DeactivateCustomerProfileRQ) {
            Ubimecs.IBS.Services.CheckLoyaltyNumberService.deactivateCustomerProfileRequest inValue = new Ubimecs.IBS.Services.CheckLoyaltyNumberService.deactivateCustomerProfileRequest();
            inValue.DeactivateCustomerProfileRQ = DeactivateCustomerProfileRQ;
            Ubimecs.IBS.Services.CheckLoyaltyNumberService.deactivateCustomerProfileResponse retVal = ((Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort)(this)).deactivateCustomerProfile(inValue);
            return retVal.DeactivateCustomerProfileRS;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.CheckLoyaltyNumberService.deactivateCustomerProfileResponse> Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort.deactivateCustomerProfileAsync(Ubimecs.IBS.Services.CheckLoyaltyNumberService.deactivateCustomerProfileRequest request) {
            return base.Channel.deactivateCustomerProfileAsync(request);
        }
        
        public System.Threading.Tasks.Task<Ubimecs.IBS.Services.CheckLoyaltyNumberService.deactivateCustomerProfileResponse> deactivateCustomerProfileAsync(Ubimecs.IBS.Services.CheckLoyaltyNumberService.DeactivateCustomerProfileRQ DeactivateCustomerProfileRQ) {
            Ubimecs.IBS.Services.CheckLoyaltyNumberService.deactivateCustomerProfileRequest inValue = new Ubimecs.IBS.Services.CheckLoyaltyNumberService.deactivateCustomerProfileRequest();
            inValue.DeactivateCustomerProfileRQ = DeactivateCustomerProfileRQ;
            return ((Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort)(this)).deactivateCustomerProfileAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Ubimecs.IBS.Services.CheckLoyaltyNumberService.getNewsLetterSubscriptionInfoResponse Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort.getNewsLetterSubscriptionInfo(Ubimecs.IBS.Services.CheckLoyaltyNumberService.getNewsLetterSubscriptionInfoRequest request) {
            return base.Channel.getNewsLetterSubscriptionInfo(request);
        }
        
        public Ubimecs.IBS.Services.CheckLoyaltyNumberService.GetNewsLetterSubscriptionInfoRS getNewsLetterSubscriptionInfo(Ubimecs.IBS.Services.CheckLoyaltyNumberService.GetNewsLetterSubscriptionInfoRQ GetNewsLetterSubscriptionInfoRQ) {
            Ubimecs.IBS.Services.CheckLoyaltyNumberService.getNewsLetterSubscriptionInfoRequest inValue = new Ubimecs.IBS.Services.CheckLoyaltyNumberService.getNewsLetterSubscriptionInfoRequest();
            inValue.GetNewsLetterSubscriptionInfoRQ = GetNewsLetterSubscriptionInfoRQ;
            Ubimecs.IBS.Services.CheckLoyaltyNumberService.getNewsLetterSubscriptionInfoResponse retVal = ((Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort)(this)).getNewsLetterSubscriptionInfo(inValue);
            return retVal.GetNewsLetterSubscriptionInfoRS;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.CheckLoyaltyNumberService.getNewsLetterSubscriptionInfoResponse> Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort.getNewsLetterSubscriptionInfoAsync(Ubimecs.IBS.Services.CheckLoyaltyNumberService.getNewsLetterSubscriptionInfoRequest request) {
            return base.Channel.getNewsLetterSubscriptionInfoAsync(request);
        }
        
        public System.Threading.Tasks.Task<Ubimecs.IBS.Services.CheckLoyaltyNumberService.getNewsLetterSubscriptionInfoResponse> getNewsLetterSubscriptionInfoAsync(Ubimecs.IBS.Services.CheckLoyaltyNumberService.GetNewsLetterSubscriptionInfoRQ GetNewsLetterSubscriptionInfoRQ) {
            Ubimecs.IBS.Services.CheckLoyaltyNumberService.getNewsLetterSubscriptionInfoRequest inValue = new Ubimecs.IBS.Services.CheckLoyaltyNumberService.getNewsLetterSubscriptionInfoRequest();
            inValue.GetNewsLetterSubscriptionInfoRQ = GetNewsLetterSubscriptionInfoRQ;
            return ((Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort)(this)).getNewsLetterSubscriptionInfoAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Ubimecs.IBS.Services.CheckLoyaltyNumberService.listCustomerProfileResponse Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort.listCustomerProfile(Ubimecs.IBS.Services.CheckLoyaltyNumberService.listCustomerProfileRequest request) {
            return base.Channel.listCustomerProfile(request);
        }
        
        public Ubimecs.IBS.Services.CheckLoyaltyNumberService.ListCustomerProfileRS listCustomerProfile(Ubimecs.IBS.Services.CheckLoyaltyNumberService.ListCustomerProfileRQ ListCustomerProfileRQ) {
            Ubimecs.IBS.Services.CheckLoyaltyNumberService.listCustomerProfileRequest inValue = new Ubimecs.IBS.Services.CheckLoyaltyNumberService.listCustomerProfileRequest();
            inValue.ListCustomerProfileRQ = ListCustomerProfileRQ;
            Ubimecs.IBS.Services.CheckLoyaltyNumberService.listCustomerProfileResponse retVal = ((Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort)(this)).listCustomerProfile(inValue);
            return retVal.ListCustomerProfileRS;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.CheckLoyaltyNumberService.listCustomerProfileResponse> Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort.listCustomerProfileAsync(Ubimecs.IBS.Services.CheckLoyaltyNumberService.listCustomerProfileRequest request) {
            return base.Channel.listCustomerProfileAsync(request);
        }
        
        public System.Threading.Tasks.Task<Ubimecs.IBS.Services.CheckLoyaltyNumberService.listCustomerProfileResponse> listCustomerProfileAsync(Ubimecs.IBS.Services.CheckLoyaltyNumberService.ListCustomerProfileRQ ListCustomerProfileRQ) {
            Ubimecs.IBS.Services.CheckLoyaltyNumberService.listCustomerProfileRequest inValue = new Ubimecs.IBS.Services.CheckLoyaltyNumberService.listCustomerProfileRequest();
            inValue.ListCustomerProfileRQ = ListCustomerProfileRQ;
            return ((Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort)(this)).listCustomerProfileAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Ubimecs.IBS.Services.CheckLoyaltyNumberService.modifyCustomerProfileResponse Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort.modifyCustomerProfile(Ubimecs.IBS.Services.CheckLoyaltyNumberService.modifyCustomerProfileRequest request) {
            return base.Channel.modifyCustomerProfile(request);
        }
        
        public Ubimecs.IBS.Services.CheckLoyaltyNumberService.ModifyCustomerProfileRS modifyCustomerProfile(Ubimecs.IBS.Services.CheckLoyaltyNumberService.ModifyCustomerProfileRQ ModifyCustomerProfileRQ) {
            Ubimecs.IBS.Services.CheckLoyaltyNumberService.modifyCustomerProfileRequest inValue = new Ubimecs.IBS.Services.CheckLoyaltyNumberService.modifyCustomerProfileRequest();
            inValue.ModifyCustomerProfileRQ = ModifyCustomerProfileRQ;
            Ubimecs.IBS.Services.CheckLoyaltyNumberService.modifyCustomerProfileResponse retVal = ((Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort)(this)).modifyCustomerProfile(inValue);
            return retVal.ModifyCustomerProfileRS;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.CheckLoyaltyNumberService.modifyCustomerProfileResponse> Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort.modifyCustomerProfileAsync(Ubimecs.IBS.Services.CheckLoyaltyNumberService.modifyCustomerProfileRequest request) {
            return base.Channel.modifyCustomerProfileAsync(request);
        }
        
        public System.Threading.Tasks.Task<Ubimecs.IBS.Services.CheckLoyaltyNumberService.modifyCustomerProfileResponse> modifyCustomerProfileAsync(Ubimecs.IBS.Services.CheckLoyaltyNumberService.ModifyCustomerProfileRQ ModifyCustomerProfileRQ) {
            Ubimecs.IBS.Services.CheckLoyaltyNumberService.modifyCustomerProfileRequest inValue = new Ubimecs.IBS.Services.CheckLoyaltyNumberService.modifyCustomerProfileRequest();
            inValue.ModifyCustomerProfileRQ = ModifyCustomerProfileRQ;
            return ((Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort)(this)).modifyCustomerProfileAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Ubimecs.IBS.Services.CheckLoyaltyNumberService.resetLoyaltyPasswordResponse Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort.resetLoyaltyPassword(Ubimecs.IBS.Services.CheckLoyaltyNumberService.resetLoyaltyPasswordRequest request) {
            return base.Channel.resetLoyaltyPassword(request);
        }
        
        public Ubimecs.IBS.Services.CheckLoyaltyNumberService.ResetLoyaltyPasswordRS resetLoyaltyPassword(Ubimecs.IBS.Services.CheckLoyaltyNumberService.ResetLoyaltyPasswordRQ ResetLoyaltyPasswordRQ) {
            Ubimecs.IBS.Services.CheckLoyaltyNumberService.resetLoyaltyPasswordRequest inValue = new Ubimecs.IBS.Services.CheckLoyaltyNumberService.resetLoyaltyPasswordRequest();
            inValue.ResetLoyaltyPasswordRQ = ResetLoyaltyPasswordRQ;
            Ubimecs.IBS.Services.CheckLoyaltyNumberService.resetLoyaltyPasswordResponse retVal = ((Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort)(this)).resetLoyaltyPassword(inValue);
            return retVal.ResetLoyaltyPasswordRS;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.CheckLoyaltyNumberService.resetLoyaltyPasswordResponse> Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort.resetLoyaltyPasswordAsync(Ubimecs.IBS.Services.CheckLoyaltyNumberService.resetLoyaltyPasswordRequest request) {
            return base.Channel.resetLoyaltyPasswordAsync(request);
        }
        
        public System.Threading.Tasks.Task<Ubimecs.IBS.Services.CheckLoyaltyNumberService.resetLoyaltyPasswordResponse> resetLoyaltyPasswordAsync(Ubimecs.IBS.Services.CheckLoyaltyNumberService.ResetLoyaltyPasswordRQ ResetLoyaltyPasswordRQ) {
            Ubimecs.IBS.Services.CheckLoyaltyNumberService.resetLoyaltyPasswordRequest inValue = new Ubimecs.IBS.Services.CheckLoyaltyNumberService.resetLoyaltyPasswordRequest();
            inValue.ResetLoyaltyPasswordRQ = ResetLoyaltyPasswordRQ;
            return ((Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort)(this)).resetLoyaltyPasswordAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Ubimecs.IBS.Services.CheckLoyaltyNumberService.setLoyaltyPasswordResponse Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort.setLoyaltyPassword(Ubimecs.IBS.Services.CheckLoyaltyNumberService.setLoyaltyPasswordRequest request) {
            return base.Channel.setLoyaltyPassword(request);
        }
        
        public Ubimecs.IBS.Services.CheckLoyaltyNumberService.SetLoyaltyPasswordRS setLoyaltyPassword(Ubimecs.IBS.Services.CheckLoyaltyNumberService.SetLoyaltyPasswordRQ SetLoyaltyPasswordRQ) {
            Ubimecs.IBS.Services.CheckLoyaltyNumberService.setLoyaltyPasswordRequest inValue = new Ubimecs.IBS.Services.CheckLoyaltyNumberService.setLoyaltyPasswordRequest();
            inValue.SetLoyaltyPasswordRQ = SetLoyaltyPasswordRQ;
            Ubimecs.IBS.Services.CheckLoyaltyNumberService.setLoyaltyPasswordResponse retVal = ((Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort)(this)).setLoyaltyPassword(inValue);
            return retVal.SetLoyaltyPasswordRS;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.CheckLoyaltyNumberService.setLoyaltyPasswordResponse> Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort.setLoyaltyPasswordAsync(Ubimecs.IBS.Services.CheckLoyaltyNumberService.setLoyaltyPasswordRequest request) {
            return base.Channel.setLoyaltyPasswordAsync(request);
        }
        
        public System.Threading.Tasks.Task<Ubimecs.IBS.Services.CheckLoyaltyNumberService.setLoyaltyPasswordResponse> setLoyaltyPasswordAsync(Ubimecs.IBS.Services.CheckLoyaltyNumberService.SetLoyaltyPasswordRQ SetLoyaltyPasswordRQ) {
            Ubimecs.IBS.Services.CheckLoyaltyNumberService.setLoyaltyPasswordRequest inValue = new Ubimecs.IBS.Services.CheckLoyaltyNumberService.setLoyaltyPasswordRequest();
            inValue.SetLoyaltyPasswordRQ = SetLoyaltyPasswordRQ;
            return ((Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort)(this)).setLoyaltyPasswordAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Ubimecs.IBS.Services.CheckLoyaltyNumberService.subscribeAirlineNewsletterResponse Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort.subscribeAirlineNewsletter(Ubimecs.IBS.Services.CheckLoyaltyNumberService.subscribeAirlineNewsletterRequest request) {
            return base.Channel.subscribeAirlineNewsletter(request);
        }
        
        public Ubimecs.IBS.Services.CheckLoyaltyNumberService.SubscribeAirlineNewsletterRS subscribeAirlineNewsletter(Ubimecs.IBS.Services.CheckLoyaltyNumberService.SubscribeAirlineNewsletterRQ SubscribeAirlineNewsletterRQ) {
            Ubimecs.IBS.Services.CheckLoyaltyNumberService.subscribeAirlineNewsletterRequest inValue = new Ubimecs.IBS.Services.CheckLoyaltyNumberService.subscribeAirlineNewsletterRequest();
            inValue.SubscribeAirlineNewsletterRQ = SubscribeAirlineNewsletterRQ;
            Ubimecs.IBS.Services.CheckLoyaltyNumberService.subscribeAirlineNewsletterResponse retVal = ((Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort)(this)).subscribeAirlineNewsletter(inValue);
            return retVal.SubscribeAirlineNewsletterRS;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.CheckLoyaltyNumberService.subscribeAirlineNewsletterResponse> Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort.subscribeAirlineNewsletterAsync(Ubimecs.IBS.Services.CheckLoyaltyNumberService.subscribeAirlineNewsletterRequest request) {
            return base.Channel.subscribeAirlineNewsletterAsync(request);
        }
        
        public System.Threading.Tasks.Task<Ubimecs.IBS.Services.CheckLoyaltyNumberService.subscribeAirlineNewsletterResponse> subscribeAirlineNewsletterAsync(Ubimecs.IBS.Services.CheckLoyaltyNumberService.SubscribeAirlineNewsletterRQ SubscribeAirlineNewsletterRQ) {
            Ubimecs.IBS.Services.CheckLoyaltyNumberService.subscribeAirlineNewsletterRequest inValue = new Ubimecs.IBS.Services.CheckLoyaltyNumberService.subscribeAirlineNewsletterRequest();
            inValue.SubscribeAirlineNewsletterRQ = SubscribeAirlineNewsletterRQ;
            return ((Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort)(this)).subscribeAirlineNewsletterAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Ubimecs.IBS.Services.CheckLoyaltyNumberService.validateLoyaltyResponse Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort.validateLoyalty(Ubimecs.IBS.Services.CheckLoyaltyNumberService.validateLoyaltyRequest request) {
            return base.Channel.validateLoyalty(request);
        }
        
        public Ubimecs.IBS.Services.CheckLoyaltyNumberService.ValidateLoyaltyRS validateLoyalty(Ubimecs.IBS.Services.CheckLoyaltyNumberService.ValidateLoyaltyRQ ValidateLoyaltyRQ) {
            Ubimecs.IBS.Services.CheckLoyaltyNumberService.validateLoyaltyRequest inValue = new Ubimecs.IBS.Services.CheckLoyaltyNumberService.validateLoyaltyRequest();
            inValue.ValidateLoyaltyRQ = ValidateLoyaltyRQ;
            Ubimecs.IBS.Services.CheckLoyaltyNumberService.validateLoyaltyResponse retVal = ((Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort)(this)).validateLoyalty(inValue);
            return retVal.ValidateLoyaltyRS;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.CheckLoyaltyNumberService.validateLoyaltyResponse> Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort.validateLoyaltyAsync(Ubimecs.IBS.Services.CheckLoyaltyNumberService.validateLoyaltyRequest request) {
            return base.Channel.validateLoyaltyAsync(request);
        }
        
        public System.Threading.Tasks.Task<Ubimecs.IBS.Services.CheckLoyaltyNumberService.validateLoyaltyResponse> validateLoyaltyAsync(Ubimecs.IBS.Services.CheckLoyaltyNumberService.ValidateLoyaltyRQ ValidateLoyaltyRQ) {
            Ubimecs.IBS.Services.CheckLoyaltyNumberService.validateLoyaltyRequest inValue = new Ubimecs.IBS.Services.CheckLoyaltyNumberService.validateLoyaltyRequest();
            inValue.ValidateLoyaltyRQ = ValidateLoyaltyRQ;
            return ((Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort)(this)).validateLoyaltyAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Ubimecs.IBS.Services.CheckLoyaltyNumberService.viewCustomerProfileResponse Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort.viewCustomerProfile(Ubimecs.IBS.Services.CheckLoyaltyNumberService.viewCustomerProfileRequest request) {
            return base.Channel.viewCustomerProfile(request);
        }
        
        public Ubimecs.IBS.Services.CheckLoyaltyNumberService.ViewCustomerProfileRS viewCustomerProfile(Ubimecs.IBS.Services.CheckLoyaltyNumberService.ViewCustomerProfileRQ ViewCustomerProfileRQ) {
            Ubimecs.IBS.Services.CheckLoyaltyNumberService.viewCustomerProfileRequest inValue = new Ubimecs.IBS.Services.CheckLoyaltyNumberService.viewCustomerProfileRequest();
            inValue.ViewCustomerProfileRQ = ViewCustomerProfileRQ;
            Ubimecs.IBS.Services.CheckLoyaltyNumberService.viewCustomerProfileResponse retVal = ((Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort)(this)).viewCustomerProfile(inValue);
            return retVal.ViewCustomerProfileRS;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.CheckLoyaltyNumberService.viewCustomerProfileResponse> Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort.viewCustomerProfileAsync(Ubimecs.IBS.Services.CheckLoyaltyNumberService.viewCustomerProfileRequest request) {
            return base.Channel.viewCustomerProfileAsync(request);
        }
        
        public System.Threading.Tasks.Task<Ubimecs.IBS.Services.CheckLoyaltyNumberService.viewCustomerProfileResponse> viewCustomerProfileAsync(Ubimecs.IBS.Services.CheckLoyaltyNumberService.ViewCustomerProfileRQ ViewCustomerProfileRQ) {
            Ubimecs.IBS.Services.CheckLoyaltyNumberService.viewCustomerProfileRequest inValue = new Ubimecs.IBS.Services.CheckLoyaltyNumberService.viewCustomerProfileRequest();
            inValue.ViewCustomerProfileRQ = ViewCustomerProfileRQ;
            return ((Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort)(this)).viewCustomerProfileAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Ubimecs.IBS.Services.CheckLoyaltyNumberService.viewCustomerProfileViaWebResponse Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort.viewCustomerProfileViaWeb(Ubimecs.IBS.Services.CheckLoyaltyNumberService.viewCustomerProfileViaWebRequest request) {
            return base.Channel.viewCustomerProfileViaWeb(request);
        }
        
        public Ubimecs.IBS.Services.CheckLoyaltyNumberService.ViewCustomerProfileViaWebRS viewCustomerProfileViaWeb(Ubimecs.IBS.Services.CheckLoyaltyNumberService.ViewCustomerProfileViaWebRQ ViewCustomerProfileViaWebRQ) {
            Ubimecs.IBS.Services.CheckLoyaltyNumberService.viewCustomerProfileViaWebRequest inValue = new Ubimecs.IBS.Services.CheckLoyaltyNumberService.viewCustomerProfileViaWebRequest();
            inValue.ViewCustomerProfileViaWebRQ = ViewCustomerProfileViaWebRQ;
            Ubimecs.IBS.Services.CheckLoyaltyNumberService.viewCustomerProfileViaWebResponse retVal = ((Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort)(this)).viewCustomerProfileViaWeb(inValue);
            return retVal.ViewCustomerProfileViaWebRS;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.CheckLoyaltyNumberService.viewCustomerProfileViaWebResponse> Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort.viewCustomerProfileViaWebAsync(Ubimecs.IBS.Services.CheckLoyaltyNumberService.viewCustomerProfileViaWebRequest request) {
            return base.Channel.viewCustomerProfileViaWebAsync(request);
        }
        
        public System.Threading.Tasks.Task<Ubimecs.IBS.Services.CheckLoyaltyNumberService.viewCustomerProfileViaWebResponse> viewCustomerProfileViaWebAsync(Ubimecs.IBS.Services.CheckLoyaltyNumberService.ViewCustomerProfileViaWebRQ ViewCustomerProfileViaWebRQ) {
            Ubimecs.IBS.Services.CheckLoyaltyNumberService.viewCustomerProfileViaWebRequest inValue = new Ubimecs.IBS.Services.CheckLoyaltyNumberService.viewCustomerProfileViaWebRequest();
            inValue.ViewCustomerProfileViaWebRQ = ViewCustomerProfileViaWebRQ;
            return ((Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort)(this)).viewCustomerProfileViaWebAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Ubimecs.IBS.Services.CheckLoyaltyNumberService.validateDuplicateCustomerProfileResponse Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort.validateDuplicateCustomerProfile(Ubimecs.IBS.Services.CheckLoyaltyNumberService.validateDuplicateCustomerProfileRequest request) {
            return base.Channel.validateDuplicateCustomerProfile(request);
        }
        
        public Ubimecs.IBS.Services.CheckLoyaltyNumberService.ValidateDuplicateCustomerProfileRS validateDuplicateCustomerProfile(Ubimecs.IBS.Services.CheckLoyaltyNumberService.ValidateDuplicateCustomerProfileRQ ValidateDuplicateCustomerProfileRQ) {
            Ubimecs.IBS.Services.CheckLoyaltyNumberService.validateDuplicateCustomerProfileRequest inValue = new Ubimecs.IBS.Services.CheckLoyaltyNumberService.validateDuplicateCustomerProfileRequest();
            inValue.ValidateDuplicateCustomerProfileRQ = ValidateDuplicateCustomerProfileRQ;
            Ubimecs.IBS.Services.CheckLoyaltyNumberService.validateDuplicateCustomerProfileResponse retVal = ((Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort)(this)).validateDuplicateCustomerProfile(inValue);
            return retVal.ValidateDuplicateCustomerProfileRS;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.CheckLoyaltyNumberService.validateDuplicateCustomerProfileResponse> Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort.validateDuplicateCustomerProfileAsync(Ubimecs.IBS.Services.CheckLoyaltyNumberService.validateDuplicateCustomerProfileRequest request) {
            return base.Channel.validateDuplicateCustomerProfileAsync(request);
        }
        
        public System.Threading.Tasks.Task<Ubimecs.IBS.Services.CheckLoyaltyNumberService.validateDuplicateCustomerProfileResponse> validateDuplicateCustomerProfileAsync(Ubimecs.IBS.Services.CheckLoyaltyNumberService.ValidateDuplicateCustomerProfileRQ ValidateDuplicateCustomerProfileRQ) {
            Ubimecs.IBS.Services.CheckLoyaltyNumberService.validateDuplicateCustomerProfileRequest inValue = new Ubimecs.IBS.Services.CheckLoyaltyNumberService.validateDuplicateCustomerProfileRequest();
            inValue.ValidateDuplicateCustomerProfileRQ = ValidateDuplicateCustomerProfileRQ;
            return ((Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort)(this)).validateDuplicateCustomerProfileAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Ubimecs.IBS.Services.CheckLoyaltyNumberService.validateCustomerProfileListResponse Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort.validateCustomerProfileList(Ubimecs.IBS.Services.CheckLoyaltyNumberService.validateCustomerProfileListRequest request) {
            return base.Channel.validateCustomerProfileList(request);
        }
        
        public Ubimecs.IBS.Services.CheckLoyaltyNumberService.ValidateCustomerProfileListRS validateCustomerProfileList(Ubimecs.IBS.Services.CheckLoyaltyNumberService.ValidateCustomerProfileListRQ ValidateCustomerProfileListRQ) {
            Ubimecs.IBS.Services.CheckLoyaltyNumberService.validateCustomerProfileListRequest inValue = new Ubimecs.IBS.Services.CheckLoyaltyNumberService.validateCustomerProfileListRequest();
            inValue.ValidateCustomerProfileListRQ = ValidateCustomerProfileListRQ;
            Ubimecs.IBS.Services.CheckLoyaltyNumberService.validateCustomerProfileListResponse retVal = ((Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort)(this)).validateCustomerProfileList(inValue);
            return retVal.ValidateCustomerProfileListRS;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.CheckLoyaltyNumberService.validateCustomerProfileListResponse> Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort.validateCustomerProfileListAsync(Ubimecs.IBS.Services.CheckLoyaltyNumberService.validateCustomerProfileListRequest request) {
            return base.Channel.validateCustomerProfileListAsync(request);
        }
        
        public System.Threading.Tasks.Task<Ubimecs.IBS.Services.CheckLoyaltyNumberService.validateCustomerProfileListResponse> validateCustomerProfileListAsync(Ubimecs.IBS.Services.CheckLoyaltyNumberService.ValidateCustomerProfileListRQ ValidateCustomerProfileListRQ) {
            Ubimecs.IBS.Services.CheckLoyaltyNumberService.validateCustomerProfileListRequest inValue = new Ubimecs.IBS.Services.CheckLoyaltyNumberService.validateCustomerProfileListRequest();
            inValue.ValidateCustomerProfileListRQ = ValidateCustomerProfileListRQ;
            return ((Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort)(this)).validateCustomerProfileListAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Ubimecs.IBS.Services.CheckLoyaltyNumberService.activateCustomerProfileLoginResponse Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort.activateCustomerProfileLogin(Ubimecs.IBS.Services.CheckLoyaltyNumberService.activateCustomerProfileLoginRequest request) {
            return base.Channel.activateCustomerProfileLogin(request);
        }
        
        public Ubimecs.IBS.Services.CheckLoyaltyNumberService.ActivateCustomerProfileLoginRS activateCustomerProfileLogin(Ubimecs.IBS.Services.CheckLoyaltyNumberService.ActivateCustomerProfileLoginRQ ActivateCustomerProfileLoginRQ) {
            Ubimecs.IBS.Services.CheckLoyaltyNumberService.activateCustomerProfileLoginRequest inValue = new Ubimecs.IBS.Services.CheckLoyaltyNumberService.activateCustomerProfileLoginRequest();
            inValue.ActivateCustomerProfileLoginRQ = ActivateCustomerProfileLoginRQ;
            Ubimecs.IBS.Services.CheckLoyaltyNumberService.activateCustomerProfileLoginResponse retVal = ((Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort)(this)).activateCustomerProfileLogin(inValue);
            return retVal.ActivateCustomerProfileLoginRS;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.CheckLoyaltyNumberService.activateCustomerProfileLoginResponse> Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort.activateCustomerProfileLoginAsync(Ubimecs.IBS.Services.CheckLoyaltyNumberService.activateCustomerProfileLoginRequest request) {
            return base.Channel.activateCustomerProfileLoginAsync(request);
        }
        
        public System.Threading.Tasks.Task<Ubimecs.IBS.Services.CheckLoyaltyNumberService.activateCustomerProfileLoginResponse> activateCustomerProfileLoginAsync(Ubimecs.IBS.Services.CheckLoyaltyNumberService.ActivateCustomerProfileLoginRQ ActivateCustomerProfileLoginRQ) {
            Ubimecs.IBS.Services.CheckLoyaltyNumberService.activateCustomerProfileLoginRequest inValue = new Ubimecs.IBS.Services.CheckLoyaltyNumberService.activateCustomerProfileLoginRequest();
            inValue.ActivateCustomerProfileLoginRQ = ActivateCustomerProfileLoginRQ;
            return ((Ubimecs.IBS.Services.CheckLoyaltyNumberService.CustomerProfilePort)(this)).activateCustomerProfileLoginAsync(inValue);
        }
    }
}
