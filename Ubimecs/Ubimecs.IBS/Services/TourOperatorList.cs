//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Ubimecs.IBS.Services.TourOperatorList {
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.ServiceContractAttribute(Namespace="http://www.ibsplc.com/wsdl", ConfigurationName="TourOperatorList.AgencyPort")]
    public interface AgencyPort {
        
        // CODEGEN: Generating message contract since the operation createAgencyRegistration is neither RPC nor document wrapped.
        [System.ServiceModel.OperationContractAttribute(Action="urn:#createAgencyRegistration", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(BaseBookingRQ))]
        Ubimecs.IBS.Services.TourOperatorList.createAgencyRegistrationResponse createAgencyRegistration(Ubimecs.IBS.Services.TourOperatorList.createAgencyRegistrationRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:#createAgencyRegistration", ReplyAction="*")]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.TourOperatorList.createAgencyRegistrationResponse> createAgencyRegistrationAsync(Ubimecs.IBS.Services.TourOperatorList.createAgencyRegistrationRequest request);
        
        // CODEGEN: Generating message contract since the operation createTravelAgent is neither RPC nor document wrapped.
        [System.ServiceModel.OperationContractAttribute(Action="urn:#createTravelAgent", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(BaseBookingRQ))]
        Ubimecs.IBS.Services.TourOperatorList.createTravelAgentResponse createTravelAgent(Ubimecs.IBS.Services.TourOperatorList.createTravelAgentRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:#createTravelAgent", ReplyAction="*")]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.TourOperatorList.createTravelAgentResponse> createTravelAgentAsync(Ubimecs.IBS.Services.TourOperatorList.createTravelAgentRequest request);
        
        // CODEGEN: Generating message contract since the operation deactivateTravelAgent is neither RPC nor document wrapped.
        [System.ServiceModel.OperationContractAttribute(Action="urn:#deactivateTravelAgent", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(BaseBookingRQ))]
        Ubimecs.IBS.Services.TourOperatorList.deactivateTravelAgentResponse deactivateTravelAgent(Ubimecs.IBS.Services.TourOperatorList.deactivateTravelAgentRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:#deactivateTravelAgent", ReplyAction="*")]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.TourOperatorList.deactivateTravelAgentResponse> deactivateTravelAgentAsync(Ubimecs.IBS.Services.TourOperatorList.deactivateTravelAgentRequest request);
        
        // CODEGEN: Generating message contract since the operation listTravelAgents is neither RPC nor document wrapped.
        [System.ServiceModel.OperationContractAttribute(Action="urn:#listTravelAgents", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(BaseBookingRQ))]
        Ubimecs.IBS.Services.TourOperatorList.listTravelAgentsResponse listTravelAgents(Ubimecs.IBS.Services.TourOperatorList.listTravelAgentsRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:#listTravelAgents", ReplyAction="*")]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.TourOperatorList.listTravelAgentsResponse> listTravelAgentsAsync(Ubimecs.IBS.Services.TourOperatorList.listTravelAgentsRequest request);
        
        // CODEGEN: Generating message contract since the operation modifyAgencyProfile is neither RPC nor document wrapped.
        [System.ServiceModel.OperationContractAttribute(Action="urn:#modifyAgencyProfile", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(BaseBookingRQ))]
        Ubimecs.IBS.Services.TourOperatorList.modifyAgencyProfileResponse modifyAgencyProfile(Ubimecs.IBS.Services.TourOperatorList.modifyAgencyProfileRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:#modifyAgencyProfile", ReplyAction="*")]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.TourOperatorList.modifyAgencyProfileResponse> modifyAgencyProfileAsync(Ubimecs.IBS.Services.TourOperatorList.modifyAgencyProfileRequest request);
        
        // CODEGEN: Generating message contract since the operation modifyTravelAgent is neither RPC nor document wrapped.
        [System.ServiceModel.OperationContractAttribute(Action="urn:#modifyTravelAgent", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(BaseBookingRQ))]
        Ubimecs.IBS.Services.TourOperatorList.modifyTravelAgentResponse modifyTravelAgent(Ubimecs.IBS.Services.TourOperatorList.modifyTravelAgentRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:#modifyTravelAgent", ReplyAction="*")]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.TourOperatorList.modifyTravelAgentResponse> modifyTravelAgentAsync(Ubimecs.IBS.Services.TourOperatorList.modifyTravelAgentRequest request);
        
        // CODEGEN: Generating message contract since the operation retrieveAgencyCredit is neither RPC nor document wrapped.
        [System.ServiceModel.OperationContractAttribute(Action="urn:#retrieveAgencyCredit", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(BaseBookingRQ))]
        Ubimecs.IBS.Services.TourOperatorList.retrieveAgencyCreditResponse retrieveAgencyCredit(Ubimecs.IBS.Services.TourOperatorList.retrieveAgencyCreditRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:#retrieveAgencyCredit", ReplyAction="*")]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.TourOperatorList.retrieveAgencyCreditResponse> retrieveAgencyCreditAsync(Ubimecs.IBS.Services.TourOperatorList.retrieveAgencyCreditRequest request);
        
        // CODEGEN: Generating message contract since the operation retrieveAgentInvoiceDetails is neither RPC nor document wrapped.
        [System.ServiceModel.OperationContractAttribute(Action="urn:#retrieveAgentInvoiceDetails", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(BaseBookingRQ))]
        Ubimecs.IBS.Services.TourOperatorList.retrieveAgentInvoiceDetailsResponse retrieveAgentInvoiceDetails(Ubimecs.IBS.Services.TourOperatorList.retrieveAgentInvoiceDetailsRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:#retrieveAgentInvoiceDetails", ReplyAction="*")]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.TourOperatorList.retrieveAgentInvoiceDetailsResponse> retrieveAgentInvoiceDetailsAsync(Ubimecs.IBS.Services.TourOperatorList.retrieveAgentInvoiceDetailsRequest request);
        
        // CODEGEN: Generating message contract since the operation retrieveDutyCodesForAgency is neither RPC nor document wrapped.
        [System.ServiceModel.OperationContractAttribute(Action="urn:#retrieveDutyCodesForAgency", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(BaseBookingRQ))]
        Ubimecs.IBS.Services.TourOperatorList.retrieveDutyCodesForAgencyResponse retrieveDutyCodesForAgency(Ubimecs.IBS.Services.TourOperatorList.retrieveDutyCodesForAgencyRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:#retrieveDutyCodesForAgency", ReplyAction="*")]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.TourOperatorList.retrieveDutyCodesForAgencyResponse> retrieveDutyCodesForAgencyAsync(Ubimecs.IBS.Services.TourOperatorList.retrieveDutyCodesForAgencyRequest request);
        
        // CODEGEN: Generating message contract since the operation retrieveFOPDetailsForAgency is neither RPC nor document wrapped.
        [System.ServiceModel.OperationContractAttribute(Action="urn:#retrieveFOPDetailsForAgency", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(BaseBookingRQ))]
        Ubimecs.IBS.Services.TourOperatorList.retrieveFOPDetailsForAgencyResponse retrieveFOPDetailsForAgency(Ubimecs.IBS.Services.TourOperatorList.retrieveFOPDetailsForAgencyRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:#retrieveFOPDetailsForAgency", ReplyAction="*")]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.TourOperatorList.retrieveFOPDetailsForAgencyResponse> retrieveFOPDetailsForAgencyAsync(Ubimecs.IBS.Services.TourOperatorList.retrieveFOPDetailsForAgencyRequest request);
        
        // CODEGEN: Generating message contract since the operation retrieveTravelAgentDetails is neither RPC nor document wrapped.
        [System.ServiceModel.OperationContractAttribute(Action="urn:#retrieveTravelAgentDetails", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(BaseBookingRQ))]
        Ubimecs.IBS.Services.TourOperatorList.retrieveTravelAgentDetailsResponse retrieveTravelAgentDetails(Ubimecs.IBS.Services.TourOperatorList.retrieveTravelAgentDetailsRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:#retrieveTravelAgentDetails", ReplyAction="*")]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.TourOperatorList.retrieveTravelAgentDetailsResponse> retrieveTravelAgentDetailsAsync(Ubimecs.IBS.Services.TourOperatorList.retrieveTravelAgentDetailsRequest request);
        
        // CODEGEN: Generating message contract since the operation viewAgencyProfile is neither RPC nor document wrapped.
        [System.ServiceModel.OperationContractAttribute(Action="urn:#viewAgencyProfile", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(BaseBookingRQ))]
        Ubimecs.IBS.Services.TourOperatorList.viewAgencyProfileResponse viewAgencyProfile(Ubimecs.IBS.Services.TourOperatorList.viewAgencyProfileRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:#viewAgencyProfile", ReplyAction="*")]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.TourOperatorList.viewAgencyProfileResponse> viewAgencyProfileAsync(Ubimecs.IBS.Services.TourOperatorList.viewAgencyProfileRequest request);
        
        // CODEGEN: Generating message contract since the operation activateTravelAgent is neither RPC nor document wrapped.
        [System.ServiceModel.OperationContractAttribute(Action="urn:#activateTravelAgent", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(BaseBookingRQ))]
        Ubimecs.IBS.Services.TourOperatorList.activateTravelAgentResponse activateTravelAgent(Ubimecs.IBS.Services.TourOperatorList.activateTravelAgentRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:#activateTravelAgent", ReplyAction="*")]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.TourOperatorList.activateTravelAgentResponse> activateTravelAgentAsync(Ubimecs.IBS.Services.TourOperatorList.activateTravelAgentRequest request);
        
        // CODEGEN: Generating message contract since the operation retrieveAgencies is neither RPC nor document wrapped.
        [System.ServiceModel.OperationContractAttribute(Action="urn:#retrieveAgencies", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(BaseBookingRQ))]
        Ubimecs.IBS.Services.TourOperatorList.retrieveAgenciesResponse retrieveAgencies(Ubimecs.IBS.Services.TourOperatorList.retrieveAgenciesRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:#retrieveAgencies", ReplyAction="*")]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.TourOperatorList.retrieveAgenciesResponse> retrieveAgenciesAsync(Ubimecs.IBS.Services.TourOperatorList.retrieveAgenciesRequest request);
        
        // CODEGEN: Generating message contract since the operation agencyPayment is neither RPC nor document wrapped.
        [System.ServiceModel.OperationContractAttribute(Action="urn:#agencyPayment", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(BaseBookingRQ))]
        Ubimecs.IBS.Services.TourOperatorList.agencyPaymentResponse agencyPayment(Ubimecs.IBS.Services.TourOperatorList.agencyPaymentRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:#agencyPayment", ReplyAction="*")]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.TourOperatorList.agencyPaymentResponse> agencyPaymentAsync(Ubimecs.IBS.Services.TourOperatorList.agencyPaymentRequest request);
        
        // CODEGEN: Generating message contract since the operation retrieveAgencyCreditHistory is neither RPC nor document wrapped.
        [System.ServiceModel.OperationContractAttribute(Action="urn:#retrieveAgencyCreditHistory", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [System.ServiceModel.ServiceKnownTypeAttribute(typeof(BaseBookingRQ))]
        Ubimecs.IBS.Services.TourOperatorList.retrieveAgencyCreditHistoryResponse retrieveAgencyCreditHistory(Ubimecs.IBS.Services.TourOperatorList.retrieveAgencyCreditHistoryRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="urn:#retrieveAgencyCreditHistory", ReplyAction="*")]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.TourOperatorList.retrieveAgencyCreditHistoryResponse> retrieveAgencyCreditHistoryAsync(Ubimecs.IBS.Services.TourOperatorList.retrieveAgencyCreditHistoryRequest request);
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class CreateAgencyRegistrationRQ : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string airlineCodeField;
        
        private BookingChannelKeyType bookingChannelField;
        
        private string[] channelCodeField;
        
        private string agencyCodeField;
        
        private string corporateIdField;
        
        private string tourOperatorCodeField;
        
        private string agencyNameField;
        
        private string agencyTypeField;
        
        private string companyTypeField;
        
        private string agencyCategoryField;
        
        private string licenseNumberField;
        
        private string domesticBudgetCurrencyField;
        
        private string domesticBudgetAmountField;
        
        private string generalCommentsField;
        
        private string chainDetailsField;
        
        private AgencyContactDetails agencyContactDetailsField;
        
        private AgencyBankingDetail agencyBankDetailsField;
        
        private string fiscalCodeField;
        
        private string parentAgencyCodeField;
        
        private string registeredLegalNameField;
        
        private string vATCodeField;
        
        private TaxInvoiceDetailType taxInvoiceDetailField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AirlineCode {
            get {
                return this.airlineCodeField;
            }
            set {
                this.airlineCodeField = value;
                this.RaisePropertyChanged("AirlineCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public BookingChannelKeyType BookingChannel {
            get {
                return this.bookingChannelField;
            }
            set {
                this.bookingChannelField = value;
                this.RaisePropertyChanged("BookingChannel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("ChannelCode", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string[] ChannelCode {
            get {
                return this.channelCodeField;
            }
            set {
                this.channelCodeField = value;
                this.RaisePropertyChanged("ChannelCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string AgencyCode {
            get {
                return this.agencyCodeField;
            }
            set {
                this.agencyCodeField = value;
                this.RaisePropertyChanged("AgencyCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string CorporateId {
            get {
                return this.corporateIdField;
            }
            set {
                this.corporateIdField = value;
                this.RaisePropertyChanged("CorporateId");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string TourOperatorCode {
            get {
                return this.tourOperatorCodeField;
            }
            set {
                this.tourOperatorCodeField = value;
                this.RaisePropertyChanged("TourOperatorCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string AgencyName {
            get {
                return this.agencyNameField;
            }
            set {
                this.agencyNameField = value;
                this.RaisePropertyChanged("AgencyName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string AgencyType {
            get {
                return this.agencyTypeField;
            }
            set {
                this.agencyTypeField = value;
                this.RaisePropertyChanged("AgencyType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public string CompanyType {
            get {
                return this.companyTypeField;
            }
            set {
                this.companyTypeField = value;
                this.RaisePropertyChanged("CompanyType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public string AgencyCategory {
            get {
                return this.agencyCategoryField;
            }
            set {
                this.agencyCategoryField = value;
                this.RaisePropertyChanged("AgencyCategory");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=10)]
        public string LicenseNumber {
            get {
                return this.licenseNumberField;
            }
            set {
                this.licenseNumberField = value;
                this.RaisePropertyChanged("LicenseNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=11)]
        public string DomesticBudgetCurrency {
            get {
                return this.domesticBudgetCurrencyField;
            }
            set {
                this.domesticBudgetCurrencyField = value;
                this.RaisePropertyChanged("DomesticBudgetCurrency");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=12)]
        public string DomesticBudgetAmount {
            get {
                return this.domesticBudgetAmountField;
            }
            set {
                this.domesticBudgetAmountField = value;
                this.RaisePropertyChanged("DomesticBudgetAmount");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=13)]
        public string GeneralComments {
            get {
                return this.generalCommentsField;
            }
            set {
                this.generalCommentsField = value;
                this.RaisePropertyChanged("GeneralComments");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=14)]
        public string ChainDetails {
            get {
                return this.chainDetailsField;
            }
            set {
                this.chainDetailsField = value;
                this.RaisePropertyChanged("ChainDetails");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=15)]
        public AgencyContactDetails AgencyContactDetails {
            get {
                return this.agencyContactDetailsField;
            }
            set {
                this.agencyContactDetailsField = value;
                this.RaisePropertyChanged("AgencyContactDetails");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=16)]
        public AgencyBankingDetail AgencyBankDetails {
            get {
                return this.agencyBankDetailsField;
            }
            set {
                this.agencyBankDetailsField = value;
                this.RaisePropertyChanged("AgencyBankDetails");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=17)]
        public string FiscalCode {
            get {
                return this.fiscalCodeField;
            }
            set {
                this.fiscalCodeField = value;
                this.RaisePropertyChanged("FiscalCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=18)]
        public string ParentAgencyCode {
            get {
                return this.parentAgencyCodeField;
            }
            set {
                this.parentAgencyCodeField = value;
                this.RaisePropertyChanged("ParentAgencyCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=19)]
        public string RegisteredLegalName {
            get {
                return this.registeredLegalNameField;
            }
            set {
                this.registeredLegalNameField = value;
                this.RaisePropertyChanged("RegisteredLegalName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=20)]
        public string VATCode {
            get {
                return this.vATCodeField;
            }
            set {
                this.vATCodeField = value;
                this.RaisePropertyChanged("VATCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=21)]
        public TaxInvoiceDetailType TaxInvoiceDetail {
            get {
                return this.taxInvoiceDetailField;
            }
            set {
                this.taxInvoiceDetailField = value;
                this.RaisePropertyChanged("TaxInvoiceDetail");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class BookingChannelKeyType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string channelTypeField;
        
        private string channelField;
        
        private string localeField;
        
        private string sessionIdField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string ChannelType {
            get {
                return this.channelTypeField;
            }
            set {
                this.channelTypeField = value;
                this.RaisePropertyChanged("ChannelType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string Channel {
            get {
                return this.channelField;
            }
            set {
                this.channelField = value;
                this.RaisePropertyChanged("Channel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string Locale {
            get {
                return this.localeField;
            }
            set {
                this.localeField = value;
                this.RaisePropertyChanged("Locale");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string SessionId {
            get {
                return this.sessionIdField;
            }
            set {
                this.sessionIdField = value;
                this.RaisePropertyChanged("SessionId");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class AgencyCreditHistType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string agencyCodeField;
        
        private string agencyNameField;
        
        private bool isCreditAllowedField;
        
        private bool isCreditAllowedFieldSpecified;
        
        private string creditTypeField;
        
        private bool isCommAddedToAvlCreditField;
        
        private bool isCommAddedToAvlCreditFieldSpecified;
        
        private CurrencyAmountType creditLimitField;
        
        private CurrencyAmountType availableCreditField;
        
        private CurrencyAmountType usedCreditField;
        
        private bool isNegCreditAllowedField;
        
        private bool isNegCreditAllowedFieldSpecified;
        
        private CurrencyAmountType maxNegCreditAllowedField;
        
        private CurrencyAmountType transactionAmountField;
        
        private string serviceInitiatorField;
        
        private string trackingNumberField;
        
        private string actionField;
        
        private string userIDField;
        
        private string dutyCodeField;
        
        private string officeCodeField;
        
        private System.DateTime lastModifiedDateField;
        
        private bool lastModifiedDateFieldSpecified;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AgencyCode {
            get {
                return this.agencyCodeField;
            }
            set {
                this.agencyCodeField = value;
                this.RaisePropertyChanged("AgencyCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string AgencyName {
            get {
                return this.agencyNameField;
            }
            set {
                this.agencyNameField = value;
                this.RaisePropertyChanged("AgencyName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public bool IsCreditAllowed {
            get {
                return this.isCreditAllowedField;
            }
            set {
                this.isCreditAllowedField = value;
                this.RaisePropertyChanged("IsCreditAllowed");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool IsCreditAllowedSpecified {
            get {
                return this.isCreditAllowedFieldSpecified;
            }
            set {
                this.isCreditAllowedFieldSpecified = value;
                this.RaisePropertyChanged("IsCreditAllowedSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string CreditType {
            get {
                return this.creditTypeField;
            }
            set {
                this.creditTypeField = value;
                this.RaisePropertyChanged("CreditType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public bool IsCommAddedToAvlCredit {
            get {
                return this.isCommAddedToAvlCreditField;
            }
            set {
                this.isCommAddedToAvlCreditField = value;
                this.RaisePropertyChanged("IsCommAddedToAvlCredit");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool IsCommAddedToAvlCreditSpecified {
            get {
                return this.isCommAddedToAvlCreditFieldSpecified;
            }
            set {
                this.isCommAddedToAvlCreditFieldSpecified = value;
                this.RaisePropertyChanged("IsCommAddedToAvlCreditSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public CurrencyAmountType CreditLimit {
            get {
                return this.creditLimitField;
            }
            set {
                this.creditLimitField = value;
                this.RaisePropertyChanged("CreditLimit");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public CurrencyAmountType AvailableCredit {
            get {
                return this.availableCreditField;
            }
            set {
                this.availableCreditField = value;
                this.RaisePropertyChanged("AvailableCredit");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public CurrencyAmountType UsedCredit {
            get {
                return this.usedCreditField;
            }
            set {
                this.usedCreditField = value;
                this.RaisePropertyChanged("UsedCredit");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public bool IsNegCreditAllowed {
            get {
                return this.isNegCreditAllowedField;
            }
            set {
                this.isNegCreditAllowedField = value;
                this.RaisePropertyChanged("IsNegCreditAllowed");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool IsNegCreditAllowedSpecified {
            get {
                return this.isNegCreditAllowedFieldSpecified;
            }
            set {
                this.isNegCreditAllowedFieldSpecified = value;
                this.RaisePropertyChanged("IsNegCreditAllowedSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public CurrencyAmountType MaxNegCreditAllowed {
            get {
                return this.maxNegCreditAllowedField;
            }
            set {
                this.maxNegCreditAllowedField = value;
                this.RaisePropertyChanged("MaxNegCreditAllowed");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=10)]
        public CurrencyAmountType TransactionAmount {
            get {
                return this.transactionAmountField;
            }
            set {
                this.transactionAmountField = value;
                this.RaisePropertyChanged("TransactionAmount");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=11)]
        public string ServiceInitiator {
            get {
                return this.serviceInitiatorField;
            }
            set {
                this.serviceInitiatorField = value;
                this.RaisePropertyChanged("ServiceInitiator");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=12)]
        public string TrackingNumber {
            get {
                return this.trackingNumberField;
            }
            set {
                this.trackingNumberField = value;
                this.RaisePropertyChanged("TrackingNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=13)]
        public string Action {
            get {
                return this.actionField;
            }
            set {
                this.actionField = value;
                this.RaisePropertyChanged("Action");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=14)]
        public string UserID {
            get {
                return this.userIDField;
            }
            set {
                this.userIDField = value;
                this.RaisePropertyChanged("UserID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=15)]
        public string DutyCode {
            get {
                return this.dutyCodeField;
            }
            set {
                this.dutyCodeField = value;
                this.RaisePropertyChanged("DutyCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=16)]
        public string OfficeCode {
            get {
                return this.officeCodeField;
            }
            set {
                this.officeCodeField = value;
                this.RaisePropertyChanged("OfficeCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=17)]
        public System.DateTime LastModifiedDate {
            get {
                return this.lastModifiedDateField;
            }
            set {
                this.lastModifiedDateField = value;
                this.RaisePropertyChanged("LastModifiedDate");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool LastModifiedDateSpecified {
            get {
                return this.lastModifiedDateFieldSpecified;
            }
            set {
                this.lastModifiedDateFieldSpecified = value;
                this.RaisePropertyChanged("LastModifiedDateSpecified");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class CurrencyAmountType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string currencyCodeField;
        
        private decimal valueField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string currencyCode {
            get {
                return this.currencyCodeField;
            }
            set {
                this.currencyCodeField = value;
                this.RaisePropertyChanged("currencyCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        public decimal Value {
            get {
                return this.valueField;
            }
            set {
                this.valueField = value;
                this.RaisePropertyChanged("Value");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class AgencyPaymentDetailsType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string agencyCodeField;
        
        private System.DateTime paymentDateField;
        
        private string commentField;
        
        private AgencyPaymentFOPType formOfPaymentField;
        
        private string fOPNumberField;
        
        private CurrencyAmountType remittanceAmtDetailsField;
        
        private CurrencyAmountType transactionAmtDetailsField;
        
        private CurrencyAmountType availableCreditField;
        
        private CurrencyAmountType writeOffAmtDtlsField;
        
        private string reportingOfficeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AgencyCode {
            get {
                return this.agencyCodeField;
            }
            set {
                this.agencyCodeField = value;
                this.RaisePropertyChanged("AgencyCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, DataType="date", Order=1)]
        public System.DateTime PaymentDate {
            get {
                return this.paymentDateField;
            }
            set {
                this.paymentDateField = value;
                this.RaisePropertyChanged("PaymentDate");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string Comment {
            get {
                return this.commentField;
            }
            set {
                this.commentField = value;
                this.RaisePropertyChanged("Comment");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public AgencyPaymentFOPType FormOfPayment {
            get {
                return this.formOfPaymentField;
            }
            set {
                this.formOfPaymentField = value;
                this.RaisePropertyChanged("FormOfPayment");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string FOPNumber {
            get {
                return this.fOPNumberField;
            }
            set {
                this.fOPNumberField = value;
                this.RaisePropertyChanged("FOPNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public CurrencyAmountType RemittanceAmtDetails {
            get {
                return this.remittanceAmtDetailsField;
            }
            set {
                this.remittanceAmtDetailsField = value;
                this.RaisePropertyChanged("RemittanceAmtDetails");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public CurrencyAmountType TransactionAmtDetails {
            get {
                return this.transactionAmtDetailsField;
            }
            set {
                this.transactionAmtDetailsField = value;
                this.RaisePropertyChanged("TransactionAmtDetails");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public CurrencyAmountType AvailableCredit {
            get {
                return this.availableCreditField;
            }
            set {
                this.availableCreditField = value;
                this.RaisePropertyChanged("AvailableCredit");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public CurrencyAmountType WriteOffAmtDtls {
            get {
                return this.writeOffAmtDtlsField;
            }
            set {
                this.writeOffAmtDtlsField = value;
                this.RaisePropertyChanged("WriteOffAmtDtls");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public string ReportingOffice {
            get {
                return this.reportingOfficeField;
            }
            set {
                this.reportingOfficeField = value;
                this.RaisePropertyChanged("ReportingOffice");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public enum AgencyPaymentFOPType {
        
        /// <remarks/>
        CA,
        
        /// <remarks/>
        CK,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class AgencyDetailsType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private AgencyCategoryType agencyCategoryField;
        
        private string agencyCodeField;
        
        private string agencyNameField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public AgencyCategoryType AgencyCategory {
            get {
                return this.agencyCategoryField;
            }
            set {
                this.agencyCategoryField = value;
                this.RaisePropertyChanged("AgencyCategory");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string AgencyCode {
            get {
                return this.agencyCodeField;
            }
            set {
                this.agencyCodeField = value;
                this.RaisePropertyChanged("AgencyCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string AgencyName {
            get {
                return this.agencyNameField;
            }
            set {
                this.agencyNameField = value;
                this.RaisePropertyChanged("AgencyName");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public enum AgencyCategoryType {
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("TOUR OPERATOR")]
        TOUROPERATOR,
        
        /// <remarks/>
        CORPORATE,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("TRAVEL AGENCY")]
        TRAVELAGENCY,
        
        /// <remarks/>
        ALL,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class CorporateCardDetails : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string corpCardNumberField;
        
        private string maskedCorpCardNumberField;
        
        private string expiryDateField;
        
        private string cardSeqNumberField;
        
        private string customercardIDField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string corpCardNumber {
            get {
                return this.corpCardNumberField;
            }
            set {
                this.corpCardNumberField = value;
                this.RaisePropertyChanged("corpCardNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string maskedCorpCardNumber {
            get {
                return this.maskedCorpCardNumberField;
            }
            set {
                this.maskedCorpCardNumberField = value;
                this.RaisePropertyChanged("maskedCorpCardNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string expiryDate {
            get {
                return this.expiryDateField;
            }
            set {
                this.expiryDateField = value;
                this.RaisePropertyChanged("expiryDate");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string CardSeqNumber {
            get {
                return this.cardSeqNumberField;
            }
            set {
                this.cardSeqNumberField = value;
                this.RaisePropertyChanged("CardSeqNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string CustomercardID {
            get {
                return this.customercardIDField;
            }
            set {
                this.customercardIDField = value;
                this.RaisePropertyChanged("CustomercardID");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class AgencyCommissionRateDetails : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string bookingTypeField;
        
        private string channelField;
        
        private double commissionRateField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string bookingType {
            get {
                return this.bookingTypeField;
            }
            set {
                this.bookingTypeField = value;
                this.RaisePropertyChanged("bookingType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string channel {
            get {
                return this.channelField;
            }
            set {
                this.channelField = value;
                this.RaisePropertyChanged("channel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public double commissionRate {
            get {
                return this.commissionRateField;
            }
            set {
                this.commissionRateField = value;
                this.RaisePropertyChanged("commissionRate");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class AgencyCommissionDetails : object, System.ComponentModel.INotifyPropertyChanged {
        
        private bool isEligibleForCommissionField;
        
        private bool isEligibleForCommissionFieldSpecified;
        
        private AgencyCommissionRateDetails[] agencyCommissionRateDetailsField;
        
        private double addOnCommissionRateField;
        
        private bool addOnCommissionRateFieldSpecified;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public bool IsEligibleForCommission {
            get {
                return this.isEligibleForCommissionField;
            }
            set {
                this.isEligibleForCommissionField = value;
                this.RaisePropertyChanged("IsEligibleForCommission");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool IsEligibleForCommissionSpecified {
            get {
                return this.isEligibleForCommissionFieldSpecified;
            }
            set {
                this.isEligibleForCommissionFieldSpecified = value;
                this.RaisePropertyChanged("IsEligibleForCommissionSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("AgencyCommissionRateDetails", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public AgencyCommissionRateDetails[] AgencyCommissionRateDetails {
            get {
                return this.agencyCommissionRateDetailsField;
            }
            set {
                this.agencyCommissionRateDetailsField = value;
                this.RaisePropertyChanged("AgencyCommissionRateDetails");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public double AddOnCommissionRate {
            get {
                return this.addOnCommissionRateField;
            }
            set {
                this.addOnCommissionRateField = value;
                this.RaisePropertyChanged("AddOnCommissionRate");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool AddOnCommissionRateSpecified {
            get {
                return this.addOnCommissionRateFieldSpecified;
            }
            set {
                this.addOnCommissionRateFieldSpecified = value;
                this.RaisePropertyChanged("AddOnCommissionRateSpecified");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class AgencyCreditDetails : object, System.ComponentModel.INotifyPropertyChanged {
        
        private bool isPrepaidCreditAllowedField;
        
        private bool isPrepaidCreditAllowedFieldSpecified;
        
        private string currencyCodeField;
        
        private decimal creditLimitField;
        
        private decimal availableCreditField;
        
        private decimal usedCreditField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public bool isPrepaidCreditAllowed {
            get {
                return this.isPrepaidCreditAllowedField;
            }
            set {
                this.isPrepaidCreditAllowedField = value;
                this.RaisePropertyChanged("isPrepaidCreditAllowed");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool isPrepaidCreditAllowedSpecified {
            get {
                return this.isPrepaidCreditAllowedFieldSpecified;
            }
            set {
                this.isPrepaidCreditAllowedFieldSpecified = value;
                this.RaisePropertyChanged("isPrepaidCreditAllowedSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string currencyCode {
            get {
                return this.currencyCodeField;
            }
            set {
                this.currencyCodeField = value;
                this.RaisePropertyChanged("currencyCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public decimal creditLimit {
            get {
                return this.creditLimitField;
            }
            set {
                this.creditLimitField = value;
                this.RaisePropertyChanged("creditLimit");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public decimal AvailableCredit {
            get {
                return this.availableCreditField;
            }
            set {
                this.availableCreditField = value;
                this.RaisePropertyChanged("AvailableCredit");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public decimal UsedCredit {
            get {
                return this.usedCreditField;
            }
            set {
                this.usedCreditField = value;
                this.RaisePropertyChanged("UsedCredit");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class BaseBookingRQ : object, System.ComponentModel.INotifyPropertyChanged {
        
        private BookingChannelKeyType bookingChannelField;
        
        private string airlineCodeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public BookingChannelKeyType BookingChannel {
            get {
                return this.bookingChannelField;
            }
            set {
                this.bookingChannelField = value;
                this.RaisePropertyChanged("BookingChannel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string AirlineCode {
            get {
                return this.airlineCodeField;
            }
            set {
                this.airlineCodeField = value;
                this.RaisePropertyChanged("AirlineCode");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class FOPDetails : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string fOPNameField;
        
        private string fOPCodeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string FOPName {
            get {
                return this.fOPNameField;
            }
            set {
                this.fOPNameField = value;
                this.RaisePropertyChanged("FOPName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string FOPCode {
            get {
                return this.fOPCodeField;
            }
            set {
                this.fOPCodeField = value;
                this.RaisePropertyChanged("FOPCode");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class DutyCodeDetails : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string dutyCodeField;
        
        private string dutyCodeNameField;
        
        private string descriptionField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string DutyCode {
            get {
                return this.dutyCodeField;
            }
            set {
                this.dutyCodeField = value;
                this.RaisePropertyChanged("DutyCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string DutyCodeName {
            get {
                return this.dutyCodeNameField;
            }
            set {
                this.dutyCodeNameField = value;
                this.RaisePropertyChanged("DutyCodeName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string Description {
            get {
                return this.descriptionField;
            }
            set {
                this.descriptionField = value;
                this.RaisePropertyChanged("Description");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class AgentInvoiceDetailsType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string ownerField;
        
        private string agentIdField;
        
        private string fopCodeField;
        
        private string pnrNumberField;
        
        private System.DateTime dateOfBookingField;
        
        private bool dateOfBookingFieldSpecified;
        
        private string documentNumberField;
        
        private string pointOfSaleField;
        
        private string guestNameField;
        
        private string guestTypeField;
        
        private string originOfTicketField;
        
        private string destOfTicketField;
        
        private System.DateTime travelDateField;
        
        private bool travelDateFieldSpecified;
        
        private string currencyField;
        
        private double appliedFareAndSurchargeField;
        
        private bool appliedFareAndSurchargeFieldSpecified;
        
        private double totalTaxField;
        
        private bool totalTaxFieldSpecified;
        
        private string feeCodeField;
        
        private double feeAmountField;
        
        private bool feeAmountFieldSpecified;
        
        private double penaltyFeesAmountField;
        
        private bool penaltyFeesAmountFieldSpecified;
        
        private double perSegServiceFeeField;
        
        private bool perSegServiceFeeFieldSpecified;
        
        private double perPaxPerSegServiceFeeField;
        
        private bool perPaxPerSegServiceFeeFieldSpecified;
        
        private double percentageCommAmountField;
        
        private bool percentageCommAmountFieldSpecified;
        
        private double totalAmountField;
        
        private bool totalAmountFieldSpecified;
        
        private string initiatorField;
        
        private string agencyNameField;
        
        private string travelTypeField;
        
        private double totalAirportTaxField;
        
        private bool totalAirportTaxFieldSpecified;
        
        private double totalOtherTaxField;
        
        private bool totalOtherTaxFieldSpecified;
        
        private string maskedCreditcardNumberField;
        
        private string paymentTransactionIDField;
        
        private string flightLegDetailsStringField;
        
        private string eticketInvoiceNumberField;
        
        private string cardHolderNameField;
        
        private string taxNumberField;
        
        private string taxOfficeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string Owner {
            get {
                return this.ownerField;
            }
            set {
                this.ownerField = value;
                this.RaisePropertyChanged("Owner");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string AgentId {
            get {
                return this.agentIdField;
            }
            set {
                this.agentIdField = value;
                this.RaisePropertyChanged("AgentId");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string FopCode {
            get {
                return this.fopCodeField;
            }
            set {
                this.fopCodeField = value;
                this.RaisePropertyChanged("FopCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string PnrNumber {
            get {
                return this.pnrNumberField;
            }
            set {
                this.pnrNumberField = value;
                this.RaisePropertyChanged("PnrNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, DataType="date", Order=4)]
        public System.DateTime DateOfBooking {
            get {
                return this.dateOfBookingField;
            }
            set {
                this.dateOfBookingField = value;
                this.RaisePropertyChanged("DateOfBooking");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool DateOfBookingSpecified {
            get {
                return this.dateOfBookingFieldSpecified;
            }
            set {
                this.dateOfBookingFieldSpecified = value;
                this.RaisePropertyChanged("DateOfBookingSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string DocumentNumber {
            get {
                return this.documentNumberField;
            }
            set {
                this.documentNumberField = value;
                this.RaisePropertyChanged("DocumentNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string PointOfSale {
            get {
                return this.pointOfSaleField;
            }
            set {
                this.pointOfSaleField = value;
                this.RaisePropertyChanged("PointOfSale");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string GuestName {
            get {
                return this.guestNameField;
            }
            set {
                this.guestNameField = value;
                this.RaisePropertyChanged("GuestName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public string GuestType {
            get {
                return this.guestTypeField;
            }
            set {
                this.guestTypeField = value;
                this.RaisePropertyChanged("GuestType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public string OriginOfTicket {
            get {
                return this.originOfTicketField;
            }
            set {
                this.originOfTicketField = value;
                this.RaisePropertyChanged("OriginOfTicket");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=10)]
        public string DestOfTicket {
            get {
                return this.destOfTicketField;
            }
            set {
                this.destOfTicketField = value;
                this.RaisePropertyChanged("DestOfTicket");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, DataType="date", Order=11)]
        public System.DateTime TravelDate {
            get {
                return this.travelDateField;
            }
            set {
                this.travelDateField = value;
                this.RaisePropertyChanged("TravelDate");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool TravelDateSpecified {
            get {
                return this.travelDateFieldSpecified;
            }
            set {
                this.travelDateFieldSpecified = value;
                this.RaisePropertyChanged("TravelDateSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=12)]
        public string Currency {
            get {
                return this.currencyField;
            }
            set {
                this.currencyField = value;
                this.RaisePropertyChanged("Currency");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=13)]
        public double AppliedFareAndSurcharge {
            get {
                return this.appliedFareAndSurchargeField;
            }
            set {
                this.appliedFareAndSurchargeField = value;
                this.RaisePropertyChanged("AppliedFareAndSurcharge");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool AppliedFareAndSurchargeSpecified {
            get {
                return this.appliedFareAndSurchargeFieldSpecified;
            }
            set {
                this.appliedFareAndSurchargeFieldSpecified = value;
                this.RaisePropertyChanged("AppliedFareAndSurchargeSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=14)]
        public double TotalTax {
            get {
                return this.totalTaxField;
            }
            set {
                this.totalTaxField = value;
                this.RaisePropertyChanged("TotalTax");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool TotalTaxSpecified {
            get {
                return this.totalTaxFieldSpecified;
            }
            set {
                this.totalTaxFieldSpecified = value;
                this.RaisePropertyChanged("TotalTaxSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=15)]
        public string FeeCode {
            get {
                return this.feeCodeField;
            }
            set {
                this.feeCodeField = value;
                this.RaisePropertyChanged("FeeCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=16)]
        public double FeeAmount {
            get {
                return this.feeAmountField;
            }
            set {
                this.feeAmountField = value;
                this.RaisePropertyChanged("FeeAmount");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool FeeAmountSpecified {
            get {
                return this.feeAmountFieldSpecified;
            }
            set {
                this.feeAmountFieldSpecified = value;
                this.RaisePropertyChanged("FeeAmountSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=17)]
        public double PenaltyFeesAmount {
            get {
                return this.penaltyFeesAmountField;
            }
            set {
                this.penaltyFeesAmountField = value;
                this.RaisePropertyChanged("PenaltyFeesAmount");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool PenaltyFeesAmountSpecified {
            get {
                return this.penaltyFeesAmountFieldSpecified;
            }
            set {
                this.penaltyFeesAmountFieldSpecified = value;
                this.RaisePropertyChanged("PenaltyFeesAmountSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=18)]
        public double PerSegServiceFee {
            get {
                return this.perSegServiceFeeField;
            }
            set {
                this.perSegServiceFeeField = value;
                this.RaisePropertyChanged("PerSegServiceFee");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool PerSegServiceFeeSpecified {
            get {
                return this.perSegServiceFeeFieldSpecified;
            }
            set {
                this.perSegServiceFeeFieldSpecified = value;
                this.RaisePropertyChanged("PerSegServiceFeeSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=19)]
        public double PerPaxPerSegServiceFee {
            get {
                return this.perPaxPerSegServiceFeeField;
            }
            set {
                this.perPaxPerSegServiceFeeField = value;
                this.RaisePropertyChanged("PerPaxPerSegServiceFee");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool PerPaxPerSegServiceFeeSpecified {
            get {
                return this.perPaxPerSegServiceFeeFieldSpecified;
            }
            set {
                this.perPaxPerSegServiceFeeFieldSpecified = value;
                this.RaisePropertyChanged("PerPaxPerSegServiceFeeSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=20)]
        public double PercentageCommAmount {
            get {
                return this.percentageCommAmountField;
            }
            set {
                this.percentageCommAmountField = value;
                this.RaisePropertyChanged("PercentageCommAmount");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool PercentageCommAmountSpecified {
            get {
                return this.percentageCommAmountFieldSpecified;
            }
            set {
                this.percentageCommAmountFieldSpecified = value;
                this.RaisePropertyChanged("PercentageCommAmountSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=21)]
        public double TotalAmount {
            get {
                return this.totalAmountField;
            }
            set {
                this.totalAmountField = value;
                this.RaisePropertyChanged("TotalAmount");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool TotalAmountSpecified {
            get {
                return this.totalAmountFieldSpecified;
            }
            set {
                this.totalAmountFieldSpecified = value;
                this.RaisePropertyChanged("TotalAmountSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=22)]
        public string Initiator {
            get {
                return this.initiatorField;
            }
            set {
                this.initiatorField = value;
                this.RaisePropertyChanged("Initiator");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=23)]
        public string AgencyName {
            get {
                return this.agencyNameField;
            }
            set {
                this.agencyNameField = value;
                this.RaisePropertyChanged("AgencyName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=24)]
        public string TravelType {
            get {
                return this.travelTypeField;
            }
            set {
                this.travelTypeField = value;
                this.RaisePropertyChanged("TravelType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=25)]
        public double TotalAirportTax {
            get {
                return this.totalAirportTaxField;
            }
            set {
                this.totalAirportTaxField = value;
                this.RaisePropertyChanged("TotalAirportTax");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool TotalAirportTaxSpecified {
            get {
                return this.totalAirportTaxFieldSpecified;
            }
            set {
                this.totalAirportTaxFieldSpecified = value;
                this.RaisePropertyChanged("TotalAirportTaxSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=26)]
        public double TotalOtherTax {
            get {
                return this.totalOtherTaxField;
            }
            set {
                this.totalOtherTaxField = value;
                this.RaisePropertyChanged("TotalOtherTax");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool TotalOtherTaxSpecified {
            get {
                return this.totalOtherTaxFieldSpecified;
            }
            set {
                this.totalOtherTaxFieldSpecified = value;
                this.RaisePropertyChanged("TotalOtherTaxSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=27)]
        public string MaskedCreditcardNumber {
            get {
                return this.maskedCreditcardNumberField;
            }
            set {
                this.maskedCreditcardNumberField = value;
                this.RaisePropertyChanged("MaskedCreditcardNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=28)]
        public string PaymentTransactionID {
            get {
                return this.paymentTransactionIDField;
            }
            set {
                this.paymentTransactionIDField = value;
                this.RaisePropertyChanged("PaymentTransactionID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=29)]
        public string FlightLegDetailsString {
            get {
                return this.flightLegDetailsStringField;
            }
            set {
                this.flightLegDetailsStringField = value;
                this.RaisePropertyChanged("FlightLegDetailsString");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=30)]
        public string EticketInvoiceNumber {
            get {
                return this.eticketInvoiceNumberField;
            }
            set {
                this.eticketInvoiceNumberField = value;
                this.RaisePropertyChanged("EticketInvoiceNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=31)]
        public string CardHolderName {
            get {
                return this.cardHolderNameField;
            }
            set {
                this.cardHolderNameField = value;
                this.RaisePropertyChanged("CardHolderName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=32)]
        public string TaxNumber {
            get {
                return this.taxNumberField;
            }
            set {
                this.taxNumberField = value;
                this.RaisePropertyChanged("TaxNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=33)]
        public string TaxOffice {
            get {
                return this.taxOfficeField;
            }
            set {
                this.taxOfficeField = value;
                this.RaisePropertyChanged("TaxOffice");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class TravelAgentDetails : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string userIDField;
        
        private string agencyCodeField;
        
        private string agencyNameField;
        
        private string statusField;
        
        private string agentLastNameField;
        
        private string agentMiddleNameField;
        
        private string agentFirstNameField;
        
        private string dutyCodeField;
        
        private string emailAddressField;
        
        private string descriptionField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string UserID {
            get {
                return this.userIDField;
            }
            set {
                this.userIDField = value;
                this.RaisePropertyChanged("UserID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string AgencyCode {
            get {
                return this.agencyCodeField;
            }
            set {
                this.agencyCodeField = value;
                this.RaisePropertyChanged("AgencyCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string AgencyName {
            get {
                return this.agencyNameField;
            }
            set {
                this.agencyNameField = value;
                this.RaisePropertyChanged("AgencyName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string Status {
            get {
                return this.statusField;
            }
            set {
                this.statusField = value;
                this.RaisePropertyChanged("Status");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string AgentLastName {
            get {
                return this.agentLastNameField;
            }
            set {
                this.agentLastNameField = value;
                this.RaisePropertyChanged("AgentLastName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string AgentMiddleName {
            get {
                return this.agentMiddleNameField;
            }
            set {
                this.agentMiddleNameField = value;
                this.RaisePropertyChanged("AgentMiddleName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string AgentFirstName {
            get {
                return this.agentFirstNameField;
            }
            set {
                this.agentFirstNameField = value;
                this.RaisePropertyChanged("AgentFirstName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string DutyCode {
            get {
                return this.dutyCodeField;
            }
            set {
                this.dutyCodeField = value;
                this.RaisePropertyChanged("DutyCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public string EmailAddress {
            get {
                return this.emailAddressField;
            }
            set {
                this.emailAddressField = value;
                this.RaisePropertyChanged("EmailAddress");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public string Description {
            get {
                return this.descriptionField;
            }
            set {
                this.descriptionField = value;
                this.RaisePropertyChanged("Description");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class AgentAddress : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string addressField;
        
        private string telexField;
        
        private string isdPhoneCodeField;
        
        private string phoneNumberField;
        
        private string faxNumberField;
        
        private string emailAddressField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string Address {
            get {
                return this.addressField;
            }
            set {
                this.addressField = value;
                this.RaisePropertyChanged("Address");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string Telex {
            get {
                return this.telexField;
            }
            set {
                this.telexField = value;
                this.RaisePropertyChanged("Telex");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string IsdPhoneCode {
            get {
                return this.isdPhoneCodeField;
            }
            set {
                this.isdPhoneCodeField = value;
                this.RaisePropertyChanged("IsdPhoneCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string PhoneNumber {
            get {
                return this.phoneNumberField;
            }
            set {
                this.phoneNumberField = value;
                this.RaisePropertyChanged("PhoneNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string FaxNumber {
            get {
                return this.faxNumberField;
            }
            set {
                this.faxNumberField = value;
                this.RaisePropertyChanged("FaxNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string EmailAddress {
            get {
                return this.emailAddressField;
            }
            set {
                this.emailAddressField = value;
                this.RaisePropertyChanged("EmailAddress");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class ErrorType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string errorCodeField;
        
        private string errorValueField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string errorCode {
            get {
                return this.errorCodeField;
            }
            set {
                this.errorCodeField = value;
                this.RaisePropertyChanged("errorCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string errorValue {
            get {
                return this.errorValueField;
            }
            set {
                this.errorValueField = value;
                this.RaisePropertyChanged("errorValue");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class TaxInvoiceFieldType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string fieldNameField;
        
        private string fieldValueField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string FieldName {
            get {
                return this.fieldNameField;
            }
            set {
                this.fieldNameField = value;
                this.RaisePropertyChanged("FieldName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string FieldValue {
            get {
                return this.fieldValueField;
            }
            set {
                this.fieldValueField = value;
                this.RaisePropertyChanged("FieldValue");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class TaxInvoiceType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string taxInvoiceType1Field;
        
        private TaxInvoiceFieldType[] taxInvoiceFieldField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("TaxInvoiceType", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string TaxInvoiceType1 {
            get {
                return this.taxInvoiceType1Field;
            }
            set {
                this.taxInvoiceType1Field = value;
                this.RaisePropertyChanged("TaxInvoiceType1");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("TaxInvoiceField", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public TaxInvoiceFieldType[] TaxInvoiceField {
            get {
                return this.taxInvoiceFieldField;
            }
            set {
                this.taxInvoiceFieldField = value;
                this.RaisePropertyChanged("TaxInvoiceField");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class TaxInvoiceDetailType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string countryCodeField;
        
        private TaxInvoiceType[] taxInvoiceField;
        
        private string defaultInvoiceTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string CountryCode {
            get {
                return this.countryCodeField;
            }
            set {
                this.countryCodeField = value;
                this.RaisePropertyChanged("CountryCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("TaxInvoice", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public TaxInvoiceType[] TaxInvoice {
            get {
                return this.taxInvoiceField;
            }
            set {
                this.taxInvoiceField = value;
                this.RaisePropertyChanged("TaxInvoice");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string DefaultInvoiceType {
            get {
                return this.defaultInvoiceTypeField;
            }
            set {
                this.defaultInvoiceTypeField = value;
                this.RaisePropertyChanged("DefaultInvoiceType");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class AgencyBankingDetail : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string bankNameField;
        
        private string swiftCodeField;
        
        private string sortCodeField;
        
        private string ibanNumberField;
        
        private string accountEmailIdField;
        
        private string accountNameField;
        
        private string accountNumberField;
        
        private string accountSuffixField;
        
        private string bankAccountCurrencyField;
        
        private string bankBSBCodeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string bankName {
            get {
                return this.bankNameField;
            }
            set {
                this.bankNameField = value;
                this.RaisePropertyChanged("bankName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string SwiftCode {
            get {
                return this.swiftCodeField;
            }
            set {
                this.swiftCodeField = value;
                this.RaisePropertyChanged("SwiftCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string SortCode {
            get {
                return this.sortCodeField;
            }
            set {
                this.sortCodeField = value;
                this.RaisePropertyChanged("SortCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string IbanNumber {
            get {
                return this.ibanNumberField;
            }
            set {
                this.ibanNumberField = value;
                this.RaisePropertyChanged("IbanNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string AccountEmailId {
            get {
                return this.accountEmailIdField;
            }
            set {
                this.accountEmailIdField = value;
                this.RaisePropertyChanged("AccountEmailId");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string accountName {
            get {
                return this.accountNameField;
            }
            set {
                this.accountNameField = value;
                this.RaisePropertyChanged("accountName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string accountNumber {
            get {
                return this.accountNumberField;
            }
            set {
                this.accountNumberField = value;
                this.RaisePropertyChanged("accountNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string accountSuffix {
            get {
                return this.accountSuffixField;
            }
            set {
                this.accountSuffixField = value;
                this.RaisePropertyChanged("accountSuffix");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public string bankAccountCurrency {
            get {
                return this.bankAccountCurrencyField;
            }
            set {
                this.bankAccountCurrencyField = value;
                this.RaisePropertyChanged("bankAccountCurrency");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public string bankBSBCode {
            get {
                return this.bankBSBCodeField;
            }
            set {
                this.bankBSBCodeField = value;
                this.RaisePropertyChanged("bankBSBCode");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class AgencyAddress : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string addressOneField;
        
        private string addressTwoField;
        
        private string addressThreeField;
        
        private string cityField;
        
        private string provinceField;
        
        private string zipCodeField;
        
        private string countryNameField;
        
        private string countryCodeField;
        
        private string stateField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AddressOne {
            get {
                return this.addressOneField;
            }
            set {
                this.addressOneField = value;
                this.RaisePropertyChanged("AddressOne");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string AddressTwo {
            get {
                return this.addressTwoField;
            }
            set {
                this.addressTwoField = value;
                this.RaisePropertyChanged("AddressTwo");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string AddressThree {
            get {
                return this.addressThreeField;
            }
            set {
                this.addressThreeField = value;
                this.RaisePropertyChanged("AddressThree");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string City {
            get {
                return this.cityField;
            }
            set {
                this.cityField = value;
                this.RaisePropertyChanged("City");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string Province {
            get {
                return this.provinceField;
            }
            set {
                this.provinceField = value;
                this.RaisePropertyChanged("Province");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string ZipCode {
            get {
                return this.zipCodeField;
            }
            set {
                this.zipCodeField = value;
                this.RaisePropertyChanged("ZipCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string CountryName {
            get {
                return this.countryNameField;
            }
            set {
                this.countryNameField = value;
                this.RaisePropertyChanged("CountryName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string CountryCode {
            get {
                return this.countryCodeField;
            }
            set {
                this.countryCodeField = value;
                this.RaisePropertyChanged("CountryCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public string State {
            get {
                return this.stateField;
            }
            set {
                this.stateField = value;
                this.RaisePropertyChanged("State");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class AgencyContactDetails : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string agencyManagerFirstNameField;
        
        private string agencyManagerLastNameField;
        
        private string managerTitleField;
        
        private AgencyAddress locationAddressField;
        
        private AgencyAddress bankAddressField;
        
        private AgencyAddress mailingAddressField;
        
        private AgencyAddress managerContactAddressField;
        
        private string websiteField;
        
        private string emailAddressField;
        
        private string phoneNumberField;
        
        private string isdPhoneCodeField;
        
        private string altPhoneNumberField;
        
        private string isdCodeAltPhoneField;
        
        private string faxField;
        
        private string tollFreeNumberField;
        
        private string languageField;
        
        private string phoneExtensionNumberField;
        
        private string alternateEmailAddressField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AgencyManagerFirstName {
            get {
                return this.agencyManagerFirstNameField;
            }
            set {
                this.agencyManagerFirstNameField = value;
                this.RaisePropertyChanged("AgencyManagerFirstName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string AgencyManagerLastName {
            get {
                return this.agencyManagerLastNameField;
            }
            set {
                this.agencyManagerLastNameField = value;
                this.RaisePropertyChanged("AgencyManagerLastName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string ManagerTitle {
            get {
                return this.managerTitleField;
            }
            set {
                this.managerTitleField = value;
                this.RaisePropertyChanged("ManagerTitle");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public AgencyAddress LocationAddress {
            get {
                return this.locationAddressField;
            }
            set {
                this.locationAddressField = value;
                this.RaisePropertyChanged("LocationAddress");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public AgencyAddress BankAddress {
            get {
                return this.bankAddressField;
            }
            set {
                this.bankAddressField = value;
                this.RaisePropertyChanged("BankAddress");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public AgencyAddress MailingAddress {
            get {
                return this.mailingAddressField;
            }
            set {
                this.mailingAddressField = value;
                this.RaisePropertyChanged("MailingAddress");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public AgencyAddress ManagerContactAddress {
            get {
                return this.managerContactAddressField;
            }
            set {
                this.managerContactAddressField = value;
                this.RaisePropertyChanged("ManagerContactAddress");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string Website {
            get {
                return this.websiteField;
            }
            set {
                this.websiteField = value;
                this.RaisePropertyChanged("Website");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public string EmailAddress {
            get {
                return this.emailAddressField;
            }
            set {
                this.emailAddressField = value;
                this.RaisePropertyChanged("EmailAddress");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public string PhoneNumber {
            get {
                return this.phoneNumberField;
            }
            set {
                this.phoneNumberField = value;
                this.RaisePropertyChanged("PhoneNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=10)]
        public string IsdPhoneCode {
            get {
                return this.isdPhoneCodeField;
            }
            set {
                this.isdPhoneCodeField = value;
                this.RaisePropertyChanged("IsdPhoneCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=11)]
        public string AltPhoneNumber {
            get {
                return this.altPhoneNumberField;
            }
            set {
                this.altPhoneNumberField = value;
                this.RaisePropertyChanged("AltPhoneNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=12)]
        public string IsdCodeAltPhone {
            get {
                return this.isdCodeAltPhoneField;
            }
            set {
                this.isdCodeAltPhoneField = value;
                this.RaisePropertyChanged("IsdCodeAltPhone");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=13)]
        public string Fax {
            get {
                return this.faxField;
            }
            set {
                this.faxField = value;
                this.RaisePropertyChanged("Fax");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=14)]
        public string TollFreeNumber {
            get {
                return this.tollFreeNumberField;
            }
            set {
                this.tollFreeNumberField = value;
                this.RaisePropertyChanged("TollFreeNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=15)]
        public string Language {
            get {
                return this.languageField;
            }
            set {
                this.languageField = value;
                this.RaisePropertyChanged("Language");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=16)]
        public string PhoneExtensionNumber {
            get {
                return this.phoneExtensionNumberField;
            }
            set {
                this.phoneExtensionNumberField = value;
                this.RaisePropertyChanged("PhoneExtensionNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=17)]
        public string AlternateEmailAddress {
            get {
                return this.alternateEmailAddressField;
            }
            set {
                this.alternateEmailAddressField = value;
                this.RaisePropertyChanged("AlternateEmailAddress");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class CreateAgencyRegistrationRS : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string agencyCodeField;
        
        private string corporateIdField;
        
        private string tourOperatorCodeField;
        
        private ErrorType errorTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AgencyCode {
            get {
                return this.agencyCodeField;
            }
            set {
                this.agencyCodeField = value;
                this.RaisePropertyChanged("AgencyCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string CorporateId {
            get {
                return this.corporateIdField;
            }
            set {
                this.corporateIdField = value;
                this.RaisePropertyChanged("CorporateId");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string TourOperatorCode {
            get {
                return this.tourOperatorCodeField;
            }
            set {
                this.tourOperatorCodeField = value;
                this.RaisePropertyChanged("TourOperatorCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public ErrorType ErrorType {
            get {
                return this.errorTypeField;
            }
            set {
                this.errorTypeField = value;
                this.RaisePropertyChanged("ErrorType");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class createAgencyRegistrationRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.TourOperatorList.CreateAgencyRegistrationRQ CreateAgencyRegistrationRQ;
        
        public createAgencyRegistrationRequest() {
        }
        
        public createAgencyRegistrationRequest(Ubimecs.IBS.Services.TourOperatorList.CreateAgencyRegistrationRQ CreateAgencyRegistrationRQ) {
            this.CreateAgencyRegistrationRQ = CreateAgencyRegistrationRQ;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class createAgencyRegistrationResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.TourOperatorList.CreateAgencyRegistrationRS CreateAgencyRegistrationRS;
        
        public createAgencyRegistrationResponse() {
        }
        
        public createAgencyRegistrationResponse(Ubimecs.IBS.Services.TourOperatorList.CreateAgencyRegistrationRS CreateAgencyRegistrationRS) {
            this.CreateAgencyRegistrationRS = CreateAgencyRegistrationRS;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class CreateTravelAgentRQ : object, System.ComponentModel.INotifyPropertyChanged {
        
        private BookingChannelKeyType bookingChannelField;
        
        private string airlineCodeField;
        
        private string userIDField;
        
        private string localeField;
        
        private string firstNameField;
        
        private string lastNameField;
        
        private string countryCodeField;
        
        private string agencyCodeField;
        
        private string dutyCodeField;
        
        private bool userLogonLockField;
        
        private bool userLogonLockFieldSpecified;
        
        private string middleNameField;
        
        private string descriptionField;
        
        private AgentAddress addressField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public BookingChannelKeyType BookingChannel {
            get {
                return this.bookingChannelField;
            }
            set {
                this.bookingChannelField = value;
                this.RaisePropertyChanged("BookingChannel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string AirlineCode {
            get {
                return this.airlineCodeField;
            }
            set {
                this.airlineCodeField = value;
                this.RaisePropertyChanged("AirlineCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string UserID {
            get {
                return this.userIDField;
            }
            set {
                this.userIDField = value;
                this.RaisePropertyChanged("UserID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string Locale {
            get {
                return this.localeField;
            }
            set {
                this.localeField = value;
                this.RaisePropertyChanged("Locale");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string FirstName {
            get {
                return this.firstNameField;
            }
            set {
                this.firstNameField = value;
                this.RaisePropertyChanged("FirstName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string LastName {
            get {
                return this.lastNameField;
            }
            set {
                this.lastNameField = value;
                this.RaisePropertyChanged("LastName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string CountryCode {
            get {
                return this.countryCodeField;
            }
            set {
                this.countryCodeField = value;
                this.RaisePropertyChanged("CountryCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string AgencyCode {
            get {
                return this.agencyCodeField;
            }
            set {
                this.agencyCodeField = value;
                this.RaisePropertyChanged("AgencyCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public string DutyCode {
            get {
                return this.dutyCodeField;
            }
            set {
                this.dutyCodeField = value;
                this.RaisePropertyChanged("DutyCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public bool UserLogonLock {
            get {
                return this.userLogonLockField;
            }
            set {
                this.userLogonLockField = value;
                this.RaisePropertyChanged("UserLogonLock");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool UserLogonLockSpecified {
            get {
                return this.userLogonLockFieldSpecified;
            }
            set {
                this.userLogonLockFieldSpecified = value;
                this.RaisePropertyChanged("UserLogonLockSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=10)]
        public string MiddleName {
            get {
                return this.middleNameField;
            }
            set {
                this.middleNameField = value;
                this.RaisePropertyChanged("MiddleName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=11)]
        public string Description {
            get {
                return this.descriptionField;
            }
            set {
                this.descriptionField = value;
                this.RaisePropertyChanged("Description");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=12)]
        public AgentAddress Address {
            get {
                return this.addressField;
            }
            set {
                this.addressField = value;
                this.RaisePropertyChanged("Address");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class CreateTravelAgentRS : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string userIDField;
        
        private string agencyCodeField;
        
        private string statusField;
        
        private ErrorType errorTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string UserID {
            get {
                return this.userIDField;
            }
            set {
                this.userIDField = value;
                this.RaisePropertyChanged("UserID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string AgencyCode {
            get {
                return this.agencyCodeField;
            }
            set {
                this.agencyCodeField = value;
                this.RaisePropertyChanged("AgencyCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string Status {
            get {
                return this.statusField;
            }
            set {
                this.statusField = value;
                this.RaisePropertyChanged("Status");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public ErrorType ErrorType {
            get {
                return this.errorTypeField;
            }
            set {
                this.errorTypeField = value;
                this.RaisePropertyChanged("ErrorType");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class createTravelAgentRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.TourOperatorList.CreateTravelAgentRQ CreateTravelAgentRQ;
        
        public createTravelAgentRequest() {
        }
        
        public createTravelAgentRequest(Ubimecs.IBS.Services.TourOperatorList.CreateTravelAgentRQ CreateTravelAgentRQ) {
            this.CreateTravelAgentRQ = CreateTravelAgentRQ;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class createTravelAgentResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.TourOperatorList.CreateTravelAgentRS CreateTravelAgentRS;
        
        public createTravelAgentResponse() {
        }
        
        public createTravelAgentResponse(Ubimecs.IBS.Services.TourOperatorList.CreateTravelAgentRS CreateTravelAgentRS) {
            this.CreateTravelAgentRS = CreateTravelAgentRS;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class DeactivateTravelAgentRQ : object, System.ComponentModel.INotifyPropertyChanged {
        
        private BookingChannelKeyType bookingChannelField;
        
        private string airlineCodeField;
        
        private string userIDField;
        
        private string agencyCodeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public BookingChannelKeyType BookingChannel {
            get {
                return this.bookingChannelField;
            }
            set {
                this.bookingChannelField = value;
                this.RaisePropertyChanged("BookingChannel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string AirlineCode {
            get {
                return this.airlineCodeField;
            }
            set {
                this.airlineCodeField = value;
                this.RaisePropertyChanged("AirlineCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string UserID {
            get {
                return this.userIDField;
            }
            set {
                this.userIDField = value;
                this.RaisePropertyChanged("UserID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string AgencyCode {
            get {
                return this.agencyCodeField;
            }
            set {
                this.agencyCodeField = value;
                this.RaisePropertyChanged("AgencyCode");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class DeactivateTravelAgentRS : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string userIDField;
        
        private string agencyCodeField;
        
        private string statusField;
        
        private ErrorType errorTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string UserID {
            get {
                return this.userIDField;
            }
            set {
                this.userIDField = value;
                this.RaisePropertyChanged("UserID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string AgencyCode {
            get {
                return this.agencyCodeField;
            }
            set {
                this.agencyCodeField = value;
                this.RaisePropertyChanged("AgencyCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string Status {
            get {
                return this.statusField;
            }
            set {
                this.statusField = value;
                this.RaisePropertyChanged("Status");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public ErrorType ErrorType {
            get {
                return this.errorTypeField;
            }
            set {
                this.errorTypeField = value;
                this.RaisePropertyChanged("ErrorType");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class deactivateTravelAgentRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.TourOperatorList.DeactivateTravelAgentRQ DeactivateTravelAgentRQ;
        
        public deactivateTravelAgentRequest() {
        }
        
        public deactivateTravelAgentRequest(Ubimecs.IBS.Services.TourOperatorList.DeactivateTravelAgentRQ DeactivateTravelAgentRQ) {
            this.DeactivateTravelAgentRQ = DeactivateTravelAgentRQ;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class deactivateTravelAgentResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.TourOperatorList.DeactivateTravelAgentRS DeactivateTravelAgentRS;
        
        public deactivateTravelAgentResponse() {
        }
        
        public deactivateTravelAgentResponse(Ubimecs.IBS.Services.TourOperatorList.DeactivateTravelAgentRS DeactivateTravelAgentRS) {
            this.DeactivateTravelAgentRS = DeactivateTravelAgentRS;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class ListTravelAgentsRQ : object, System.ComponentModel.INotifyPropertyChanged {
        
        private BookingChannelKeyType bookingChannelField;
        
        private string airlineCodeField;
        
        private string userIDField;
        
        private string agencyCodeField;
        
        private string statusField;
        
        private long startIndexField;
        
        private bool startIndexFieldSpecified;
        
        private long endIndexField;
        
        private bool endIndexFieldSpecified;
        
        private string agentLastNameField;
        
        private string agentMiddleNameField;
        
        private string agentFirstNameField;
        
        private string dutyCodeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public BookingChannelKeyType BookingChannel {
            get {
                return this.bookingChannelField;
            }
            set {
                this.bookingChannelField = value;
                this.RaisePropertyChanged("BookingChannel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string AirlineCode {
            get {
                return this.airlineCodeField;
            }
            set {
                this.airlineCodeField = value;
                this.RaisePropertyChanged("AirlineCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string UserID {
            get {
                return this.userIDField;
            }
            set {
                this.userIDField = value;
                this.RaisePropertyChanged("UserID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string AgencyCode {
            get {
                return this.agencyCodeField;
            }
            set {
                this.agencyCodeField = value;
                this.RaisePropertyChanged("AgencyCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string Status {
            get {
                return this.statusField;
            }
            set {
                this.statusField = value;
                this.RaisePropertyChanged("Status");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public long StartIndex {
            get {
                return this.startIndexField;
            }
            set {
                this.startIndexField = value;
                this.RaisePropertyChanged("StartIndex");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool StartIndexSpecified {
            get {
                return this.startIndexFieldSpecified;
            }
            set {
                this.startIndexFieldSpecified = value;
                this.RaisePropertyChanged("StartIndexSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public long EndIndex {
            get {
                return this.endIndexField;
            }
            set {
                this.endIndexField = value;
                this.RaisePropertyChanged("EndIndex");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool EndIndexSpecified {
            get {
                return this.endIndexFieldSpecified;
            }
            set {
                this.endIndexFieldSpecified = value;
                this.RaisePropertyChanged("EndIndexSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string AgentLastName {
            get {
                return this.agentLastNameField;
            }
            set {
                this.agentLastNameField = value;
                this.RaisePropertyChanged("AgentLastName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public string AgentMiddleName {
            get {
                return this.agentMiddleNameField;
            }
            set {
                this.agentMiddleNameField = value;
                this.RaisePropertyChanged("AgentMiddleName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public string AgentFirstName {
            get {
                return this.agentFirstNameField;
            }
            set {
                this.agentFirstNameField = value;
                this.RaisePropertyChanged("AgentFirstName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=10)]
        public string DutyCode {
            get {
                return this.dutyCodeField;
            }
            set {
                this.dutyCodeField = value;
                this.RaisePropertyChanged("DutyCode");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class ListTravelAgentsRS : object, System.ComponentModel.INotifyPropertyChanged {
        
        private long totalAgentCountField;
        
        private bool totalAgentCountFieldSpecified;
        
        private TravelAgentDetails[] travelAgentDetailsField;
        
        private ErrorType errorTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public long TotalAgentCount {
            get {
                return this.totalAgentCountField;
            }
            set {
                this.totalAgentCountField = value;
                this.RaisePropertyChanged("TotalAgentCount");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool TotalAgentCountSpecified {
            get {
                return this.totalAgentCountFieldSpecified;
            }
            set {
                this.totalAgentCountFieldSpecified = value;
                this.RaisePropertyChanged("TotalAgentCountSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("TravelAgentDetails", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public TravelAgentDetails[] TravelAgentDetails {
            get {
                return this.travelAgentDetailsField;
            }
            set {
                this.travelAgentDetailsField = value;
                this.RaisePropertyChanged("TravelAgentDetails");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public ErrorType ErrorType {
            get {
                return this.errorTypeField;
            }
            set {
                this.errorTypeField = value;
                this.RaisePropertyChanged("ErrorType");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class listTravelAgentsRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.TourOperatorList.ListTravelAgentsRQ ListTravelAgentsRQ;
        
        public listTravelAgentsRequest() {
        }
        
        public listTravelAgentsRequest(Ubimecs.IBS.Services.TourOperatorList.ListTravelAgentsRQ ListTravelAgentsRQ) {
            this.ListTravelAgentsRQ = ListTravelAgentsRQ;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class listTravelAgentsResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.TourOperatorList.ListTravelAgentsRS ListTravelAgentsRS;
        
        public listTravelAgentsResponse() {
        }
        
        public listTravelAgentsResponse(Ubimecs.IBS.Services.TourOperatorList.ListTravelAgentsRS ListTravelAgentsRS) {
            this.ListTravelAgentsRS = ListTravelAgentsRS;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class ModifyAgencyProfileRQ : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string airlineCodeField;
        
        private BookingChannelKeyType bookingChannelField;
        
        private string agencyCodeField;
        
        private string corporateIdField;
        
        private string tourOperatorCodeField;
        
        private string internetLoginPasswordField;
        
        private string internetCreditUsagePasswordField;
        
        private AgencyContactDetails agencyContactDetailsField;
        
        private AgencyBankingDetail agencyBankDetailsField;
        
        private string businessRegistrationNumberField;
        
        private string reportingOfficeField;
        
        private TaxInvoiceDetailType taxInvoiceDetailField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AirlineCode {
            get {
                return this.airlineCodeField;
            }
            set {
                this.airlineCodeField = value;
                this.RaisePropertyChanged("AirlineCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public BookingChannelKeyType BookingChannel {
            get {
                return this.bookingChannelField;
            }
            set {
                this.bookingChannelField = value;
                this.RaisePropertyChanged("BookingChannel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string AgencyCode {
            get {
                return this.agencyCodeField;
            }
            set {
                this.agencyCodeField = value;
                this.RaisePropertyChanged("AgencyCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string CorporateId {
            get {
                return this.corporateIdField;
            }
            set {
                this.corporateIdField = value;
                this.RaisePropertyChanged("CorporateId");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string TourOperatorCode {
            get {
                return this.tourOperatorCodeField;
            }
            set {
                this.tourOperatorCodeField = value;
                this.RaisePropertyChanged("TourOperatorCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string InternetLoginPassword {
            get {
                return this.internetLoginPasswordField;
            }
            set {
                this.internetLoginPasswordField = value;
                this.RaisePropertyChanged("InternetLoginPassword");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string InternetCreditUsagePassword {
            get {
                return this.internetCreditUsagePasswordField;
            }
            set {
                this.internetCreditUsagePasswordField = value;
                this.RaisePropertyChanged("InternetCreditUsagePassword");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public AgencyContactDetails AgencyContactDetails {
            get {
                return this.agencyContactDetailsField;
            }
            set {
                this.agencyContactDetailsField = value;
                this.RaisePropertyChanged("AgencyContactDetails");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public AgencyBankingDetail AgencyBankDetails {
            get {
                return this.agencyBankDetailsField;
            }
            set {
                this.agencyBankDetailsField = value;
                this.RaisePropertyChanged("AgencyBankDetails");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public string BusinessRegistrationNumber {
            get {
                return this.businessRegistrationNumberField;
            }
            set {
                this.businessRegistrationNumberField = value;
                this.RaisePropertyChanged("BusinessRegistrationNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=10)]
        public string ReportingOffice {
            get {
                return this.reportingOfficeField;
            }
            set {
                this.reportingOfficeField = value;
                this.RaisePropertyChanged("ReportingOffice");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=11)]
        public TaxInvoiceDetailType TaxInvoiceDetail {
            get {
                return this.taxInvoiceDetailField;
            }
            set {
                this.taxInvoiceDetailField = value;
                this.RaisePropertyChanged("TaxInvoiceDetail");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class ModifyAgencyProfileRS : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string agencyCodeField;
        
        private string corporateIdField;
        
        private string tourOperatorCodeField;
        
        private ErrorType errorTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AgencyCode {
            get {
                return this.agencyCodeField;
            }
            set {
                this.agencyCodeField = value;
                this.RaisePropertyChanged("AgencyCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string CorporateId {
            get {
                return this.corporateIdField;
            }
            set {
                this.corporateIdField = value;
                this.RaisePropertyChanged("CorporateId");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string TourOperatorCode {
            get {
                return this.tourOperatorCodeField;
            }
            set {
                this.tourOperatorCodeField = value;
                this.RaisePropertyChanged("TourOperatorCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public ErrorType ErrorType {
            get {
                return this.errorTypeField;
            }
            set {
                this.errorTypeField = value;
                this.RaisePropertyChanged("ErrorType");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class modifyAgencyProfileRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.TourOperatorList.ModifyAgencyProfileRQ ModifyAgencyProfileRQ;
        
        public modifyAgencyProfileRequest() {
        }
        
        public modifyAgencyProfileRequest(Ubimecs.IBS.Services.TourOperatorList.ModifyAgencyProfileRQ ModifyAgencyProfileRQ) {
            this.ModifyAgencyProfileRQ = ModifyAgencyProfileRQ;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class modifyAgencyProfileResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.TourOperatorList.ModifyAgencyProfileRS ModifyAgencyProfileRS;
        
        public modifyAgencyProfileResponse() {
        }
        
        public modifyAgencyProfileResponse(Ubimecs.IBS.Services.TourOperatorList.ModifyAgencyProfileRS ModifyAgencyProfileRS) {
            this.ModifyAgencyProfileRS = ModifyAgencyProfileRS;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class ModifyTravelAgentRQ : object, System.ComponentModel.INotifyPropertyChanged {
        
        private BookingChannelKeyType bookingChannelField;
        
        private string airlineCodeField;
        
        private string userIDField;
        
        private string localeField;
        
        private string firstNameField;
        
        private string lastNameField;
        
        private string countryCodeField;
        
        private string agencyCodeField;
        
        private string dutyCodeField;
        
        private bool userLogonLockField;
        
        private string middleInitialField;
        
        private bool isDefaultAgentField;
        
        private string descriptionField;
        
        private AgentAddress addressField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public BookingChannelKeyType BookingChannel {
            get {
                return this.bookingChannelField;
            }
            set {
                this.bookingChannelField = value;
                this.RaisePropertyChanged("BookingChannel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string AirlineCode {
            get {
                return this.airlineCodeField;
            }
            set {
                this.airlineCodeField = value;
                this.RaisePropertyChanged("AirlineCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string UserID {
            get {
                return this.userIDField;
            }
            set {
                this.userIDField = value;
                this.RaisePropertyChanged("UserID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string Locale {
            get {
                return this.localeField;
            }
            set {
                this.localeField = value;
                this.RaisePropertyChanged("Locale");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string FirstName {
            get {
                return this.firstNameField;
            }
            set {
                this.firstNameField = value;
                this.RaisePropertyChanged("FirstName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string LastName {
            get {
                return this.lastNameField;
            }
            set {
                this.lastNameField = value;
                this.RaisePropertyChanged("LastName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string CountryCode {
            get {
                return this.countryCodeField;
            }
            set {
                this.countryCodeField = value;
                this.RaisePropertyChanged("CountryCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string AgencyCode {
            get {
                return this.agencyCodeField;
            }
            set {
                this.agencyCodeField = value;
                this.RaisePropertyChanged("AgencyCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public string DutyCode {
            get {
                return this.dutyCodeField;
            }
            set {
                this.dutyCodeField = value;
                this.RaisePropertyChanged("DutyCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public bool UserLogonLock {
            get {
                return this.userLogonLockField;
            }
            set {
                this.userLogonLockField = value;
                this.RaisePropertyChanged("UserLogonLock");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=10)]
        public string MiddleInitial {
            get {
                return this.middleInitialField;
            }
            set {
                this.middleInitialField = value;
                this.RaisePropertyChanged("MiddleInitial");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=11)]
        public bool IsDefaultAgent {
            get {
                return this.isDefaultAgentField;
            }
            set {
                this.isDefaultAgentField = value;
                this.RaisePropertyChanged("IsDefaultAgent");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=12)]
        public string Description {
            get {
                return this.descriptionField;
            }
            set {
                this.descriptionField = value;
                this.RaisePropertyChanged("Description");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=13)]
        public AgentAddress Address {
            get {
                return this.addressField;
            }
            set {
                this.addressField = value;
                this.RaisePropertyChanged("Address");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class ModifyTravelAgentRS : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string userIDField;
        
        private string agencyCodeField;
        
        private string statusField;
        
        private ErrorType errorTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string UserID {
            get {
                return this.userIDField;
            }
            set {
                this.userIDField = value;
                this.RaisePropertyChanged("UserID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string AgencyCode {
            get {
                return this.agencyCodeField;
            }
            set {
                this.agencyCodeField = value;
                this.RaisePropertyChanged("AgencyCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string Status {
            get {
                return this.statusField;
            }
            set {
                this.statusField = value;
                this.RaisePropertyChanged("Status");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public ErrorType ErrorType {
            get {
                return this.errorTypeField;
            }
            set {
                this.errorTypeField = value;
                this.RaisePropertyChanged("ErrorType");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class modifyTravelAgentRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.TourOperatorList.ModifyTravelAgentRQ ModifyTravelAgentRQ;
        
        public modifyTravelAgentRequest() {
        }
        
        public modifyTravelAgentRequest(Ubimecs.IBS.Services.TourOperatorList.ModifyTravelAgentRQ ModifyTravelAgentRQ) {
            this.ModifyTravelAgentRQ = ModifyTravelAgentRQ;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class modifyTravelAgentResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.TourOperatorList.ModifyTravelAgentRS ModifyTravelAgentRS;
        
        public modifyTravelAgentResponse() {
        }
        
        public modifyTravelAgentResponse(Ubimecs.IBS.Services.TourOperatorList.ModifyTravelAgentRS ModifyTravelAgentRS) {
            this.ModifyTravelAgentRS = ModifyTravelAgentRS;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class RetrieveAgencyCreditRQ : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string airlineCodeField;
        
        private BookingChannelKeyType bookingChannelField;
        
        private string profileIdField;
        
        private string agencyCodeField;
        
        private string corporateIdField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AirlineCode {
            get {
                return this.airlineCodeField;
            }
            set {
                this.airlineCodeField = value;
                this.RaisePropertyChanged("AirlineCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public BookingChannelKeyType BookingChannel {
            get {
                return this.bookingChannelField;
            }
            set {
                this.bookingChannelField = value;
                this.RaisePropertyChanged("BookingChannel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string ProfileId {
            get {
                return this.profileIdField;
            }
            set {
                this.profileIdField = value;
                this.RaisePropertyChanged("ProfileId");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string AgencyCode {
            get {
                return this.agencyCodeField;
            }
            set {
                this.agencyCodeField = value;
                this.RaisePropertyChanged("AgencyCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string CorporateId {
            get {
                return this.corporateIdField;
            }
            set {
                this.corporateIdField = value;
                this.RaisePropertyChanged("CorporateId");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class RetrieveAgencyCreditRS : object, System.ComponentModel.INotifyPropertyChanged {
        
        private double totalAmountAvaialableField;
        
        private bool totalAmountAvaialableFieldSpecified;
        
        private string currencyField;
        
        private ErrorType errorTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public double TotalAmountAvaialable {
            get {
                return this.totalAmountAvaialableField;
            }
            set {
                this.totalAmountAvaialableField = value;
                this.RaisePropertyChanged("TotalAmountAvaialable");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool TotalAmountAvaialableSpecified {
            get {
                return this.totalAmountAvaialableFieldSpecified;
            }
            set {
                this.totalAmountAvaialableFieldSpecified = value;
                this.RaisePropertyChanged("TotalAmountAvaialableSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string Currency {
            get {
                return this.currencyField;
            }
            set {
                this.currencyField = value;
                this.RaisePropertyChanged("Currency");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public ErrorType ErrorType {
            get {
                return this.errorTypeField;
            }
            set {
                this.errorTypeField = value;
                this.RaisePropertyChanged("ErrorType");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class retrieveAgencyCreditRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.TourOperatorList.RetrieveAgencyCreditRQ RetrieveAgencyCreditRQ;
        
        public retrieveAgencyCreditRequest() {
        }
        
        public retrieveAgencyCreditRequest(Ubimecs.IBS.Services.TourOperatorList.RetrieveAgencyCreditRQ RetrieveAgencyCreditRQ) {
            this.RetrieveAgencyCreditRQ = RetrieveAgencyCreditRQ;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class retrieveAgencyCreditResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.TourOperatorList.RetrieveAgencyCreditRS RetrieveAgencyCreditRS;
        
        public retrieveAgencyCreditResponse() {
        }
        
        public retrieveAgencyCreditResponse(Ubimecs.IBS.Services.TourOperatorList.RetrieveAgencyCreditRS RetrieveAgencyCreditRS) {
            this.RetrieveAgencyCreditRS = RetrieveAgencyCreditRS;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class RetrieveAgentInvoiceDetailsRQ : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string airlineCodeField;
        
        private System.DateTime invoiceDateField;
        
        private string invoiceNumberField;
        
        private string ownerField;
        
        private string fopTypeField;
        
        private string currencyField;
        
        private BookingChannelKeyType bookingChannelField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AirlineCode {
            get {
                return this.airlineCodeField;
            }
            set {
                this.airlineCodeField = value;
                this.RaisePropertyChanged("AirlineCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, DataType="date", Order=1)]
        public System.DateTime InvoiceDate {
            get {
                return this.invoiceDateField;
            }
            set {
                this.invoiceDateField = value;
                this.RaisePropertyChanged("InvoiceDate");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string InvoiceNumber {
            get {
                return this.invoiceNumberField;
            }
            set {
                this.invoiceNumberField = value;
                this.RaisePropertyChanged("InvoiceNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string Owner {
            get {
                return this.ownerField;
            }
            set {
                this.ownerField = value;
                this.RaisePropertyChanged("Owner");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string FopType {
            get {
                return this.fopTypeField;
            }
            set {
                this.fopTypeField = value;
                this.RaisePropertyChanged("FopType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string Currency {
            get {
                return this.currencyField;
            }
            set {
                this.currencyField = value;
                this.RaisePropertyChanged("Currency");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public BookingChannelKeyType BookingChannel {
            get {
                return this.bookingChannelField;
            }
            set {
                this.bookingChannelField = value;
                this.RaisePropertyChanged("BookingChannel");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class RetrieveAgentInvoiceDetailsRS : object, System.ComponentModel.INotifyPropertyChanged {
        
        private AgentInvoiceDetailsType[] agentInvoiceDetailsField;
        
        private ErrorType errorTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("AgentInvoiceDetails", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public AgentInvoiceDetailsType[] AgentInvoiceDetails {
            get {
                return this.agentInvoiceDetailsField;
            }
            set {
                this.agentInvoiceDetailsField = value;
                this.RaisePropertyChanged("AgentInvoiceDetails");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public ErrorType ErrorType {
            get {
                return this.errorTypeField;
            }
            set {
                this.errorTypeField = value;
                this.RaisePropertyChanged("ErrorType");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class retrieveAgentInvoiceDetailsRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.TourOperatorList.RetrieveAgentInvoiceDetailsRQ RetrieveAgentInvoiceDetailsRQ;
        
        public retrieveAgentInvoiceDetailsRequest() {
        }
        
        public retrieveAgentInvoiceDetailsRequest(Ubimecs.IBS.Services.TourOperatorList.RetrieveAgentInvoiceDetailsRQ RetrieveAgentInvoiceDetailsRQ) {
            this.RetrieveAgentInvoiceDetailsRQ = RetrieveAgentInvoiceDetailsRQ;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class retrieveAgentInvoiceDetailsResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.TourOperatorList.RetrieveAgentInvoiceDetailsRS RetrieveAgentInvoiceDetailsRS;
        
        public retrieveAgentInvoiceDetailsResponse() {
        }
        
        public retrieveAgentInvoiceDetailsResponse(Ubimecs.IBS.Services.TourOperatorList.RetrieveAgentInvoiceDetailsRS RetrieveAgentInvoiceDetailsRS) {
            this.RetrieveAgentInvoiceDetailsRS = RetrieveAgentInvoiceDetailsRS;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class RetrieveDutyCodesForAgencyRQ : object, System.ComponentModel.INotifyPropertyChanged {
        
        private BookingChannelKeyType bookingChannelField;
        
        private string airlineCodeField;
        
        private string agencyCodeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public BookingChannelKeyType BookingChannel {
            get {
                return this.bookingChannelField;
            }
            set {
                this.bookingChannelField = value;
                this.RaisePropertyChanged("BookingChannel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string AirlineCode {
            get {
                return this.airlineCodeField;
            }
            set {
                this.airlineCodeField = value;
                this.RaisePropertyChanged("AirlineCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string AgencyCode {
            get {
                return this.agencyCodeField;
            }
            set {
                this.agencyCodeField = value;
                this.RaisePropertyChanged("AgencyCode");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class RetrieveDutyCodesForAgencyRS : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string agencyCodeField;
        
        private DutyCodeDetails[] dutyCodeDetailsField;
        
        private ErrorType errorTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AgencyCode {
            get {
                return this.agencyCodeField;
            }
            set {
                this.agencyCodeField = value;
                this.RaisePropertyChanged("AgencyCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("DutyCodeDetails", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public DutyCodeDetails[] DutyCodeDetails {
            get {
                return this.dutyCodeDetailsField;
            }
            set {
                this.dutyCodeDetailsField = value;
                this.RaisePropertyChanged("DutyCodeDetails");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public ErrorType ErrorType {
            get {
                return this.errorTypeField;
            }
            set {
                this.errorTypeField = value;
                this.RaisePropertyChanged("ErrorType");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class retrieveDutyCodesForAgencyRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.TourOperatorList.RetrieveDutyCodesForAgencyRQ RetrieveDutyCodesForAgencyRQ;
        
        public retrieveDutyCodesForAgencyRequest() {
        }
        
        public retrieveDutyCodesForAgencyRequest(Ubimecs.IBS.Services.TourOperatorList.RetrieveDutyCodesForAgencyRQ RetrieveDutyCodesForAgencyRQ) {
            this.RetrieveDutyCodesForAgencyRQ = RetrieveDutyCodesForAgencyRQ;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class retrieveDutyCodesForAgencyResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.TourOperatorList.RetrieveDutyCodesForAgencyRS RetrieveDutyCodesForAgencyRS;
        
        public retrieveDutyCodesForAgencyResponse() {
        }
        
        public retrieveDutyCodesForAgencyResponse(Ubimecs.IBS.Services.TourOperatorList.RetrieveDutyCodesForAgencyRS RetrieveDutyCodesForAgencyRS) {
            this.RetrieveDutyCodesForAgencyRS = RetrieveDutyCodesForAgencyRS;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class RetrieveFOPDetailsForAgencyRQ : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string agencyCodeField;
        
        private string corporateIdField;
        
        private string tourOperatorCodeField;
        
        private BookingChannelKeyType bookingChannelField;
        
        private string airlineCodeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AgencyCode {
            get {
                return this.agencyCodeField;
            }
            set {
                this.agencyCodeField = value;
                this.RaisePropertyChanged("AgencyCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string CorporateId {
            get {
                return this.corporateIdField;
            }
            set {
                this.corporateIdField = value;
                this.RaisePropertyChanged("CorporateId");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string TourOperatorCode {
            get {
                return this.tourOperatorCodeField;
            }
            set {
                this.tourOperatorCodeField = value;
                this.RaisePropertyChanged("TourOperatorCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public BookingChannelKeyType BookingChannel {
            get {
                return this.bookingChannelField;
            }
            set {
                this.bookingChannelField = value;
                this.RaisePropertyChanged("BookingChannel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string AirlineCode {
            get {
                return this.airlineCodeField;
            }
            set {
                this.airlineCodeField = value;
                this.RaisePropertyChanged("AirlineCode");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class RetrieveFOPDetailsForAgencyRS : object, System.ComponentModel.INotifyPropertyChanged {
        
        private FOPDetails[] fOPDetailsField;
        
        private ErrorType errorTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("FOPDetails", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public FOPDetails[] FOPDetails {
            get {
                return this.fOPDetailsField;
            }
            set {
                this.fOPDetailsField = value;
                this.RaisePropertyChanged("FOPDetails");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public ErrorType ErrorType {
            get {
                return this.errorTypeField;
            }
            set {
                this.errorTypeField = value;
                this.RaisePropertyChanged("ErrorType");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class retrieveFOPDetailsForAgencyRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.TourOperatorList.RetrieveFOPDetailsForAgencyRQ RetrieveFOPDetailsForAgencyRQ;
        
        public retrieveFOPDetailsForAgencyRequest() {
        }
        
        public retrieveFOPDetailsForAgencyRequest(Ubimecs.IBS.Services.TourOperatorList.RetrieveFOPDetailsForAgencyRQ RetrieveFOPDetailsForAgencyRQ) {
            this.RetrieveFOPDetailsForAgencyRQ = RetrieveFOPDetailsForAgencyRQ;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class retrieveFOPDetailsForAgencyResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.TourOperatorList.RetrieveFOPDetailsForAgencyRS RetrieveFOPDetailsForAgencyRS;
        
        public retrieveFOPDetailsForAgencyResponse() {
        }
        
        public retrieveFOPDetailsForAgencyResponse(Ubimecs.IBS.Services.TourOperatorList.RetrieveFOPDetailsForAgencyRS RetrieveFOPDetailsForAgencyRS) {
            this.RetrieveFOPDetailsForAgencyRS = RetrieveFOPDetailsForAgencyRS;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class RetrieveTravelAgentDetailsRQ : object, System.ComponentModel.INotifyPropertyChanged {
        
        private BookingChannelKeyType bookingChannelField;
        
        private string airlineCodeField;
        
        private string userIDField;
        
        private string agencyCodeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public BookingChannelKeyType BookingChannel {
            get {
                return this.bookingChannelField;
            }
            set {
                this.bookingChannelField = value;
                this.RaisePropertyChanged("BookingChannel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string AirlineCode {
            get {
                return this.airlineCodeField;
            }
            set {
                this.airlineCodeField = value;
                this.RaisePropertyChanged("AirlineCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string UserID {
            get {
                return this.userIDField;
            }
            set {
                this.userIDField = value;
                this.RaisePropertyChanged("UserID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string AgencyCode {
            get {
                return this.agencyCodeField;
            }
            set {
                this.agencyCodeField = value;
                this.RaisePropertyChanged("AgencyCode");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class RetrieveTravelAgentDetailsRS : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string userIDField;
        
        private string agencyCodeField;
        
        private string localeField;
        
        private string firstNameField;
        
        private string lastNameField;
        
        private string countryCodeField;
        
        private AgentAddress addressField;
        
        private string dutyCodeField;
        
        private bool userLogonLockField;
        
        private bool userLogonLockFieldSpecified;
        
        private string middleInitialField;
        
        private bool isDefaultAgentField;
        
        private bool isDefaultAgentFieldSpecified;
        
        private string descriptionField;
        
        private string statusField;
        
        private ErrorType errorTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string UserID {
            get {
                return this.userIDField;
            }
            set {
                this.userIDField = value;
                this.RaisePropertyChanged("UserID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string AgencyCode {
            get {
                return this.agencyCodeField;
            }
            set {
                this.agencyCodeField = value;
                this.RaisePropertyChanged("AgencyCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string Locale {
            get {
                return this.localeField;
            }
            set {
                this.localeField = value;
                this.RaisePropertyChanged("Locale");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string FirstName {
            get {
                return this.firstNameField;
            }
            set {
                this.firstNameField = value;
                this.RaisePropertyChanged("FirstName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string LastName {
            get {
                return this.lastNameField;
            }
            set {
                this.lastNameField = value;
                this.RaisePropertyChanged("LastName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string CountryCode {
            get {
                return this.countryCodeField;
            }
            set {
                this.countryCodeField = value;
                this.RaisePropertyChanged("CountryCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public AgentAddress Address {
            get {
                return this.addressField;
            }
            set {
                this.addressField = value;
                this.RaisePropertyChanged("Address");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string DutyCode {
            get {
                return this.dutyCodeField;
            }
            set {
                this.dutyCodeField = value;
                this.RaisePropertyChanged("DutyCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public bool UserLogonLock {
            get {
                return this.userLogonLockField;
            }
            set {
                this.userLogonLockField = value;
                this.RaisePropertyChanged("UserLogonLock");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool UserLogonLockSpecified {
            get {
                return this.userLogonLockFieldSpecified;
            }
            set {
                this.userLogonLockFieldSpecified = value;
                this.RaisePropertyChanged("UserLogonLockSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public string MiddleInitial {
            get {
                return this.middleInitialField;
            }
            set {
                this.middleInitialField = value;
                this.RaisePropertyChanged("MiddleInitial");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=10)]
        public bool IsDefaultAgent {
            get {
                return this.isDefaultAgentField;
            }
            set {
                this.isDefaultAgentField = value;
                this.RaisePropertyChanged("IsDefaultAgent");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool IsDefaultAgentSpecified {
            get {
                return this.isDefaultAgentFieldSpecified;
            }
            set {
                this.isDefaultAgentFieldSpecified = value;
                this.RaisePropertyChanged("IsDefaultAgentSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=11)]
        public string Description {
            get {
                return this.descriptionField;
            }
            set {
                this.descriptionField = value;
                this.RaisePropertyChanged("Description");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=12)]
        public string Status {
            get {
                return this.statusField;
            }
            set {
                this.statusField = value;
                this.RaisePropertyChanged("Status");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=13)]
        public ErrorType ErrorType {
            get {
                return this.errorTypeField;
            }
            set {
                this.errorTypeField = value;
                this.RaisePropertyChanged("ErrorType");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class retrieveTravelAgentDetailsRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.TourOperatorList.RetrieveTravelAgentDetailsRQ RetrieveTravelAgentDetailsRQ;
        
        public retrieveTravelAgentDetailsRequest() {
        }
        
        public retrieveTravelAgentDetailsRequest(Ubimecs.IBS.Services.TourOperatorList.RetrieveTravelAgentDetailsRQ RetrieveTravelAgentDetailsRQ) {
            this.RetrieveTravelAgentDetailsRQ = RetrieveTravelAgentDetailsRQ;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class retrieveTravelAgentDetailsResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.TourOperatorList.RetrieveTravelAgentDetailsRS RetrieveTravelAgentDetailsRS;
        
        public retrieveTravelAgentDetailsResponse() {
        }
        
        public retrieveTravelAgentDetailsResponse(Ubimecs.IBS.Services.TourOperatorList.RetrieveTravelAgentDetailsRS RetrieveTravelAgentDetailsRS) {
            this.RetrieveTravelAgentDetailsRS = RetrieveTravelAgentDetailsRS;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class ViewAgencyProfileRQ : BaseBookingRQ {
        
        private string agencyCodeField;
        
        private string corporateIdField;
        
        private string tourOperatorCodeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AgencyCode {
            get {
                return this.agencyCodeField;
            }
            set {
                this.agencyCodeField = value;
                this.RaisePropertyChanged("AgencyCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string CorporateId {
            get {
                return this.corporateIdField;
            }
            set {
                this.corporateIdField = value;
                this.RaisePropertyChanged("CorporateId");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string TourOperatorCode {
            get {
                return this.tourOperatorCodeField;
            }
            set {
                this.tourOperatorCodeField = value;
                this.RaisePropertyChanged("TourOperatorCode");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class ViewAgencyProfileRS : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string agencyCodeField;
        
        private string agencyNameField;
        
        private string agencyCategoryField;
        
        private string agencyTypeField;
        
        private string reportingOfficeField;
        
        private string registeredLegalNameField;
        
        private string statusField;
        
        private string agencyRegionalNameField;
        
        private string internetLoginPasswordField;
        
        private string internetCreditUsagePasswordField;
        
        private AgencyContactDetails agencyContactDetailsField;
        
        private AgencyCreditDetails agencyCreditDetailsField;
        
        private AgencyCommissionDetails agencyCommissionDetailsField;
        
        private AgencyBankingDetail agencyBankingDetailField;
        
        private string businessRegistrationNumberField;
        
        private CorporateCardDetails corporateCardDetailsField;
        
        private string generalCommentsField;
        
        private TaxInvoiceDetailType taxInvoiceDetailField;
        
        private string[] bookingChannelCodesField;
        
        private string[] agentDutyCodesField;
        
        private ErrorType errorTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AgencyCode {
            get {
                return this.agencyCodeField;
            }
            set {
                this.agencyCodeField = value;
                this.RaisePropertyChanged("AgencyCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string AgencyName {
            get {
                return this.agencyNameField;
            }
            set {
                this.agencyNameField = value;
                this.RaisePropertyChanged("AgencyName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string AgencyCategory {
            get {
                return this.agencyCategoryField;
            }
            set {
                this.agencyCategoryField = value;
                this.RaisePropertyChanged("AgencyCategory");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string AgencyType {
            get {
                return this.agencyTypeField;
            }
            set {
                this.agencyTypeField = value;
                this.RaisePropertyChanged("AgencyType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string ReportingOffice {
            get {
                return this.reportingOfficeField;
            }
            set {
                this.reportingOfficeField = value;
                this.RaisePropertyChanged("ReportingOffice");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string RegisteredLegalName {
            get {
                return this.registeredLegalNameField;
            }
            set {
                this.registeredLegalNameField = value;
                this.RaisePropertyChanged("RegisteredLegalName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string Status {
            get {
                return this.statusField;
            }
            set {
                this.statusField = value;
                this.RaisePropertyChanged("Status");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string AgencyRegionalName {
            get {
                return this.agencyRegionalNameField;
            }
            set {
                this.agencyRegionalNameField = value;
                this.RaisePropertyChanged("AgencyRegionalName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public string InternetLoginPassword {
            get {
                return this.internetLoginPasswordField;
            }
            set {
                this.internetLoginPasswordField = value;
                this.RaisePropertyChanged("InternetLoginPassword");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=9)]
        public string InternetCreditUsagePassword {
            get {
                return this.internetCreditUsagePasswordField;
            }
            set {
                this.internetCreditUsagePasswordField = value;
                this.RaisePropertyChanged("InternetCreditUsagePassword");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=10)]
        public AgencyContactDetails AgencyContactDetails {
            get {
                return this.agencyContactDetailsField;
            }
            set {
                this.agencyContactDetailsField = value;
                this.RaisePropertyChanged("AgencyContactDetails");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=11)]
        public AgencyCreditDetails AgencyCreditDetails {
            get {
                return this.agencyCreditDetailsField;
            }
            set {
                this.agencyCreditDetailsField = value;
                this.RaisePropertyChanged("AgencyCreditDetails");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=12)]
        public AgencyCommissionDetails AgencyCommissionDetails {
            get {
                return this.agencyCommissionDetailsField;
            }
            set {
                this.agencyCommissionDetailsField = value;
                this.RaisePropertyChanged("AgencyCommissionDetails");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=13)]
        public AgencyBankingDetail AgencyBankingDetail {
            get {
                return this.agencyBankingDetailField;
            }
            set {
                this.agencyBankingDetailField = value;
                this.RaisePropertyChanged("AgencyBankingDetail");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=14)]
        public string BusinessRegistrationNumber {
            get {
                return this.businessRegistrationNumberField;
            }
            set {
                this.businessRegistrationNumberField = value;
                this.RaisePropertyChanged("BusinessRegistrationNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=15)]
        public CorporateCardDetails corporateCardDetails {
            get {
                return this.corporateCardDetailsField;
            }
            set {
                this.corporateCardDetailsField = value;
                this.RaisePropertyChanged("corporateCardDetails");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=16)]
        public string GeneralComments {
            get {
                return this.generalCommentsField;
            }
            set {
                this.generalCommentsField = value;
                this.RaisePropertyChanged("GeneralComments");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=17)]
        public TaxInvoiceDetailType TaxInvoiceDetail {
            get {
                return this.taxInvoiceDetailField;
            }
            set {
                this.taxInvoiceDetailField = value;
                this.RaisePropertyChanged("TaxInvoiceDetail");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("BookingChannelCodes", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=18)]
        public string[] BookingChannelCodes {
            get {
                return this.bookingChannelCodesField;
            }
            set {
                this.bookingChannelCodesField = value;
                this.RaisePropertyChanged("BookingChannelCodes");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("AgentDutyCodes", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=19)]
        public string[] AgentDutyCodes {
            get {
                return this.agentDutyCodesField;
            }
            set {
                this.agentDutyCodesField = value;
                this.RaisePropertyChanged("AgentDutyCodes");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=20)]
        public ErrorType ErrorType {
            get {
                return this.errorTypeField;
            }
            set {
                this.errorTypeField = value;
                this.RaisePropertyChanged("ErrorType");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class viewAgencyProfileRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.TourOperatorList.ViewAgencyProfileRQ ViewAgencyProfileRQ;
        
        public viewAgencyProfileRequest() {
        }
        
        public viewAgencyProfileRequest(Ubimecs.IBS.Services.TourOperatorList.ViewAgencyProfileRQ ViewAgencyProfileRQ) {
            this.ViewAgencyProfileRQ = ViewAgencyProfileRQ;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class viewAgencyProfileResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.TourOperatorList.ViewAgencyProfileRS ViewAgencyProfileRS;
        
        public viewAgencyProfileResponse() {
        }
        
        public viewAgencyProfileResponse(Ubimecs.IBS.Services.TourOperatorList.ViewAgencyProfileRS ViewAgencyProfileRS) {
            this.ViewAgencyProfileRS = ViewAgencyProfileRS;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class ActivateTravelAgentRQ : object, System.ComponentModel.INotifyPropertyChanged {
        
        private BookingChannelKeyType bookingChannelField;
        
        private string airlineCodeField;
        
        private string userIDField;
        
        private string agencyCodeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public BookingChannelKeyType BookingChannel {
            get {
                return this.bookingChannelField;
            }
            set {
                this.bookingChannelField = value;
                this.RaisePropertyChanged("BookingChannel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string AirlineCode {
            get {
                return this.airlineCodeField;
            }
            set {
                this.airlineCodeField = value;
                this.RaisePropertyChanged("AirlineCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string UserID {
            get {
                return this.userIDField;
            }
            set {
                this.userIDField = value;
                this.RaisePropertyChanged("UserID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string AgencyCode {
            get {
                return this.agencyCodeField;
            }
            set {
                this.agencyCodeField = value;
                this.RaisePropertyChanged("AgencyCode");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class ActivateTravelAgentRS : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string userIDField;
        
        private string agencyCodeField;
        
        private string statusField;
        
        private ErrorType errorTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string UserID {
            get {
                return this.userIDField;
            }
            set {
                this.userIDField = value;
                this.RaisePropertyChanged("UserID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string AgencyCode {
            get {
                return this.agencyCodeField;
            }
            set {
                this.agencyCodeField = value;
                this.RaisePropertyChanged("AgencyCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string Status {
            get {
                return this.statusField;
            }
            set {
                this.statusField = value;
                this.RaisePropertyChanged("Status");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public ErrorType ErrorType {
            get {
                return this.errorTypeField;
            }
            set {
                this.errorTypeField = value;
                this.RaisePropertyChanged("ErrorType");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class activateTravelAgentRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.TourOperatorList.ActivateTravelAgentRQ ActivateTravelAgentRQ;
        
        public activateTravelAgentRequest() {
        }
        
        public activateTravelAgentRequest(Ubimecs.IBS.Services.TourOperatorList.ActivateTravelAgentRQ ActivateTravelAgentRQ) {
            this.ActivateTravelAgentRQ = ActivateTravelAgentRQ;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class activateTravelAgentResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.TourOperatorList.ActivateTravelAgentRS ActivateTravelAgentRS;
        
        public activateTravelAgentResponse() {
        }
        
        public activateTravelAgentResponse(Ubimecs.IBS.Services.TourOperatorList.ActivateTravelAgentRS ActivateTravelAgentRS) {
            this.ActivateTravelAgentRS = ActivateTravelAgentRS;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class RetrieveAgenciesRQ : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string airlineCodeField;
        
        private BookingChannelKeyType bookingChannelField;
        
        private AgencyCategoryType agencyCategoryField;
        
        private System.DateTime lastModifiedStartDateAndTimeField;
        
        private bool lastModifiedStartDateAndTimeFieldSpecified;
        
        private System.DateTime lastModifiedEndDateAndTimeField;
        
        private bool lastModifiedEndDateAndTimeFieldSpecified;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AirlineCode {
            get {
                return this.airlineCodeField;
            }
            set {
                this.airlineCodeField = value;
                this.RaisePropertyChanged("AirlineCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public BookingChannelKeyType BookingChannel {
            get {
                return this.bookingChannelField;
            }
            set {
                this.bookingChannelField = value;
                this.RaisePropertyChanged("BookingChannel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public AgencyCategoryType AgencyCategory {
            get {
                return this.agencyCategoryField;
            }
            set {
                this.agencyCategoryField = value;
                this.RaisePropertyChanged("AgencyCategory");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public System.DateTime LastModifiedStartDateAndTime {
            get {
                return this.lastModifiedStartDateAndTimeField;
            }
            set {
                this.lastModifiedStartDateAndTimeField = value;
                this.RaisePropertyChanged("LastModifiedStartDateAndTime");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool LastModifiedStartDateAndTimeSpecified {
            get {
                return this.lastModifiedStartDateAndTimeFieldSpecified;
            }
            set {
                this.lastModifiedStartDateAndTimeFieldSpecified = value;
                this.RaisePropertyChanged("LastModifiedStartDateAndTimeSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public System.DateTime LastModifiedEndDateAndTime {
            get {
                return this.lastModifiedEndDateAndTimeField;
            }
            set {
                this.lastModifiedEndDateAndTimeField = value;
                this.RaisePropertyChanged("LastModifiedEndDateAndTime");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool LastModifiedEndDateAndTimeSpecified {
            get {
                return this.lastModifiedEndDateAndTimeFieldSpecified;
            }
            set {
                this.lastModifiedEndDateAndTimeFieldSpecified = value;
                this.RaisePropertyChanged("LastModifiedEndDateAndTimeSpecified");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class RetrieveAgenciesRS : object, System.ComponentModel.INotifyPropertyChanged {
        
        private AgencyDetailsType[] agencyDetailsField;
        
        private ErrorType errorTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("AgencyDetails", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public AgencyDetailsType[] AgencyDetails {
            get {
                return this.agencyDetailsField;
            }
            set {
                this.agencyDetailsField = value;
                this.RaisePropertyChanged("AgencyDetails");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public ErrorType ErrorType {
            get {
                return this.errorTypeField;
            }
            set {
                this.errorTypeField = value;
                this.RaisePropertyChanged("ErrorType");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class retrieveAgenciesRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.TourOperatorList.RetrieveAgenciesRQ RetrieveAgenciesRQ;
        
        public retrieveAgenciesRequest() {
        }
        
        public retrieveAgenciesRequest(Ubimecs.IBS.Services.TourOperatorList.RetrieveAgenciesRQ RetrieveAgenciesRQ) {
            this.RetrieveAgenciesRQ = RetrieveAgenciesRQ;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class retrieveAgenciesResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.TourOperatorList.RetrieveAgenciesRS RetrieveAgenciesRS;
        
        public retrieveAgenciesResponse() {
        }
        
        public retrieveAgenciesResponse(Ubimecs.IBS.Services.TourOperatorList.RetrieveAgenciesRS RetrieveAgenciesRS) {
            this.RetrieveAgenciesRS = RetrieveAgenciesRS;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class AgencyPaymentRQ : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string airlineCodeField;
        
        private BookingChannelKeyType bookingChannelField;
        
        private int noOfEntriesField;
        
        private bool noOfEntriesFieldSpecified;
        
        private CurrencyAmountType totalRemittanceAmtDetailsField;
        
        private string batchNumberField;
        
        private string creditChainCodeField;
        
        private AgencyPaymentActionType actionField;
        
        private AgencyPaymentDetailsType[] agencyPaymentDetailsField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AirlineCode {
            get {
                return this.airlineCodeField;
            }
            set {
                this.airlineCodeField = value;
                this.RaisePropertyChanged("AirlineCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public BookingChannelKeyType BookingChannel {
            get {
                return this.bookingChannelField;
            }
            set {
                this.bookingChannelField = value;
                this.RaisePropertyChanged("BookingChannel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public int NoOfEntries {
            get {
                return this.noOfEntriesField;
            }
            set {
                this.noOfEntriesField = value;
                this.RaisePropertyChanged("NoOfEntries");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool NoOfEntriesSpecified {
            get {
                return this.noOfEntriesFieldSpecified;
            }
            set {
                this.noOfEntriesFieldSpecified = value;
                this.RaisePropertyChanged("NoOfEntriesSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public CurrencyAmountType TotalRemittanceAmtDetails {
            get {
                return this.totalRemittanceAmtDetailsField;
            }
            set {
                this.totalRemittanceAmtDetailsField = value;
                this.RaisePropertyChanged("TotalRemittanceAmtDetails");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string BatchNumber {
            get {
                return this.batchNumberField;
            }
            set {
                this.batchNumberField = value;
                this.RaisePropertyChanged("BatchNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public string CreditChainCode {
            get {
                return this.creditChainCodeField;
            }
            set {
                this.creditChainCodeField = value;
                this.RaisePropertyChanged("CreditChainCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public AgencyPaymentActionType Action {
            get {
                return this.actionField;
            }
            set {
                this.actionField = value;
                this.RaisePropertyChanged("Action");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("AgencyPaymentDetails", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public AgencyPaymentDetailsType[] AgencyPaymentDetails {
            get {
                return this.agencyPaymentDetailsField;
            }
            set {
                this.agencyPaymentDetailsField = value;
                this.RaisePropertyChanged("AgencyPaymentDetails");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public enum AgencyPaymentActionType {
        
        /// <remarks/>
        ADD,
        
        /// <remarks/>
        APPROVE,
        
        /// <remarks/>
        ADD_AND_APPROVE,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class AgencyPaymentRS : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string batchNumberField;
        
        private System.DateTime batchDateField;
        
        private bool batchDateFieldSpecified;
        
        private CurrencyAmountType totalRemittanceAmtDetailsField;
        
        private bool isApprovedField;
        
        private bool isApprovedFieldSpecified;
        
        private System.DateTime approvalDateField;
        
        private bool approvalDateFieldSpecified;
        
        private AgencyPaymentDetailsType[] agencyPaymentDetailsField;
        
        private string userIdField;
        
        private string creditChainCodeField;
        
        private ErrorType errorTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string BatchNumber {
            get {
                return this.batchNumberField;
            }
            set {
                this.batchNumberField = value;
                this.RaisePropertyChanged("BatchNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, DataType="date", Order=1)]
        public System.DateTime BatchDate {
            get {
                return this.batchDateField;
            }
            set {
                this.batchDateField = value;
                this.RaisePropertyChanged("BatchDate");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool BatchDateSpecified {
            get {
                return this.batchDateFieldSpecified;
            }
            set {
                this.batchDateFieldSpecified = value;
                this.RaisePropertyChanged("BatchDateSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public CurrencyAmountType TotalRemittanceAmtDetails {
            get {
                return this.totalRemittanceAmtDetailsField;
            }
            set {
                this.totalRemittanceAmtDetailsField = value;
                this.RaisePropertyChanged("TotalRemittanceAmtDetails");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public bool IsApproved {
            get {
                return this.isApprovedField;
            }
            set {
                this.isApprovedField = value;
                this.RaisePropertyChanged("IsApproved");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool IsApprovedSpecified {
            get {
                return this.isApprovedFieldSpecified;
            }
            set {
                this.isApprovedFieldSpecified = value;
                this.RaisePropertyChanged("IsApprovedSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public System.DateTime ApprovalDate {
            get {
                return this.approvalDateField;
            }
            set {
                this.approvalDateField = value;
                this.RaisePropertyChanged("ApprovalDate");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool ApprovalDateSpecified {
            get {
                return this.approvalDateFieldSpecified;
            }
            set {
                this.approvalDateFieldSpecified = value;
                this.RaisePropertyChanged("ApprovalDateSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("AgencyPaymentDetails", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public AgencyPaymentDetailsType[] AgencyPaymentDetails {
            get {
                return this.agencyPaymentDetailsField;
            }
            set {
                this.agencyPaymentDetailsField = value;
                this.RaisePropertyChanged("AgencyPaymentDetails");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public string UserId {
            get {
                return this.userIdField;
            }
            set {
                this.userIdField = value;
                this.RaisePropertyChanged("UserId");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=7)]
        public string CreditChainCode {
            get {
                return this.creditChainCodeField;
            }
            set {
                this.creditChainCodeField = value;
                this.RaisePropertyChanged("CreditChainCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=8)]
        public ErrorType ErrorType {
            get {
                return this.errorTypeField;
            }
            set {
                this.errorTypeField = value;
                this.RaisePropertyChanged("ErrorType");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class agencyPaymentRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.TourOperatorList.AgencyPaymentRQ AgencyPaymentRQ;
        
        public agencyPaymentRequest() {
        }
        
        public agencyPaymentRequest(Ubimecs.IBS.Services.TourOperatorList.AgencyPaymentRQ AgencyPaymentRQ) {
            this.AgencyPaymentRQ = AgencyPaymentRQ;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class agencyPaymentResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.TourOperatorList.AgencyPaymentRS AgencyPaymentRS;
        
        public agencyPaymentResponse() {
        }
        
        public agencyPaymentResponse(Ubimecs.IBS.Services.TourOperatorList.AgencyPaymentRS AgencyPaymentRS) {
            this.AgencyPaymentRS = AgencyPaymentRS;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class RetrieveAgencyCreditHistoryRQ : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string airlineCodeField;
        
        private BookingChannelKeyType bookingChannelField;
        
        private string agencyCodeField;
        
        private System.DateTime fromDateField;
        
        private System.DateTime toDateField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string AirlineCode {
            get {
                return this.airlineCodeField;
            }
            set {
                this.airlineCodeField = value;
                this.RaisePropertyChanged("AirlineCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public BookingChannelKeyType BookingChannel {
            get {
                return this.bookingChannelField;
            }
            set {
                this.bookingChannelField = value;
                this.RaisePropertyChanged("BookingChannel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string AgencyCode {
            get {
                return this.agencyCodeField;
            }
            set {
                this.agencyCodeField = value;
                this.RaisePropertyChanged("AgencyCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, DataType="date", Order=3)]
        public System.DateTime FromDate {
            get {
                return this.fromDateField;
            }
            set {
                this.fromDateField = value;
                this.RaisePropertyChanged("FromDate");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, DataType="date", Order=4)]
        public System.DateTime ToDate {
            get {
                return this.toDateField;
            }
            set {
                this.toDateField = value;
                this.RaisePropertyChanged("ToDate");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.ibsplc.com/iRes/simpleTypes/")]
    public partial class RetrieveAgencyCreditHistoryRS : object, System.ComponentModel.INotifyPropertyChanged {
        
        private AgencyCreditHistType[] agencyCreditHistoryField;
        
        private ErrorType errorTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("AgencyCreditHistory", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public AgencyCreditHistType[] AgencyCreditHistory {
            get {
                return this.agencyCreditHistoryField;
            }
            set {
                this.agencyCreditHistoryField = value;
                this.RaisePropertyChanged("AgencyCreditHistory");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public ErrorType ErrorType {
            get {
                return this.errorTypeField;
            }
            set {
                this.errorTypeField = value;
                this.RaisePropertyChanged("ErrorType");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class retrieveAgencyCreditHistoryRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.TourOperatorList.RetrieveAgencyCreditHistoryRQ RetrieveAgencyCreditHistoryRQ;
        
        public retrieveAgencyCreditHistoryRequest() {
        }
        
        public retrieveAgencyCreditHistoryRequest(Ubimecs.IBS.Services.TourOperatorList.RetrieveAgencyCreditHistoryRQ RetrieveAgencyCreditHistoryRQ) {
            this.RetrieveAgencyCreditHistoryRQ = RetrieveAgencyCreditHistoryRQ;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class retrieveAgencyCreditHistoryResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.ibsplc.com/iRes/simpleTypes/", Order=0)]
        public Ubimecs.IBS.Services.TourOperatorList.RetrieveAgencyCreditHistoryRS RetrieveAgencyCreditHistoryRS;
        
        public retrieveAgencyCreditHistoryResponse() {
        }
        
        public retrieveAgencyCreditHistoryResponse(Ubimecs.IBS.Services.TourOperatorList.RetrieveAgencyCreditHistoryRS RetrieveAgencyCreditHistoryRS) {
            this.RetrieveAgencyCreditHistoryRS = RetrieveAgencyCreditHistoryRS;
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    public interface AgencyPortChannel : Ubimecs.IBS.Services.TourOperatorList.AgencyPort, System.ServiceModel.IClientChannel {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    public partial class AgencyPortClient : System.ServiceModel.ClientBase<Ubimecs.IBS.Services.TourOperatorList.AgencyPort>, Ubimecs.IBS.Services.TourOperatorList.AgencyPort {
        
        public AgencyPortClient() {
        }
        
        public AgencyPortClient(string endpointConfigurationName) : 
                base(endpointConfigurationName) {
        }
        
        public AgencyPortClient(string endpointConfigurationName, string remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public AgencyPortClient(string endpointConfigurationName, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public AgencyPortClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress) {
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Ubimecs.IBS.Services.TourOperatorList.createAgencyRegistrationResponse Ubimecs.IBS.Services.TourOperatorList.AgencyPort.createAgencyRegistration(Ubimecs.IBS.Services.TourOperatorList.createAgencyRegistrationRequest request) {
            return base.Channel.createAgencyRegistration(request);
        }
        
        public Ubimecs.IBS.Services.TourOperatorList.CreateAgencyRegistrationRS createAgencyRegistration(Ubimecs.IBS.Services.TourOperatorList.CreateAgencyRegistrationRQ CreateAgencyRegistrationRQ) {
            Ubimecs.IBS.Services.TourOperatorList.createAgencyRegistrationRequest inValue = new Ubimecs.IBS.Services.TourOperatorList.createAgencyRegistrationRequest();
            inValue.CreateAgencyRegistrationRQ = CreateAgencyRegistrationRQ;
            Ubimecs.IBS.Services.TourOperatorList.createAgencyRegistrationResponse retVal = ((Ubimecs.IBS.Services.TourOperatorList.AgencyPort)(this)).createAgencyRegistration(inValue);
            return retVal.CreateAgencyRegistrationRS;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.TourOperatorList.createAgencyRegistrationResponse> Ubimecs.IBS.Services.TourOperatorList.AgencyPort.createAgencyRegistrationAsync(Ubimecs.IBS.Services.TourOperatorList.createAgencyRegistrationRequest request) {
            return base.Channel.createAgencyRegistrationAsync(request);
        }
        
        public System.Threading.Tasks.Task<Ubimecs.IBS.Services.TourOperatorList.createAgencyRegistrationResponse> createAgencyRegistrationAsync(Ubimecs.IBS.Services.TourOperatorList.CreateAgencyRegistrationRQ CreateAgencyRegistrationRQ) {
            Ubimecs.IBS.Services.TourOperatorList.createAgencyRegistrationRequest inValue = new Ubimecs.IBS.Services.TourOperatorList.createAgencyRegistrationRequest();
            inValue.CreateAgencyRegistrationRQ = CreateAgencyRegistrationRQ;
            return ((Ubimecs.IBS.Services.TourOperatorList.AgencyPort)(this)).createAgencyRegistrationAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Ubimecs.IBS.Services.TourOperatorList.createTravelAgentResponse Ubimecs.IBS.Services.TourOperatorList.AgencyPort.createTravelAgent(Ubimecs.IBS.Services.TourOperatorList.createTravelAgentRequest request) {
            return base.Channel.createTravelAgent(request);
        }
        
        public Ubimecs.IBS.Services.TourOperatorList.CreateTravelAgentRS createTravelAgent(Ubimecs.IBS.Services.TourOperatorList.CreateTravelAgentRQ CreateTravelAgentRQ) {
            Ubimecs.IBS.Services.TourOperatorList.createTravelAgentRequest inValue = new Ubimecs.IBS.Services.TourOperatorList.createTravelAgentRequest();
            inValue.CreateTravelAgentRQ = CreateTravelAgentRQ;
            Ubimecs.IBS.Services.TourOperatorList.createTravelAgentResponse retVal = ((Ubimecs.IBS.Services.TourOperatorList.AgencyPort)(this)).createTravelAgent(inValue);
            return retVal.CreateTravelAgentRS;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.TourOperatorList.createTravelAgentResponse> Ubimecs.IBS.Services.TourOperatorList.AgencyPort.createTravelAgentAsync(Ubimecs.IBS.Services.TourOperatorList.createTravelAgentRequest request) {
            return base.Channel.createTravelAgentAsync(request);
        }
        
        public System.Threading.Tasks.Task<Ubimecs.IBS.Services.TourOperatorList.createTravelAgentResponse> createTravelAgentAsync(Ubimecs.IBS.Services.TourOperatorList.CreateTravelAgentRQ CreateTravelAgentRQ) {
            Ubimecs.IBS.Services.TourOperatorList.createTravelAgentRequest inValue = new Ubimecs.IBS.Services.TourOperatorList.createTravelAgentRequest();
            inValue.CreateTravelAgentRQ = CreateTravelAgentRQ;
            return ((Ubimecs.IBS.Services.TourOperatorList.AgencyPort)(this)).createTravelAgentAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Ubimecs.IBS.Services.TourOperatorList.deactivateTravelAgentResponse Ubimecs.IBS.Services.TourOperatorList.AgencyPort.deactivateTravelAgent(Ubimecs.IBS.Services.TourOperatorList.deactivateTravelAgentRequest request) {
            return base.Channel.deactivateTravelAgent(request);
        }
        
        public Ubimecs.IBS.Services.TourOperatorList.DeactivateTravelAgentRS deactivateTravelAgent(Ubimecs.IBS.Services.TourOperatorList.DeactivateTravelAgentRQ DeactivateTravelAgentRQ) {
            Ubimecs.IBS.Services.TourOperatorList.deactivateTravelAgentRequest inValue = new Ubimecs.IBS.Services.TourOperatorList.deactivateTravelAgentRequest();
            inValue.DeactivateTravelAgentRQ = DeactivateTravelAgentRQ;
            Ubimecs.IBS.Services.TourOperatorList.deactivateTravelAgentResponse retVal = ((Ubimecs.IBS.Services.TourOperatorList.AgencyPort)(this)).deactivateTravelAgent(inValue);
            return retVal.DeactivateTravelAgentRS;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.TourOperatorList.deactivateTravelAgentResponse> Ubimecs.IBS.Services.TourOperatorList.AgencyPort.deactivateTravelAgentAsync(Ubimecs.IBS.Services.TourOperatorList.deactivateTravelAgentRequest request) {
            return base.Channel.deactivateTravelAgentAsync(request);
        }
        
        public System.Threading.Tasks.Task<Ubimecs.IBS.Services.TourOperatorList.deactivateTravelAgentResponse> deactivateTravelAgentAsync(Ubimecs.IBS.Services.TourOperatorList.DeactivateTravelAgentRQ DeactivateTravelAgentRQ) {
            Ubimecs.IBS.Services.TourOperatorList.deactivateTravelAgentRequest inValue = new Ubimecs.IBS.Services.TourOperatorList.deactivateTravelAgentRequest();
            inValue.DeactivateTravelAgentRQ = DeactivateTravelAgentRQ;
            return ((Ubimecs.IBS.Services.TourOperatorList.AgencyPort)(this)).deactivateTravelAgentAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Ubimecs.IBS.Services.TourOperatorList.listTravelAgentsResponse Ubimecs.IBS.Services.TourOperatorList.AgencyPort.listTravelAgents(Ubimecs.IBS.Services.TourOperatorList.listTravelAgentsRequest request) {
            return base.Channel.listTravelAgents(request);
        }
        
        public Ubimecs.IBS.Services.TourOperatorList.ListTravelAgentsRS listTravelAgents(Ubimecs.IBS.Services.TourOperatorList.ListTravelAgentsRQ ListTravelAgentsRQ) {
            Ubimecs.IBS.Services.TourOperatorList.listTravelAgentsRequest inValue = new Ubimecs.IBS.Services.TourOperatorList.listTravelAgentsRequest();
            inValue.ListTravelAgentsRQ = ListTravelAgentsRQ;
            Ubimecs.IBS.Services.TourOperatorList.listTravelAgentsResponse retVal = ((Ubimecs.IBS.Services.TourOperatorList.AgencyPort)(this)).listTravelAgents(inValue);
            return retVal.ListTravelAgentsRS;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.TourOperatorList.listTravelAgentsResponse> Ubimecs.IBS.Services.TourOperatorList.AgencyPort.listTravelAgentsAsync(Ubimecs.IBS.Services.TourOperatorList.listTravelAgentsRequest request) {
            return base.Channel.listTravelAgentsAsync(request);
        }
        
        public System.Threading.Tasks.Task<Ubimecs.IBS.Services.TourOperatorList.listTravelAgentsResponse> listTravelAgentsAsync(Ubimecs.IBS.Services.TourOperatorList.ListTravelAgentsRQ ListTravelAgentsRQ) {
            Ubimecs.IBS.Services.TourOperatorList.listTravelAgentsRequest inValue = new Ubimecs.IBS.Services.TourOperatorList.listTravelAgentsRequest();
            inValue.ListTravelAgentsRQ = ListTravelAgentsRQ;
            return ((Ubimecs.IBS.Services.TourOperatorList.AgencyPort)(this)).listTravelAgentsAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Ubimecs.IBS.Services.TourOperatorList.modifyAgencyProfileResponse Ubimecs.IBS.Services.TourOperatorList.AgencyPort.modifyAgencyProfile(Ubimecs.IBS.Services.TourOperatorList.modifyAgencyProfileRequest request) {
            return base.Channel.modifyAgencyProfile(request);
        }
        
        public Ubimecs.IBS.Services.TourOperatorList.ModifyAgencyProfileRS modifyAgencyProfile(Ubimecs.IBS.Services.TourOperatorList.ModifyAgencyProfileRQ ModifyAgencyProfileRQ) {
            Ubimecs.IBS.Services.TourOperatorList.modifyAgencyProfileRequest inValue = new Ubimecs.IBS.Services.TourOperatorList.modifyAgencyProfileRequest();
            inValue.ModifyAgencyProfileRQ = ModifyAgencyProfileRQ;
            Ubimecs.IBS.Services.TourOperatorList.modifyAgencyProfileResponse retVal = ((Ubimecs.IBS.Services.TourOperatorList.AgencyPort)(this)).modifyAgencyProfile(inValue);
            return retVal.ModifyAgencyProfileRS;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.TourOperatorList.modifyAgencyProfileResponse> Ubimecs.IBS.Services.TourOperatorList.AgencyPort.modifyAgencyProfileAsync(Ubimecs.IBS.Services.TourOperatorList.modifyAgencyProfileRequest request) {
            return base.Channel.modifyAgencyProfileAsync(request);
        }
        
        public System.Threading.Tasks.Task<Ubimecs.IBS.Services.TourOperatorList.modifyAgencyProfileResponse> modifyAgencyProfileAsync(Ubimecs.IBS.Services.TourOperatorList.ModifyAgencyProfileRQ ModifyAgencyProfileRQ) {
            Ubimecs.IBS.Services.TourOperatorList.modifyAgencyProfileRequest inValue = new Ubimecs.IBS.Services.TourOperatorList.modifyAgencyProfileRequest();
            inValue.ModifyAgencyProfileRQ = ModifyAgencyProfileRQ;
            return ((Ubimecs.IBS.Services.TourOperatorList.AgencyPort)(this)).modifyAgencyProfileAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Ubimecs.IBS.Services.TourOperatorList.modifyTravelAgentResponse Ubimecs.IBS.Services.TourOperatorList.AgencyPort.modifyTravelAgent(Ubimecs.IBS.Services.TourOperatorList.modifyTravelAgentRequest request) {
            return base.Channel.modifyTravelAgent(request);
        }
        
        public Ubimecs.IBS.Services.TourOperatorList.ModifyTravelAgentRS modifyTravelAgent(Ubimecs.IBS.Services.TourOperatorList.ModifyTravelAgentRQ ModifyTravelAgentRQ) {
            Ubimecs.IBS.Services.TourOperatorList.modifyTravelAgentRequest inValue = new Ubimecs.IBS.Services.TourOperatorList.modifyTravelAgentRequest();
            inValue.ModifyTravelAgentRQ = ModifyTravelAgentRQ;
            Ubimecs.IBS.Services.TourOperatorList.modifyTravelAgentResponse retVal = ((Ubimecs.IBS.Services.TourOperatorList.AgencyPort)(this)).modifyTravelAgent(inValue);
            return retVal.ModifyTravelAgentRS;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.TourOperatorList.modifyTravelAgentResponse> Ubimecs.IBS.Services.TourOperatorList.AgencyPort.modifyTravelAgentAsync(Ubimecs.IBS.Services.TourOperatorList.modifyTravelAgentRequest request) {
            return base.Channel.modifyTravelAgentAsync(request);
        }
        
        public System.Threading.Tasks.Task<Ubimecs.IBS.Services.TourOperatorList.modifyTravelAgentResponse> modifyTravelAgentAsync(Ubimecs.IBS.Services.TourOperatorList.ModifyTravelAgentRQ ModifyTravelAgentRQ) {
            Ubimecs.IBS.Services.TourOperatorList.modifyTravelAgentRequest inValue = new Ubimecs.IBS.Services.TourOperatorList.modifyTravelAgentRequest();
            inValue.ModifyTravelAgentRQ = ModifyTravelAgentRQ;
            return ((Ubimecs.IBS.Services.TourOperatorList.AgencyPort)(this)).modifyTravelAgentAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Ubimecs.IBS.Services.TourOperatorList.retrieveAgencyCreditResponse Ubimecs.IBS.Services.TourOperatorList.AgencyPort.retrieveAgencyCredit(Ubimecs.IBS.Services.TourOperatorList.retrieveAgencyCreditRequest request) {
            return base.Channel.retrieveAgencyCredit(request);
        }
        
        public Ubimecs.IBS.Services.TourOperatorList.RetrieveAgencyCreditRS retrieveAgencyCredit(Ubimecs.IBS.Services.TourOperatorList.RetrieveAgencyCreditRQ RetrieveAgencyCreditRQ) {
            Ubimecs.IBS.Services.TourOperatorList.retrieveAgencyCreditRequest inValue = new Ubimecs.IBS.Services.TourOperatorList.retrieveAgencyCreditRequest();
            inValue.RetrieveAgencyCreditRQ = RetrieveAgencyCreditRQ;
            Ubimecs.IBS.Services.TourOperatorList.retrieveAgencyCreditResponse retVal = ((Ubimecs.IBS.Services.TourOperatorList.AgencyPort)(this)).retrieveAgencyCredit(inValue);
            return retVal.RetrieveAgencyCreditRS;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.TourOperatorList.retrieveAgencyCreditResponse> Ubimecs.IBS.Services.TourOperatorList.AgencyPort.retrieveAgencyCreditAsync(Ubimecs.IBS.Services.TourOperatorList.retrieveAgencyCreditRequest request) {
            return base.Channel.retrieveAgencyCreditAsync(request);
        }
        
        public System.Threading.Tasks.Task<Ubimecs.IBS.Services.TourOperatorList.retrieveAgencyCreditResponse> retrieveAgencyCreditAsync(Ubimecs.IBS.Services.TourOperatorList.RetrieveAgencyCreditRQ RetrieveAgencyCreditRQ) {
            Ubimecs.IBS.Services.TourOperatorList.retrieveAgencyCreditRequest inValue = new Ubimecs.IBS.Services.TourOperatorList.retrieveAgencyCreditRequest();
            inValue.RetrieveAgencyCreditRQ = RetrieveAgencyCreditRQ;
            return ((Ubimecs.IBS.Services.TourOperatorList.AgencyPort)(this)).retrieveAgencyCreditAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Ubimecs.IBS.Services.TourOperatorList.retrieveAgentInvoiceDetailsResponse Ubimecs.IBS.Services.TourOperatorList.AgencyPort.retrieveAgentInvoiceDetails(Ubimecs.IBS.Services.TourOperatorList.retrieveAgentInvoiceDetailsRequest request) {
            return base.Channel.retrieveAgentInvoiceDetails(request);
        }
        
        public Ubimecs.IBS.Services.TourOperatorList.RetrieveAgentInvoiceDetailsRS retrieveAgentInvoiceDetails(Ubimecs.IBS.Services.TourOperatorList.RetrieveAgentInvoiceDetailsRQ RetrieveAgentInvoiceDetailsRQ) {
            Ubimecs.IBS.Services.TourOperatorList.retrieveAgentInvoiceDetailsRequest inValue = new Ubimecs.IBS.Services.TourOperatorList.retrieveAgentInvoiceDetailsRequest();
            inValue.RetrieveAgentInvoiceDetailsRQ = RetrieveAgentInvoiceDetailsRQ;
            Ubimecs.IBS.Services.TourOperatorList.retrieveAgentInvoiceDetailsResponse retVal = ((Ubimecs.IBS.Services.TourOperatorList.AgencyPort)(this)).retrieveAgentInvoiceDetails(inValue);
            return retVal.RetrieveAgentInvoiceDetailsRS;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.TourOperatorList.retrieveAgentInvoiceDetailsResponse> Ubimecs.IBS.Services.TourOperatorList.AgencyPort.retrieveAgentInvoiceDetailsAsync(Ubimecs.IBS.Services.TourOperatorList.retrieveAgentInvoiceDetailsRequest request) {
            return base.Channel.retrieveAgentInvoiceDetailsAsync(request);
        }
        
        public System.Threading.Tasks.Task<Ubimecs.IBS.Services.TourOperatorList.retrieveAgentInvoiceDetailsResponse> retrieveAgentInvoiceDetailsAsync(Ubimecs.IBS.Services.TourOperatorList.RetrieveAgentInvoiceDetailsRQ RetrieveAgentInvoiceDetailsRQ) {
            Ubimecs.IBS.Services.TourOperatorList.retrieveAgentInvoiceDetailsRequest inValue = new Ubimecs.IBS.Services.TourOperatorList.retrieveAgentInvoiceDetailsRequest();
            inValue.RetrieveAgentInvoiceDetailsRQ = RetrieveAgentInvoiceDetailsRQ;
            return ((Ubimecs.IBS.Services.TourOperatorList.AgencyPort)(this)).retrieveAgentInvoiceDetailsAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Ubimecs.IBS.Services.TourOperatorList.retrieveDutyCodesForAgencyResponse Ubimecs.IBS.Services.TourOperatorList.AgencyPort.retrieveDutyCodesForAgency(Ubimecs.IBS.Services.TourOperatorList.retrieveDutyCodesForAgencyRequest request) {
            return base.Channel.retrieveDutyCodesForAgency(request);
        }
        
        public Ubimecs.IBS.Services.TourOperatorList.RetrieveDutyCodesForAgencyRS retrieveDutyCodesForAgency(Ubimecs.IBS.Services.TourOperatorList.RetrieveDutyCodesForAgencyRQ RetrieveDutyCodesForAgencyRQ) {
            Ubimecs.IBS.Services.TourOperatorList.retrieveDutyCodesForAgencyRequest inValue = new Ubimecs.IBS.Services.TourOperatorList.retrieveDutyCodesForAgencyRequest();
            inValue.RetrieveDutyCodesForAgencyRQ = RetrieveDutyCodesForAgencyRQ;
            Ubimecs.IBS.Services.TourOperatorList.retrieveDutyCodesForAgencyResponse retVal = ((Ubimecs.IBS.Services.TourOperatorList.AgencyPort)(this)).retrieveDutyCodesForAgency(inValue);
            return retVal.RetrieveDutyCodesForAgencyRS;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.TourOperatorList.retrieveDutyCodesForAgencyResponse> Ubimecs.IBS.Services.TourOperatorList.AgencyPort.retrieveDutyCodesForAgencyAsync(Ubimecs.IBS.Services.TourOperatorList.retrieveDutyCodesForAgencyRequest request) {
            return base.Channel.retrieveDutyCodesForAgencyAsync(request);
        }
        
        public System.Threading.Tasks.Task<Ubimecs.IBS.Services.TourOperatorList.retrieveDutyCodesForAgencyResponse> retrieveDutyCodesForAgencyAsync(Ubimecs.IBS.Services.TourOperatorList.RetrieveDutyCodesForAgencyRQ RetrieveDutyCodesForAgencyRQ) {
            Ubimecs.IBS.Services.TourOperatorList.retrieveDutyCodesForAgencyRequest inValue = new Ubimecs.IBS.Services.TourOperatorList.retrieveDutyCodesForAgencyRequest();
            inValue.RetrieveDutyCodesForAgencyRQ = RetrieveDutyCodesForAgencyRQ;
            return ((Ubimecs.IBS.Services.TourOperatorList.AgencyPort)(this)).retrieveDutyCodesForAgencyAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Ubimecs.IBS.Services.TourOperatorList.retrieveFOPDetailsForAgencyResponse Ubimecs.IBS.Services.TourOperatorList.AgencyPort.retrieveFOPDetailsForAgency(Ubimecs.IBS.Services.TourOperatorList.retrieveFOPDetailsForAgencyRequest request) {
            return base.Channel.retrieveFOPDetailsForAgency(request);
        }
        
        public Ubimecs.IBS.Services.TourOperatorList.RetrieveFOPDetailsForAgencyRS retrieveFOPDetailsForAgency(Ubimecs.IBS.Services.TourOperatorList.RetrieveFOPDetailsForAgencyRQ RetrieveFOPDetailsForAgencyRQ) {
            Ubimecs.IBS.Services.TourOperatorList.retrieveFOPDetailsForAgencyRequest inValue = new Ubimecs.IBS.Services.TourOperatorList.retrieveFOPDetailsForAgencyRequest();
            inValue.RetrieveFOPDetailsForAgencyRQ = RetrieveFOPDetailsForAgencyRQ;
            Ubimecs.IBS.Services.TourOperatorList.retrieveFOPDetailsForAgencyResponse retVal = ((Ubimecs.IBS.Services.TourOperatorList.AgencyPort)(this)).retrieveFOPDetailsForAgency(inValue);
            return retVal.RetrieveFOPDetailsForAgencyRS;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.TourOperatorList.retrieveFOPDetailsForAgencyResponse> Ubimecs.IBS.Services.TourOperatorList.AgencyPort.retrieveFOPDetailsForAgencyAsync(Ubimecs.IBS.Services.TourOperatorList.retrieveFOPDetailsForAgencyRequest request) {
            return base.Channel.retrieveFOPDetailsForAgencyAsync(request);
        }
        
        public System.Threading.Tasks.Task<Ubimecs.IBS.Services.TourOperatorList.retrieveFOPDetailsForAgencyResponse> retrieveFOPDetailsForAgencyAsync(Ubimecs.IBS.Services.TourOperatorList.RetrieveFOPDetailsForAgencyRQ RetrieveFOPDetailsForAgencyRQ) {
            Ubimecs.IBS.Services.TourOperatorList.retrieveFOPDetailsForAgencyRequest inValue = new Ubimecs.IBS.Services.TourOperatorList.retrieveFOPDetailsForAgencyRequest();
            inValue.RetrieveFOPDetailsForAgencyRQ = RetrieveFOPDetailsForAgencyRQ;
            return ((Ubimecs.IBS.Services.TourOperatorList.AgencyPort)(this)).retrieveFOPDetailsForAgencyAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Ubimecs.IBS.Services.TourOperatorList.retrieveTravelAgentDetailsResponse Ubimecs.IBS.Services.TourOperatorList.AgencyPort.retrieveTravelAgentDetails(Ubimecs.IBS.Services.TourOperatorList.retrieveTravelAgentDetailsRequest request) {
            return base.Channel.retrieveTravelAgentDetails(request);
        }
        
        public Ubimecs.IBS.Services.TourOperatorList.RetrieveTravelAgentDetailsRS retrieveTravelAgentDetails(Ubimecs.IBS.Services.TourOperatorList.RetrieveTravelAgentDetailsRQ RetrieveTravelAgentDetailsRQ) {
            Ubimecs.IBS.Services.TourOperatorList.retrieveTravelAgentDetailsRequest inValue = new Ubimecs.IBS.Services.TourOperatorList.retrieveTravelAgentDetailsRequest();
            inValue.RetrieveTravelAgentDetailsRQ = RetrieveTravelAgentDetailsRQ;
            Ubimecs.IBS.Services.TourOperatorList.retrieveTravelAgentDetailsResponse retVal = ((Ubimecs.IBS.Services.TourOperatorList.AgencyPort)(this)).retrieveTravelAgentDetails(inValue);
            return retVal.RetrieveTravelAgentDetailsRS;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.TourOperatorList.retrieveTravelAgentDetailsResponse> Ubimecs.IBS.Services.TourOperatorList.AgencyPort.retrieveTravelAgentDetailsAsync(Ubimecs.IBS.Services.TourOperatorList.retrieveTravelAgentDetailsRequest request) {
            return base.Channel.retrieveTravelAgentDetailsAsync(request);
        }
        
        public System.Threading.Tasks.Task<Ubimecs.IBS.Services.TourOperatorList.retrieveTravelAgentDetailsResponse> retrieveTravelAgentDetailsAsync(Ubimecs.IBS.Services.TourOperatorList.RetrieveTravelAgentDetailsRQ RetrieveTravelAgentDetailsRQ) {
            Ubimecs.IBS.Services.TourOperatorList.retrieveTravelAgentDetailsRequest inValue = new Ubimecs.IBS.Services.TourOperatorList.retrieveTravelAgentDetailsRequest();
            inValue.RetrieveTravelAgentDetailsRQ = RetrieveTravelAgentDetailsRQ;
            return ((Ubimecs.IBS.Services.TourOperatorList.AgencyPort)(this)).retrieveTravelAgentDetailsAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Ubimecs.IBS.Services.TourOperatorList.viewAgencyProfileResponse Ubimecs.IBS.Services.TourOperatorList.AgencyPort.viewAgencyProfile(Ubimecs.IBS.Services.TourOperatorList.viewAgencyProfileRequest request) {
            return base.Channel.viewAgencyProfile(request);
        }
        
        public Ubimecs.IBS.Services.TourOperatorList.ViewAgencyProfileRS viewAgencyProfile(Ubimecs.IBS.Services.TourOperatorList.ViewAgencyProfileRQ ViewAgencyProfileRQ) {
            Ubimecs.IBS.Services.TourOperatorList.viewAgencyProfileRequest inValue = new Ubimecs.IBS.Services.TourOperatorList.viewAgencyProfileRequest();
            inValue.ViewAgencyProfileRQ = ViewAgencyProfileRQ;
            Ubimecs.IBS.Services.TourOperatorList.viewAgencyProfileResponse retVal = ((Ubimecs.IBS.Services.TourOperatorList.AgencyPort)(this)).viewAgencyProfile(inValue);
            return retVal.ViewAgencyProfileRS;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.TourOperatorList.viewAgencyProfileResponse> Ubimecs.IBS.Services.TourOperatorList.AgencyPort.viewAgencyProfileAsync(Ubimecs.IBS.Services.TourOperatorList.viewAgencyProfileRequest request) {
            return base.Channel.viewAgencyProfileAsync(request);
        }
        
        public System.Threading.Tasks.Task<Ubimecs.IBS.Services.TourOperatorList.viewAgencyProfileResponse> viewAgencyProfileAsync(Ubimecs.IBS.Services.TourOperatorList.ViewAgencyProfileRQ ViewAgencyProfileRQ) {
            Ubimecs.IBS.Services.TourOperatorList.viewAgencyProfileRequest inValue = new Ubimecs.IBS.Services.TourOperatorList.viewAgencyProfileRequest();
            inValue.ViewAgencyProfileRQ = ViewAgencyProfileRQ;
            return ((Ubimecs.IBS.Services.TourOperatorList.AgencyPort)(this)).viewAgencyProfileAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Ubimecs.IBS.Services.TourOperatorList.activateTravelAgentResponse Ubimecs.IBS.Services.TourOperatorList.AgencyPort.activateTravelAgent(Ubimecs.IBS.Services.TourOperatorList.activateTravelAgentRequest request) {
            return base.Channel.activateTravelAgent(request);
        }
        
        public Ubimecs.IBS.Services.TourOperatorList.ActivateTravelAgentRS activateTravelAgent(Ubimecs.IBS.Services.TourOperatorList.ActivateTravelAgentRQ ActivateTravelAgentRQ) {
            Ubimecs.IBS.Services.TourOperatorList.activateTravelAgentRequest inValue = new Ubimecs.IBS.Services.TourOperatorList.activateTravelAgentRequest();
            inValue.ActivateTravelAgentRQ = ActivateTravelAgentRQ;
            Ubimecs.IBS.Services.TourOperatorList.activateTravelAgentResponse retVal = ((Ubimecs.IBS.Services.TourOperatorList.AgencyPort)(this)).activateTravelAgent(inValue);
            return retVal.ActivateTravelAgentRS;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.TourOperatorList.activateTravelAgentResponse> Ubimecs.IBS.Services.TourOperatorList.AgencyPort.activateTravelAgentAsync(Ubimecs.IBS.Services.TourOperatorList.activateTravelAgentRequest request) {
            return base.Channel.activateTravelAgentAsync(request);
        }
        
        public System.Threading.Tasks.Task<Ubimecs.IBS.Services.TourOperatorList.activateTravelAgentResponse> activateTravelAgentAsync(Ubimecs.IBS.Services.TourOperatorList.ActivateTravelAgentRQ ActivateTravelAgentRQ) {
            Ubimecs.IBS.Services.TourOperatorList.activateTravelAgentRequest inValue = new Ubimecs.IBS.Services.TourOperatorList.activateTravelAgentRequest();
            inValue.ActivateTravelAgentRQ = ActivateTravelAgentRQ;
            return ((Ubimecs.IBS.Services.TourOperatorList.AgencyPort)(this)).activateTravelAgentAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Ubimecs.IBS.Services.TourOperatorList.retrieveAgenciesResponse Ubimecs.IBS.Services.TourOperatorList.AgencyPort.retrieveAgencies(Ubimecs.IBS.Services.TourOperatorList.retrieveAgenciesRequest request) {
            return base.Channel.retrieveAgencies(request);
        }
        
        public Ubimecs.IBS.Services.TourOperatorList.RetrieveAgenciesRS retrieveAgencies(Ubimecs.IBS.Services.TourOperatorList.RetrieveAgenciesRQ RetrieveAgenciesRQ) {
            Ubimecs.IBS.Services.TourOperatorList.retrieveAgenciesRequest inValue = new Ubimecs.IBS.Services.TourOperatorList.retrieveAgenciesRequest();
            inValue.RetrieveAgenciesRQ = RetrieveAgenciesRQ;
            Ubimecs.IBS.Services.TourOperatorList.retrieveAgenciesResponse retVal = ((Ubimecs.IBS.Services.TourOperatorList.AgencyPort)(this)).retrieveAgencies(inValue);
            return retVal.RetrieveAgenciesRS;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.TourOperatorList.retrieveAgenciesResponse> Ubimecs.IBS.Services.TourOperatorList.AgencyPort.retrieveAgenciesAsync(Ubimecs.IBS.Services.TourOperatorList.retrieveAgenciesRequest request) {
            return base.Channel.retrieveAgenciesAsync(request);
        }
        
        public System.Threading.Tasks.Task<Ubimecs.IBS.Services.TourOperatorList.retrieveAgenciesResponse> retrieveAgenciesAsync(Ubimecs.IBS.Services.TourOperatorList.RetrieveAgenciesRQ RetrieveAgenciesRQ) {
            Ubimecs.IBS.Services.TourOperatorList.retrieveAgenciesRequest inValue = new Ubimecs.IBS.Services.TourOperatorList.retrieveAgenciesRequest();
            inValue.RetrieveAgenciesRQ = RetrieveAgenciesRQ;
            return ((Ubimecs.IBS.Services.TourOperatorList.AgencyPort)(this)).retrieveAgenciesAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Ubimecs.IBS.Services.TourOperatorList.agencyPaymentResponse Ubimecs.IBS.Services.TourOperatorList.AgencyPort.agencyPayment(Ubimecs.IBS.Services.TourOperatorList.agencyPaymentRequest request) {
            return base.Channel.agencyPayment(request);
        }
        
        public Ubimecs.IBS.Services.TourOperatorList.AgencyPaymentRS agencyPayment(Ubimecs.IBS.Services.TourOperatorList.AgencyPaymentRQ AgencyPaymentRQ) {
            Ubimecs.IBS.Services.TourOperatorList.agencyPaymentRequest inValue = new Ubimecs.IBS.Services.TourOperatorList.agencyPaymentRequest();
            inValue.AgencyPaymentRQ = AgencyPaymentRQ;
            Ubimecs.IBS.Services.TourOperatorList.agencyPaymentResponse retVal = ((Ubimecs.IBS.Services.TourOperatorList.AgencyPort)(this)).agencyPayment(inValue);
            return retVal.AgencyPaymentRS;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.TourOperatorList.agencyPaymentResponse> Ubimecs.IBS.Services.TourOperatorList.AgencyPort.agencyPaymentAsync(Ubimecs.IBS.Services.TourOperatorList.agencyPaymentRequest request) {
            return base.Channel.agencyPaymentAsync(request);
        }
        
        public System.Threading.Tasks.Task<Ubimecs.IBS.Services.TourOperatorList.agencyPaymentResponse> agencyPaymentAsync(Ubimecs.IBS.Services.TourOperatorList.AgencyPaymentRQ AgencyPaymentRQ) {
            Ubimecs.IBS.Services.TourOperatorList.agencyPaymentRequest inValue = new Ubimecs.IBS.Services.TourOperatorList.agencyPaymentRequest();
            inValue.AgencyPaymentRQ = AgencyPaymentRQ;
            return ((Ubimecs.IBS.Services.TourOperatorList.AgencyPort)(this)).agencyPaymentAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Ubimecs.IBS.Services.TourOperatorList.retrieveAgencyCreditHistoryResponse Ubimecs.IBS.Services.TourOperatorList.AgencyPort.retrieveAgencyCreditHistory(Ubimecs.IBS.Services.TourOperatorList.retrieveAgencyCreditHistoryRequest request) {
            return base.Channel.retrieveAgencyCreditHistory(request);
        }
        
        public Ubimecs.IBS.Services.TourOperatorList.RetrieveAgencyCreditHistoryRS retrieveAgencyCreditHistory(Ubimecs.IBS.Services.TourOperatorList.RetrieveAgencyCreditHistoryRQ RetrieveAgencyCreditHistoryRQ) {
            Ubimecs.IBS.Services.TourOperatorList.retrieveAgencyCreditHistoryRequest inValue = new Ubimecs.IBS.Services.TourOperatorList.retrieveAgencyCreditHistoryRequest();
            inValue.RetrieveAgencyCreditHistoryRQ = RetrieveAgencyCreditHistoryRQ;
            Ubimecs.IBS.Services.TourOperatorList.retrieveAgencyCreditHistoryResponse retVal = ((Ubimecs.IBS.Services.TourOperatorList.AgencyPort)(this)).retrieveAgencyCreditHistory(inValue);
            return retVal.RetrieveAgencyCreditHistoryRS;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Ubimecs.IBS.Services.TourOperatorList.retrieveAgencyCreditHistoryResponse> Ubimecs.IBS.Services.TourOperatorList.AgencyPort.retrieveAgencyCreditHistoryAsync(Ubimecs.IBS.Services.TourOperatorList.retrieveAgencyCreditHistoryRequest request) {
            return base.Channel.retrieveAgencyCreditHistoryAsync(request);
        }
        
        public System.Threading.Tasks.Task<Ubimecs.IBS.Services.TourOperatorList.retrieveAgencyCreditHistoryResponse> retrieveAgencyCreditHistoryAsync(Ubimecs.IBS.Services.TourOperatorList.RetrieveAgencyCreditHistoryRQ RetrieveAgencyCreditHistoryRQ) {
            Ubimecs.IBS.Services.TourOperatorList.retrieveAgencyCreditHistoryRequest inValue = new Ubimecs.IBS.Services.TourOperatorList.retrieveAgencyCreditHistoryRequest();
            inValue.RetrieveAgencyCreditHistoryRQ = RetrieveAgencyCreditHistoryRQ;
            return ((Ubimecs.IBS.Services.TourOperatorList.AgencyPort)(this)).retrieveAgencyCreditHistoryAsync(inValue);
        }
    }
}
