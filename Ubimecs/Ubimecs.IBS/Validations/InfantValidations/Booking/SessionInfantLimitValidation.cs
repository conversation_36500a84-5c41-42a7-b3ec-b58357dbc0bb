using Ubimecs.IBS.Utility;
using Ubimecs.IBS.Validations.Contracts;
using Ubimecs.Infrastructure.Caching;
using Ubimecs.Infrastructure.Models.Common;
using Ubimecs.Infrastructure.Models.Request;

namespace Ubimecs.IBS.Validations.InfantValidations.Booking;

/// <summary>
/// Session içerisinde yer al<PERSON> kolt<PERSON>n Infant Limitini kontrol eder
/// </summary>
public class SessionInfantLimitValidation: IValidationRule<BookingSelectSeatRequest>
{
    public void Validate(BookingSelectSeatRequest requestData, SessionCache session)
    {
        var sessionSeats = session.CurrentFlow.PNR.Seats;
        var requestPassengerIds = requestData.Seats.Select(s => s.PassengerId).ToHashSet();
        var filteredSessionSeats = sessionSeats.Where(s => !requestPassengerIds.Contains(s.PassengerId)).ToList();

        var (leftSide, rightSide) = IbsUtility.SplitSeats(session);
        
        List<BookingSelectSeatRequest.BookingSelectSeat>
            leftSeats = IbsUtility.GetSeatsPosition(leftSide, filteredSessionSeats.Select(x=> new BookingSelectSeatRequest.BookingSelectSeat
            {
                Column = x.Column,
                Number = x.Number,
                PassengerId = x.PassengerId
            }).ToList());
        List<BookingSelectSeatRequest.BookingSelectSeat> rightSeats = IbsUtility.GetSeatsPosition(rightSide, filteredSessionSeats.Select(x=> new BookingSelectSeatRequest.BookingSelectSeat
        {
            Column = x.Column,
            Number = x.Number,
            PassengerId = x.PassengerId
        }).ToList());
        
        //foreach (var sessionSeat in sessionSeats)
        //{
        //    if (leftSide.Contains(sessionSeat.Column))
        //    {
        //        leftSeats.Add(new BookingSelectSeatRequest.BookingSelectSeat
        //        {
        //            Column = sessionSeat.Column,
        //            Number =   sessionSeat.Number,
        //            PassengerId = sessionSeat.PassengerId
        //        });
        //    }
        //    else if (rightSide.Contains(sessionSeat.Column))
        //    {
        //        rightSeats.Add(new BookingSelectSeatRequest.BookingSelectSeat
        //        {
        //            Column = sessionSeat.Column,
        //            Number =   sessionSeat.Number,
        //            PassengerId = sessionSeat.PassengerId
        //        });
        //    }
        //}

        foreach (var seat in requestData.Seats)
        {
            var isInfant = session.CurrentFlow.PNR.Passengers.FirstOrDefault(x=>x.ParentGuestID == seat.PassengerId) != null;
            if (IbsUtility.CheckInfantInGroup(leftSeats, session).Any() && leftSide.Contains(seat.Column) && isInfant)
            {
                throw new TmobException("Maximum infant(s) per unit is exceeded in seat map.");
            } 
            if (IbsUtility.CheckInfantInGroup(rightSeats, session).Any() && rightSide.Contains(seat.Column) && isInfant)
            {
                throw new TmobException("Maximum infant(s) per unit is exceeded in seat map.");
            } 
        }
   
    }
}