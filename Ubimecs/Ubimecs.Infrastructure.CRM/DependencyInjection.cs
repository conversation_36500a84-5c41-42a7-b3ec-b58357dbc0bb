using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Ubimecs.Infrastructure.CRM.Configuration;
using Ubimecs.Infrastructure.CRM.Constants;
using Ubimecs.Infrastructure.CRM.Contracts;
using Ubimecs.Infrastructure.CRM.Services;

namespace Ubimecs.Infrastructure.CRM;

public static class DependencyInjection
{
    public static IServiceCollection AddCrmServices(this IServiceCollection services, IConfiguration configuration)
    {
        //INFO: Add dependencies to here;
        services.Configure<Next4BizCrmSettings>(options => configuration.GetSection("Next4BizCrmSettings").Bind(options));
        services.AddHttpClient<INext4BizCrmRequestService,Next4BizCrmRequestService>();
        services.AddScoped<ICrmService, Next4BizCrmService>();
        
        var config = new Next4BizCrmSettings();
        configuration.GetSection("Next4BizCrmSettings").Bind(config);
        
        Endpoints.Configure(config.BaseUrl);
        
        return services;
    }
}