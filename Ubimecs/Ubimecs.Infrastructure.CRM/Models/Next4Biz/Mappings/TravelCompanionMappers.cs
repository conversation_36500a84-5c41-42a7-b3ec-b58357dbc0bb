using Ubimecs.Infrastructure.CRM.Configuration;
using Ubimecs.Infrastructure.CRM.Constants.Next4Biz.TravelCompanion;
using Ubimecs.Infrastructure.CRM.Models.Next4Biz.Enums;
using Ubimecs.Infrastructure.CRM.Models.Next4Biz.Requests;
using Ubimecs.Infrastructure.CRM.Models.Next4Biz.Requests.Dto;

namespace Ubimecs.Infrastructure.CRM.Models.Next4Biz.Mappings;

public static class TravelCompanionMappers
{
    public static CreateTravelCompanionRequest Map(this CreateTravelCompanionRequestDto request,Next4BizCrmSettings settings,string accessToken)
    {
        var travelCompanionCustomFields = TravelCompanionCustomFieldService.GetCustomFieldDictionary(settings.Environment);
        var vTable = VTableService.GetVTableDictionary(settings.Environment);
        return new CreateTravelCompanionRequest
        {
            AccessToken = accessToken,
            TenantSlug = settings.TenantSlug,
            Channel = settings.ChannelId,
            VtableId = vTable[VTable.TravelCompanion],
            VtableRow = new List<VtableRow>()
            {
               new VtableRow
               {
                   RelatedId = request.PersonId,
                   RelationKindId = Convert.ToInt32(vTable[VTable.RelationKindId]),
                   CustomData = new List<VtableRowCustomData>
                   {
                       new VtableRowCustomData
                       {
                           Id = travelCompanionCustomFields[TravelCompanionCustomField.FirstName],
                           Value = request.FirstName
                       },
                       new VtableRowCustomData
                       {
                           Id = travelCompanionCustomFields[TravelCompanionCustomField.LastName],
                           Value = request.LastName
                       },
                       new VtableRowCustomData
                       {
                           Id = travelCompanionCustomFields[TravelCompanionCustomField.Gender],
                           Value = ((int)request.Gender).ToString() 
                       },
                       new VtableRowCustomData
                       {
                           Id = travelCompanionCustomFields[TravelCompanionCustomField.BirthDate],
                           Value = ((DateTimeOffset)request.BirthDate).ToUnixTimeMilliseconds().ToString() 
                       },
                       new VtableRowCustomData
                       {
                           Id = travelCompanionCustomFields[TravelCompanionCustomField.Nationality],
                           Value = request.Nationality
                       },
                       new VtableRowCustomData
                       {
                           Id = travelCompanionCustomFields[TravelCompanionCustomField.TravelCompanionId],
                           Value = request.CustomerId
                       }
                   }
               }
            }
        };
    }

    public static RemoveTravelCompanionRequest Map(this RemoveTravelCompanionRequestDto request,Next4BizCrmSettings settings,string accessToken)
    {
        var vTable = VTableService.GetVTableDictionary(settings.Environment);

        return new RemoveTravelCompanionRequest
        {
            AccessToken = accessToken,
            TenantSlug = settings.TenantSlug,
            Channel = settings.ChannelId,
            VtableId = vTable[VTable.TravelCompanion],
            VtableRow = new List<RemoveTravelCompanionVTableRow>
            {
                new RemoveTravelCompanionVTableRow()
                {
                    VTableRowId = request.Id
                }
            }
        };
    }
    
}