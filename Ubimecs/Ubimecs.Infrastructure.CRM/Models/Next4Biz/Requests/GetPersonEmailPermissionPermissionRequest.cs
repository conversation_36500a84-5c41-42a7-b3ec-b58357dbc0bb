using System.Text.Json.Serialization;
using Microsoft.Extensions.Primitives;

namespace Ubimecs.Infrastructure.CRM.Models.Next4Biz.Requests;

public class GetPersonEmailPermissionPermissionRequest
{
    [JsonPropertyName("tenant_slug")]
    public string TenantSlug { get; set; }
    [JsonPropertyName("channel")]
    public string Channel { get; set; }
    [Json<PERSON>ropertyName("access_token")]
    public string AccessToken { get; set; }
    [JsonPropertyName("person_email_permission")]
    public List<PersonEmailPermission> PersonEmailPermissions { get; set; }
}


public class PersonEmailPermission
{
    [JsonPropertyName("email_address")]
    public string Email { get; set; }
}