using System.Text.Json.Serialization;

namespace Ubimecs.Infrastructure.CRM.Models.Next4Biz.Requests;

    public class GetPersonRequest
    {
        [JsonPropertyName("tenant_slug")]
        public string TenantSlug { get; set; }
    
        [JsonPropertyName("channel")]
        public string Channel { get; set; }
    
        [Json<PERSON>ropertyName("access_token")]
        public string AccessToken { get; set; }
    
        [Json<PERSON>ropertyName("person")]
        public List<GetPerson> Persons { get; set; }
    }

    public class GetPerson
    {
        [JsonPropertyName("email")]
        public string Email { get; set; }
    }
