using System.Text.Json.Serialization;

namespace Ubimecs.Infrastructure.CRM.Models.Next4Biz.Requests;

public class UpdatePersonPhonePermissionRequest
{
    
    
    [JsonPropertyName("tenant_slug")]
    public string TenantSlug { get; set; }
    [JsonPropertyName("channel")]
    public string Channel { get; set; }
    [Json<PERSON>ropertyName("access_token")]
    public string AccessToken { get; set; }
    [JsonPropertyName("person_phone_permission")]
    public List<UpdatePersonPhonePermission> PersonPhonePermissions { get; set; }
}


public class UpdatePersonPhonePermission : PersonPhonePermission
{
    [JsonPropertyName("is_sms_authorized")]
    public bool IsSmsAuthorized { get; set; }
    [Json<PERSON>ropertyName("is_call_authorized")]
    public bool IsPhoneAuthorized { get; set; }
}