namespace Ubimecs.Infrastructure.CSM.Constants.Next4Biz.GroundOperation;

public class GroundOperationCustomFieldBuilder
{
    public static Dictionary<GroundOperationCustomField, string> GetGroundOperationDictionary(string environment)
    {
        return environment.ToLower() switch
        {
            "test" => GroundOperationCustomFieldEnvironments.Test,
            "prod" => GroundOperationCustomFieldEnvironments.Prod,
            _ => throw new ArgumentException("Invalid environment name") // Geçersiz ortam adı
        };
    }
}