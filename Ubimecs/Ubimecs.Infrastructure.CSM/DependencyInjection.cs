using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Ubimecs.Infrastructure.CSM.Configuration;
using Ubimecs.Infrastructure.CSM.Contracts;
using Ubimecs.Infrastructure.CSM.Services;

namespace Ubimecs.Infrastructure.CSM;

public static class DependencyInjection
{
    //FIXME: Change method name to AddCsmServices();
    public static IServiceCollection AddCsmInfrastructure(this IServiceCollection services, IConfiguration configuration)
    {
        services.Configure<Next4BizCsmSettings>(options => configuration.GetSection("Next4BizCsmSettings").Bind(options));
        services.Configure<FlightStatusSettings>(options => configuration.GetSection("FlightStatusSettings").Bind(options));
        services.AddHttpClient<INext4BizCsmRequestService, Next4BizCsmRequestService>();
        services.AddScoped<ICsmService,Next4BizCsmService>();
        services.AddScoped<INext4BizFileService, Next4BizFileService>();
        
        return services;
    }
}