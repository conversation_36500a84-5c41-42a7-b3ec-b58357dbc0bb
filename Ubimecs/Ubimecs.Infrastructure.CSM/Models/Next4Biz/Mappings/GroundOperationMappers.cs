using Ubimecs.Infrastructure.CSM.Constants.Next4Biz.ContactMethod;
using Ubimecs.Infrastructure.CSM.Constants.Next4Biz.GroundOperation;
using Ubimecs.Infrastructure.CSM.Constants.Next4Biz.Issue;
using Ubimecs.Infrastructure.CSM.Constants.Next4Biz.Security;
using Ubimecs.Infrastructure.CSM.Models.Next4Biz.Requests;

namespace Ubimecs.Infrastructure.CSM.Models.Next4Biz.Mappings;

public static class GroundOperationMappers
{
    
    public static CreateIssueDto WheelChairIssueMap(this WheelChairReportRequest request, string environment)
    {
        var customFields = GroundOperationCustomFieldBuilder.GetGroundOperationDictionary(environment);
        var issueTypeFields = IssueTypeService.GetIssueTypeDictionary(environment);
        var categoryFields = GroundOperationCategoryBuilder.GetGroundOperationDictionary(environment);
        var contactMethodFields = ContactMethodService.GetContactMethodDictionary(environment);
        
        return new CreateIssueDto()
        {
            Category = new CategoryDto
            {
                CategoryID = categoryFields[GroundOperationCategory.Wheelchair]
            },
            IssueType = new IssueTypeDto
            {
                IssueTypeID = issueTypeFields[IssueType.Request]
            },
            ContactMethod = new ContactMethodDto
            {
                ContactMethodID = contactMethodFields[ContactMethod.WebContactForm]
            },
            Customer = new CustomerDto
            {
                CustomerEmail = request.Email,
                CustomerName = request.PassengerName,
                CustomerSurname = request.PassengerSurname,
                CustomerMobilePhoneNumber = request.Phone
            },
            IssueDescription = "Description",
            CustomFields = new List<CustomFieldDto>
            {
                new CustomFieldDto
                {
                    CustomFieldID = customFields[GroundOperationCustomField.Carrier],
                    CustomFieldValue = request.Carrier
                },
                new CustomFieldDto
                {
                    CustomFieldID = customFields[GroundOperationCustomField.FlightNumber],
                    CustomFieldValue = request.FlightNumber
                },
                new CustomFieldDto
                {
                    CustomFieldID = customFields[GroundOperationCustomField.FlightDate],
                    CustomFieldValue = request.FlightDate.Value.Date.ToString("yyyy-MM-dd"),
                },
                new CustomFieldDto
                {
                    CustomFieldID = customFields[GroundOperationCustomField.OwnWheelchair],
                    CustomFieldValue = request.OwnWheelchair.ToString()
                },
                new CustomFieldDto
                {
                    CustomFieldID = customFields[GroundOperationCustomField.OwnElectricWheelchair],
                    CustomFieldValue = request.OwnElectricWheelchair.ToString()
                },
                new CustomFieldDto
                {
                    CustomFieldID = customFields[GroundOperationCustomField.BatteryType],
                    CustomFieldValue = request.BatteryType
                },
                new CustomFieldDto
                {
                    CustomFieldID = customFields[GroundOperationCustomField.WeightKg],
                    CustomFieldValue = request.WeightKg
                },
                new CustomFieldDto
                {
                    CustomFieldID = customFields[GroundOperationCustomField.LengthCm],
                    CustomFieldValue = request.LengthCm
                },
                new CustomFieldDto
                {
                    CustomFieldID = customFields[GroundOperationCustomField.HeightCm],
                    CustomFieldValue = request.HeightCm
                },
                new CustomFieldDto
                {
                    CustomFieldID = customFields[GroundOperationCustomField.WidthCm],
                    CustomFieldValue = request.WidthCm
                },
                new CustomFieldDto
                {
                    CustomFieldID = customFields[GroundOperationCustomField.BatteryCount],
                    CustomFieldValue = request.BatteryCount
                },
                new CustomFieldDto
                {
                    CustomFieldID = customFields[GroundOperationCustomField.WhValue],
                    CustomFieldValue = request.WhValue
                },
            }
        };
    }
}