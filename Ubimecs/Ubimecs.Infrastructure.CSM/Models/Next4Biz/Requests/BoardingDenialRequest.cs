using Ubimecs.Infrastructure.CSM.Models.Next4Biz.Enums;

namespace Ubimecs.Infrastructure.CSM.Models.Next4Biz.Requests;

public class BoardingDenialRequest
{
    public string Carrier { get; set; }
    public string? FlightNumber { get; set; }
    public DateTime? FlightDate { get; set; }
    public Sender Sender { get; set; }
    public string PassengerName { get; set; }
    public string PassengerSurname { get; set; }
    public string PnrOrTicketNumber { get; set; }
    public string Email { get; set; }
    public string Phone { get; set; }
    public string Description { get; set; }
    public PaymentType PaymentType { get; set; }
    public string? BankName { get; set; }
    public string? AccountHolderName { get; set; }
    public string? IBAN { get; set; }
    public string? SwiftBicCode { get; set; }
}