using Amazon.Runtime;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Amazon.S3;
using QuestPDF.Infrastructure;
using Ubimecs.Infrastructure.FileService.Configuration;
using Ubimecs.Infrastructure.FileService.Contracts;
using Ubimecs.Infrastructure.FileService.Services;
using Ubimecs.Infrastructure.FileService.Services.Exports;

namespace Ubimecs.Infrastructure.FileService;

public static class DependencyInjection
{
    public static IServiceCollection AddFileServices(this IServiceCollection services, IConfiguration configuration)
    {
        var awsConfig = new AwsSettings();
        
        configuration.GetSection("AwsSettings").Bind(awsConfig);

        var credentials = new BasicAWSCredentials(awsConfig.AccessKey, awsConfig.SecretKey);

        services.AddScoped<IAmazonS3>(sp => new AmazonS3Client(
            credentials,
            new AmazonS3Config
            {
                RegionEndpoint = Amazon.RegionEndpoint.GetBySystemName(awsConfig.Region)
            }));

        services.AddScoped<IFileStorageService>(sp =>
        {
            var s3Client = sp.GetRequiredService<IAmazonS3>();
            return new S3FileStorageService(s3Client, awsConfig);
        });
        
        QuestPDF.Settings.License = LicenseType.Community;
        services.AddScoped<IFileGenerator, PdfFileGenerator>();
        services.AddScoped<IFileGenerator, ExcelFileGenerator>();
        services.AddScoped<IFileGenerator, CsvFileGenerator>();
        
        services.AddScoped<FileGenerationService>();
        services.AddScoped<IFileGeneratorFactory, FileGeneratorFactory>();
        services.AddScoped<IExportHandler, SalesReportExportHandler>();
        services.AddScoped<IExportHandler, PnrListExportHandler>();
        
        return services;

    }

}