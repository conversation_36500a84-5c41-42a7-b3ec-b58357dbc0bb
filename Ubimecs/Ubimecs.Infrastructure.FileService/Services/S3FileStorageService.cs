using Amazon.S3;
using Amazon.S3.Model;
using Amazon.S3.Transfer;
using Ubimecs.Infrastructure.FileService.Configuration;
using Ubimecs.Infrastructure.FileService.Constants;
using Ubimecs.Infrastructure.FileService.Contracts;

namespace Ubimecs.Infrastructure.FileService.Services;

public class S3FileStorageService : IFileStorageService
{
    private readonly IAmazonS3 _s3Client;
    private readonly AwsSettings _awsSettings;
    public S3FileStorageService(IAmazonS3 s3Client,AwsSettings awsSettings)
    {
        _s3Client = s3Client;
        _awsSettings = awsSettings;
    }
    public async Task<string> UploadFileAsync(Stream fileStream,FolderType folderType, string fileName, string contentType)
    {
        var fileTransferUtility = new TransferUtility(_s3Client);

        var key = CombineUrl(folderType, fileName);
        
        string extention = contentType.Split('/')[1];

        var uploadRequest = new TransferUtilityUploadRequest
        {
            InputStream = fileStream,
            Key = key+"."+extention,
            BucketName = _awsSettings.BucketName,
            ContentType = contentType
        };
        
        await fileTransferUtility.UploadAsync(uploadRequest);
        return $"https://{_awsSettings.BucketName}.s3.{_awsSettings.Region}.amazonaws.com/{GetFolderPath(folderType)}/{fileName}.{extention}";
    }

    public async Task<DeleteObjectResponse> DeleteFileAsync(string key,FolderType folderType)
    {
        try
        {
            string filePath = GetFolderPath(folderType);
        
            var deleteObjectRequest = new DeleteObjectRequest
            {
                BucketName = _awsSettings.BucketName,
                Key =  filePath+"/"+key
            };
        
            return await _s3Client.DeleteObjectAsync(deleteObjectRequest);
        }
        catch (Exception e)
        {
            return null;
        }
       
    }

    public string CombineUrl(FolderType folderType, string fileName)
    {
        return folderType switch
        {
            FolderType.B2BAgencyProfile => string.IsNullOrEmpty(_awsSettings.AgencyImageFolderPath)
                ? fileName
                : Path.Combine(_awsSettings.AgencyImageFolderPath, fileName).Replace("\\", "/"),

            FolderType.B2BReport => string.IsNullOrEmpty(_awsSettings.FolderPath)
                ? fileName
                : Path.Combine(_awsSettings.FolderPath, fileName).Replace("\\", "/"),

            _ => throw new ArgumentOutOfRangeException(nameof(folderType), folderType, null)
        };
    }

    public string GetFolderPath(FolderType folderType)
    {
        return folderType switch
        {
            FolderType.B2BAgencyProfile => _awsSettings.AgencyImageFolderPath,
            FolderType.B2BReport => _awsSettings.FolderPath,
            _ => throw new ArgumentOutOfRangeException(nameof(folderType), folderType, null)
        };
    }
    public Stream GetFileStream(string base64Image)
    {
        var base64Data = base64Image.Contains(",") ? base64Image.Split(',')[1] : base64Image;
        byte[] imageBytes = Convert.FromBase64String(base64Data);
        return new MemoryStream(imageBytes);
    }
}