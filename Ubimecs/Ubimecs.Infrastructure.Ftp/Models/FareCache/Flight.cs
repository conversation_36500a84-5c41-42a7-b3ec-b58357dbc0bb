using System.Xml.Serialization;

namespace Ubimecs.Infrastructure.Ftp.Models.FareCache
{
    public class Flight
    {
        [XmlAttribute(AttributeName = "BookingClass")]
        public string BookingClass { get; set; }

        [XmlAttribute(AttributeName = "Class")]
        public string Class { get; set; }

        [XmlAttribute(AttributeName = "DepartureTime")]
        public DateTime DepartureTime { get; set; }

        [XmlAttribute(AttributeName = "ArrivalTime")]
        public DateTime ArrivalTime { get; set; }

        [XmlAttribute(AttributeName = "DepartureDate")]
        public DateTime DepartureDate { get; set; }

        [XmlAttribute(AttributeName = "ArrivalDate")]
        public DateTime ArrivalDate { get; set; }

        [XmlAttribute(AttributeName = "FlightKey")]
        public string FlightKey { get; set; }

        [XmlAttribute(AttributeName = "Code")]
        public string Code { get; set; }

        [XmlArray(ElementName = "Segments")]
        [XmlArrayItem(ElementName = "Segment")]
        public List<Segment> Segments { get; set; }

        [XmlArray(ElementName = "Extras")]
        [XmlArrayItem(ElementName = "Extra")]
        public List<Extra> Extras { get; set; }
        
        [XmlIgnore]
        public string Currency { get; set; }
    }
}
