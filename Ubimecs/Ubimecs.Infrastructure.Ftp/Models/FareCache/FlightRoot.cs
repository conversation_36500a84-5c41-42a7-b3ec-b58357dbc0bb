using System;
using System.Xml.Serialization;


namespace Ubimecs.Infrastructure.Ftp.Models.FareCache
{
    [XmlRoot(ElementName = "FlightRoot", Namespace = "http://www.vilauma.de/edf/Flight")]
    public class FlightRoot
    {
        [XmlAttribute(AttributeName = "SchemaVersion")]
        public string SchemaVersion { get; set; }

        [XmlAttribute(AttributeName = "CreationDate")]
        public DateTime CreationDate { get; set; }

        [XmlElement(ElementName = "BasicData")]
        public BasicData BasicData { get; set; }

        [XmlElement(ElementName = "SellingData")]
        public SellingData SellingData { get; set; }
    }
}
