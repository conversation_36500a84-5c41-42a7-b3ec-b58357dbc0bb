using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System.Linq;

namespace Ubimecs.Infrastructure.HolidayExtras.Models;

public class CarParkProductResponse: IHolidayBase
{
    [JsonProperty("Product")]
    public List<Product> Product { get; set; } = new();
}

public class Product
{
    [JsonProperty("name")]
    public string Name { get; set; }

    [JsonProperty("lead_time")]
    public int LeadTime { get; set; }

    [JsonProperty("mindestparkdauer")]
    public int MinStay { get; set; }

    [JsonProperty("maximum_stay")]
    public int MaxStay { get; set; }

    [JsonProperty("telefon")]
    public string Phone { get; set; }

    [JsonProperty("transfer")]
    public string Transfer { get; set; }

    [JsonProperty("tripappintroduction")]
    public string Description { get; set; }

    [JsonProperty("tripappimages")]
    public string Images { get; set; }

    public string MainImage => Images?.Split(';')
        .Select(x => x.Trim())
        .FirstOrDefault(x => !x.Contains("icon", StringComparison.OrdinalIgnoreCase)) ?? string.Empty;

    [JsonProperty("longitude")]
    public double Longitude { get; set; }

    [JsonProperty("latitude")]
    public double Latitude { get; set; }
}