using Newtonsoft.Json;

namespace Ubimecs.Infrastructure.HolidayExtras.Models;

public class CarParkResponse : IHolidayBase
{
    [JsonProperty("ATTRIBUTES")]
    public CarParkAttributes Attributes { get; set; } = new();

    [JsonProperty("CarPark")]
    public List<CarParkDetail> CarParks { get; set; } = new();

    [JsonProperty("Pricing")]
    public object Pricing { get; set; } = new();

    [JsonProperty("SepaID")]
    public string SepaID { get; set; } = string.Empty;
}

public class CarParkAttributes
{
    [JsonProperty("Product")]
    public string Product { get; set; } = string.Empty;

    [JsonProperty("RequestCode")]
    public int RequestCode { get; set; }

    [JsonProperty("Result")]
    public string Result { get; set; } = string.Empty;

    [JsonProperty("cached")]
    public bool Cached { get; set; }

    [JsonProperty("expires")]
    public string Expires { get; set; } = string.Empty;
}

public class CarParkDetail
{
    [JsonProperty("Name")]
    public string Name { get; set; } = string.Empty;

    [JsonProperty("Code")]
    public string Code { get; set; } = string.Empty;

    [JsonProperty("BookingURL")]
    public string BookingURL { get; set; } = string.Empty;

    [JsonProperty("MoreInfoURL")]
    public string MoreInfoURL { get; set; } = string.Empty;

    [JsonProperty("_latitude")]
    public double Latitude { get; set; }

    [JsonProperty("_longitude")]
    public double Longitude { get; set; }

    [JsonProperty("NonDiscPrice")]
    public decimal NonDiscPrice { get; set; }

    [JsonProperty("TotalPrice")]
    public decimal TotalPrice { get; set; }

    [JsonProperty("GatePrice")]
    public string GatePrice { get; set; } = string.Empty;
}



