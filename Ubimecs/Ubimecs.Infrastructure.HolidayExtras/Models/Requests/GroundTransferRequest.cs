namespace Ubimecs.Infrastructure.HolidayExtras.Models.Requests;

public class GroundTransferRequest
{
    public DateTime FromDate { get; set; }
    public string FromTime { get; set; }
    public DateTime ReturnDate { get; set; }
    public string ReturnTime { get; set; }
    public int AdultCount { get; set; }
    public int ChildCount { get; set; }
    public int InfantCount { get; set; }
    
    public string CreateQueryString(DateTime fromDate,string fromTime,DateTime returnDate, 
        string returnTime, int adultCount, int childCount, int infantCount)
    {
        var queryParams = new Dictionary<string, string>
        {
            { "FromDate", fromDate.ToString("yyyy-MM-dd") },
            { "FromTime", String.Join("", fromTime.Split(':')) },
            { "ReturnDate", returnDate.ToString("yyyy-MM-dd") },
            { "ReturnTime", String.Join("", returnTime.Split(':')) },
            {"PickUp","AGP"},
            {"PickUpType","IATA"},
            {"DropOff","85101062"},
            {"DropOffType","TTI"}
        };
        if (adultCount > 0)
        {
            queryParams["AdultCount"] = adultCount.ToString();
        }
        if (childCount > 0)
        {
            queryParams["ChildCount"] = childCount.ToString();
        }

        if (infantCount > 0)
        {
            queryParams["InfantCount"] = infantCount.ToString();
        }
        return string.Join("&", queryParams.Select(kvp => $"{kvp.Key}={kvp.Value}"));
    }
    
}