namespace Ubimecs.Infrastructure.Mailing.Models.IbsNative;

public class SendScheduleChangeDto
{
    public string? ActionCode { get; set; } 
    
    public string? DelayTime { get; set; }
    
    public string? ReasonCode { get; set; } 
    
    public string? ReasonMessage { get; set; }
    public string? PNRNo { get; set; } 
    
    public string? Email { get; set; } 
    
    public AffectedBookingInfoDto AffectedBookingInfo { get; set; } = new();
}

public class AffectedBookingInfoDto
{
    public FlightInfoDto OriginalFlightInfo { get; set; } = new();
    public ReAccomodatedBookingInfoDto ReAccomodatedBookingInfo { get; set; }

}

public class SegmentInfoDto
{
    public string? ArrDate { get; set; }
    public string? ArrTime { get; set; }
    public string? BrdPoint { get; set; }
    public string? DepDate { get; set; }
    public string? DepTime { get; set; }
    public string? FltNo { get; set; }
    public string? OffPoint { get; set; }
    public string? Stops { get; set; }
}

public class ReAccomodatedBookingInfoDto
{
    public PnrInfoDto Pnrinfo { get; set; }
    public string? Route { get; set; }
    public SegmentInfoDto SegmentInfo { get; set; }
}

public class PnrInfoDto
{
    public ContactsDto Contacts { get; set; }
    public CustomerInfoDto CustomerInfo { get; set; }
    public string? PnrNo { get; set; }
    public string? PointOfsale { get; set; }
}

public class ContactsDto
{
    public string? AlternateEmail { get; set; }
    public string? ContactType { get; set; }
    public string? Email { get; set; }
    public string? IsPreferredContact { get; set; }
    public string? PhoneNo { get; set; }
}

public class CustomerInfoDto
{
    public List<PaxDetailDto> PaxDetails { get; set; }
}

public class PaxDetailDto
{
    public string? GivenName { get; set; }
    public string? PaxID { get; set; }
    public string? SurName { get; set; }
    public string? TicketNo { get; set; }
}
