
@{
Layout = null;

// Currency symbol logic
string currencySymbol = (Model.Currency ?? "EUR").ToUpper() switch
{
"TRY" => "₺",
"USD" => "$",
"EUR" => "€",
"GBP" => "£",
_ => ""
};

// Trip type detection
bool isRoundTrip = !string.IsNullOrWhiteSpace(Model.InboundDate);
}

<div style="font-family: Arial, sans-serif; background-color: #1a4480; color: white; padding: 40px 24px;">
    <div style="position: relative; z-index: 2;">
        <h1 style="font-weight: 600; font-size: 36px; line-height: 44px; margin: 0; color: white;">Booking Confirmation</h1>
    </div>
    <div style="margin-top: 20px; position: relative; z-index: 2;">
        <p style="font-weight: 400; font-size: 36px; line-height: 44px; margin: 0; color: white;">Have a good trip!</p>
    </div>
    <div style="margin-top: 80px; display: flex; flex-wrap: wrap; width: 100%; position: relative; z-index: 2; gap: 40px;">
        <div style="width: 40%;">
            <p style="font-weight: 600; font-size: 16px; line-height: 20px; margin: 0; color: white;">@(Model.FullName ?? "Guest")</p>
            <p style="font-weight: 600; font-size: 16px; line-height: 20px; margin: 5px 0 0 0; color: white;">@(Model.Address ?? "-")</p>
            <p style="font-weight: 600; font-size: 16px; line-height: 20px; margin: 5px 0 0 0; color: white;">@(Model.PostalCode ?? "-") @(Model.City ?? "-"), @(Model.Country ?? "-")</p>
        </div>
        <div style="width: calc(60% - 40px);">
            <div style="display: flex; margin-bottom: 15px;">
                <p style="font-weight: 400; font-size: 16px; line-height: 20px; margin: 0; width: 180px; color: white;">Booking date:</p>
                <p style="font-weight: 400; font-size: 16px; line-height: 20px; margin: 0; color: white;">@(Model.BookingDate ?? "-")</p>
            </div>
            <div style="display: flex; margin-bottom: 15px;">
                <p style="font-weight: 400; font-size: 16px; line-height: 20px; margin: 0; width: 180px; color: white;">Booking number:</p>
                <p style="font-weight: 400; font-size: 16px; line-height: 20px; margin: 0; color: white;">@(Model.PnrNumber ?? "-")</p>
            </div>
            <div style="display: flex;">
                <p style="font-weight: 400; font-size: 16px; line-height: 20px; margin: 0; width: 180px; color: white;">Issued by:</p>
                <p style="font-weight: 400; font-size: 16px; line-height: 20px; margin: 0; color: white;">@(Model.IssuedBy ?? "Online Booking System")</p>
            </div>
        </div>
    </div>
</div>


<div style="font-family: Arial, sans-serif; padding: 24px;">
    <div style="display: flex; align-items: flex-start; margin-bottom: 40px;">
        <div style="margin-right: 16px;">
            <div style="width: 80px; height: 60px; border: 2px solid #1a4480; border-radius: 8px; display: flex; justify-content: center; align-items: center; padding: 10px;">
                <img src="https://s3-freebird-bucket.s3.eu-west-1.amazonaws.com/mail-template-images/union.png" alt="Travel Document" style="width: 50px; height: 40px;" />
            </div>
        </div>
        <div>
            <h1 style="color: #1a4480; font-weight: 600; font-size: 24px; line-height: 32px; margin: 0 0 16px 0;">Your Travel Document</h1>
            <p style="color: #1a4480; font-weight: 400; font-size: 16px; line-height: 24px; margin: 0;">Thank you for choosing @Model.AirlineName. Your flight booking is confirmed. Please find attached the copy of your ticket.</p>
        </div>
    </div>

    <div style="margin-top: 40px;">
        <h2 style="color: #000000; font-weight: 600; font-size: 20px; line-height: 28px; margin: 0 0 24px 0;">Print Travel Documents</h2>

        <div style="display: flex; flex-wrap: wrap; gap: 8px; width: 100%;">
            <div style="background-color: #F1F4F7; border-radius: 8px; padding: 20px; min-width: 200px;">
                <p style="color: #1a4480; font-weight: 600; font-size: 16px; line-height: 24px; margin: 0 0 8px 0;">All Tickets</p>
                <a href="@Model.AllTicketsUrl" style="color: #1a4480; font-weight: 600; font-size: 16px; line-height: 24px; text-decoration: none; display: flex; align-items: center;">
                    Download
                    <img src="https://s3-freebird-bucket.s3.eu-west-1.amazonaws.com/mail-template-images/download.png" alt="Download" style="width: 16px; height: 16px; margin-left: 8px;" />
                </a>
            </div>

            @if (Model.PassengerFares != null && Model.PassengerFares.Count > 0)
            {
            @foreach(var passengerFare in Model.PassengerFares)
            {
            <div style="background-color: #F1F4F7; border-radius: 8px; padding: 20px; min-width: 200px;">
                <p style="color: #1a4480; font-weight: 600; font-size: 16px; line-height: 24px; margin: 0 0 8px 0;">@passengerFare.PassengerName</p>
                <a href="@(Model.TicketUrl1 ?? "#")" style="color: #1a4480; font-weight: 600; font-size: 16px; line-height: 24px; text-decoration: none; display: flex; align-items: center;">
                Download
                <img src="https://s3-freebird-bucket.s3.eu-west-1.amazonaws.com/mail-template-images/download.png" alt="Download" style="width: 16px; height: 16px; margin-left: 8px;" />
                </a>
            </div>
            }
            }
        </div>
    </div>
</div>


<div style="font-family: Arial, sans-serif; padding: 24px;">
    <h1 style="color: #1a4480; font-weight: 600; font-size: 28px; line-height: 36px; margin: 0 0 24px 0;">Passengers</h1>

    @if (Model.PassengerFares != null && Model.PassengerFares.Count > 0)
    {
    @foreach(var passengerFare in Model.PassengerFares)
    {
    <div style="background-color: #ffffff; border: 2px solid #EBF0F5; border-radius: 12px; padding: 24px; margin-bottom: 16px;">
        <div style="display: flex; align-items: center; margin-bottom: 16px;">
            <div style="margin-right: 16px;">
                <img src="https://s3-freebird-bucket.s3.eu-west-1.amazonaws.com/mail-template-images/avatar.png" alt="User Icon" style="width: 48px; height: 48px;" />
            </div>
            <div>
                <h2 style="color: #4A5568; font-weight: 600; font-size: 20px; line-height: 28px; margin: 0;">@passengerFare.PassengerName</h2>
            </div>
        </div>

        <div style="display: flex; flex-wrap: wrap;">
            <div style="width: 200px; margin-right: 24px; margin-bottom: 8px;">
                <p style="color: #718096; font-weight: 400; font-size: 16px; line-height: 24px; margin: 0 0 4px 0;">Fare Type</p>
                <p style="color: #4A5568; font-weight: 600; font-size: 18px; line-height: 24px; margin: 0;">@(passengerFare.FareType ?? "-")</p>
            </div>
            <div style="width: 200px; margin-bottom: 8px;">
                <p style="color: #718096; font-weight: 400; font-size: 16px; line-height: 24px; margin: 0 0 4px 0;">Passenger Name</p>
                <p style="color: #4A5568; font-weight: 600; font-size: 18px; line-height: 24px; margin: 0;">@passengerFare.PassengerName</p>
            </div>
        </div>
    </div>
    }
    }
    else
    {
    <div style="background-color: #ffffff; border: 2px solid #EBF0F5; border-radius: 12px; padding: 24px; margin-bottom: 16px;">
        <p style="color: #4A5568; font-weight: 400; font-size: 16px; line-height: 24px; margin: 0;">No passenger information available.</p>
    </div>
    }
</div>


<div style="font-family: Arial, sans-serif; padding: 24px;">
    <h1 style="color: #1a4480; font-weight: 600; font-size: 28px; line-height: 36px; margin: 0 0 24px 0;">Itinerary</h1>

    <div style="background-color: #ffffff; border: 2px solid #EBF0F5; border-radius: 12px; padding: 24px;">
        <div>
            <p style="color: #718096; font-weight: 400; font-size: 16px; line-height: 24px; margin: 0 0 4px 0;">Outbound Flight</p>
            <h2 style="color: #4A5568; font-weight: 600; font-size: 24px; line-height: 32px; margin: 0 0 24px 0;">@(Model.FlightDay ?? ""), @(Model.FlightDate ?? "")</h2>
        </div>

        <div style="display: flex; align-items: center; margin-bottom: 16px;">
            <div style="width: 150px;">
                <h3 style="color: #1a4480; font-weight: 600; font-size: 20px; line-height: 28px; margin: 0 0 4px 0;">@(Model.DepartureCity ?? "-") (@(Model.DepartureCode ?? "-"))</h3>
                <p style="color: #4A5568; font-weight: 600; font-size: 20px; line-height: 28px; margin: 0;">@(Model.DepartureTime ?? "-")</p>
            </div>

            <div style="margin: 0 24px;">
                <img src="https://s3-freebird-bucket.s3.eu-west-1.amazonaws.com/mail-template-images/arrow.png" alt="Arrow" style="width: 24px; height: 24px;" />
            </div>

            <div style="width: 150px;">
                <h3 style="color: #1a4480; font-weight: 600; font-size: 20px; line-height: 28px; margin: 0 0 4px 0;">@(Model.ArrivalCity ?? "-") (@(Model.ArrivalCode ?? "-"))</h3>
                <p style="color: #4A5568; font-weight: 600; font-size: 20px; line-height: 28px; margin: 0;">@(Model.ArrivalTime ?? "-")</p>
            </div>
        </div>

        <div>
            <p style="color: #718096; font-weight: 400; font-size: 16px; line-height: 24px; margin: 0 0 4px 0;">@(Model.AirlineName ?? "Airline") @(Model.FlightNumber ?? "")</p>
            <p style="color: #718096; font-weight: 400; font-size: 16px; line-height: 24px; margin: 0;">Operator: @(Model.OperatorName ?? Model.AirlineName ?? "Airline")</p>
        </div>
    </div>
</div>


<div style="font-family: Arial, sans-serif; padding: 24px;">
    <h1 style="color: #1a4480; font-weight: 600; font-size: 28px; line-height: 36px; margin: 0 0 24px 0;">Flight Summary</h1>

    <div style="background-color: #ffffff; border: 2px solid #EBF0F5; border-radius: 12px; padding: 24px;">
        <div style="display: flex; margin-bottom: 24px;">
            <!-- Outbound Column -->
            <div style="flex: 1; padding-right: 16px;">
                <p style="color: #718096; font-weight: 400; font-size: 16px; line-height: 24px; margin: 0 0 4px 0;">Outbound Flight</p>
                <h2 style="color: #4A5568; font-weight: 600; font-size: 20px; line-height: 28px; margin: 0 0 24px 0;">@(Model.OutboundDay ?? ""), @(Model.OutboundDate ?? "")</h2>

                @if (Model.PassengerFares != null && Model.PassengerFares.Count > 0)
                {
                @foreach (var passengerFare in Model.PassengerFares)
                {
                <!-- Passenger Fare -->
                <h3 style="color: #4A5568; font-weight: 600; font-size: 18px; line-height: 26px; margin: 0 0 16px 0;">@passengerFare.PassengerName</h3>

                <div style="display: flex; justify-content: space-between; margin-bottom: 12px; border-bottom: 1px solid #EBF0F5; padding-bottom: 12px;">
                    <span style="color: #4A5568; font-size: 14px;">Fare(@(passengerFare.FareType ?? "Economy"))</span>
                    <span style="color: #1a4480; font-weight: 600; font-size: 14px;">@(passengerFare.OutboundFarePrice ?? currencySymbol + "0.00")</span>
                </div>

                @foreach (var service in passengerFare.OutboundServices)
                {
                <div style="display: flex; justify-content: space-between; margin-bottom: 12px; border-bottom: 1px solid #EBF0F5; padding-bottom: 12px;">
                    <span style="color: #4A5568; font-size: 14px;">@service.ServiceName</span>
                    <span style="color: #1a4480; font-weight: 600; font-size: 14px;">@service.Price</span>
                </div>
                }
                }
                }


            </div>

            @if (isRoundTrip)
            {
            <!-- Inbound Column -->
            <div style="flex: 1; padding-left: 16px; border-left: 1px solid #EBF0F5;">
                <p style="color: #718096; font-weight: 400; font-size: 16px; line-height: 24px; margin: 0 0 4px 0;">Inbound Flight</p>
                <h2 style="color: #4A5568; font-weight: 600; font-size: 20px; line-height: 28px; margin: 0 0 24px 0;">@(Model.InboundDay ?? ""), @(Model.InboundDate ?? "")</h2>

                @if (Model.PassengerFares != null && Model.PassengerFares.Count > 0)
                {
                @foreach (var passengerFare in Model.PassengerFares)
                {
                <!-- Passenger Fare -->
                <h3 style="color: #4A5568; font-weight: 600; font-size: 18px; line-height: 26px; margin: 0 0 16px 0;">@passengerFare.PassengerName</h3>

                <div style="display: flex; justify-content: space-between; margin-bottom: 12px; border-bottom: 1px solid #EBF0F5; padding-bottom: 12px;">
                    <span style="color: #4A5568; font-size: 14px;">Fare(@(passengerFare.FareType ?? "Economy"))</span>
                    <span style="color: #1a4480; font-weight: 600; font-size: 14px;">@(passengerFare.InboundFarePrice ?? currencySymbol + "0.00")</span>
                </div>

                @foreach (var service in passengerFare.InboundServices)
                {
                <div style="display: flex; justify-content: space-between; margin-bottom: 12px; border-bottom: 1px solid #EBF0F5; padding-bottom: 12px;">
                    <span style="color: #4A5568; font-size: 14px;">@service.ServiceName</span>
                    <span style="color: #1a4480; font-weight: 600; font-size: 14px;">@service.Price</span>
                </div>
                }
                }
                }


            </div>
            }
        </div>

        <!-- Bottom Total Section -->
        <div>
            @if (!string.IsNullOrEmpty(Model.TotalFarePrice) && Model.TotalFarePrice != currencySymbol + "0.00")
            {
            <div style="display: flex; justify-content: space-between; margin-bottom: 12px; border-bottom: 1px solid #EBF0F5; padding-bottom: 12px;">
                <span style="color: #4A5568; font-size: 14px;">Fare</span>
                <span style="color: #1a4480; font-weight: 600; font-size: 14px;">@Model.TotalFarePrice</span>
            </div>
            }

            @if (!string.IsNullOrEmpty(Model.NonRefundablePrice) && Model.NonRefundablePrice != currencySymbol + "0.00")
            {
            <div style="display: flex; justify-content: space-between; margin-bottom: 12px; border-bottom: 1px solid #EBF0F5; padding-bottom: 12px;">
                <span style="color: #4A5568; font-size: 14px;">Non-Refundable Components</span>
                <span style="color: #1a4480; font-weight: 600; font-size: 14px;">@Model.NonRefundablePrice</span>
            </div>
            }

            @if (!string.IsNullOrEmpty(Model.CancellationFee1) && Model.CancellationFee1 != currencySymbol + "0.00")
            {
            <div style="display: flex; justify-content: space-between; margin-bottom: 12px; border-bottom: 1px solid #EBF0F5; padding-bottom: 12px;">
                <span style="color: #4A5568; font-size: 14px;">Ticket Cancellation Fee</span>
                <span style="color: #1a4480; font-weight: 600; font-size: 14px;">@Model.CancellationFee1</span>
            </div>
            }

            @if (!string.IsNullOrEmpty(Model.CancellationFee2) && Model.CancellationFee2 != currencySymbol + "0.00")
            {
            <div style="display: flex; justify-content: space-between; margin-bottom: 24px; border-bottom: 1px solid #EBF0F5; padding-bottom: 12px;">
                <span style="color: #4A5568; font-size: 14px;">Ticket Cancellation Fee</span>
                <span style="color: #1a4480; font-weight: 600; font-size: 14px;">@Model.CancellationFee2</span>
            </div>
            }

            <div style="display: flex; justify-content: space-between; font-weight: 700;">
                <span style="color: #1a4480; font-size: 18px;">Total Amount</span>
                <span style="color: #1a4480; font-size: 18px;">@(Model.TotalAmount ?? currencySymbol + "0.00")</span>
            </div>
        </div>
    </div>
</div>


<div style="font-family: Arial, sans-serif; padding: 24px;">
    <h1 style="color: #1a4480; font-weight: 600; font-size: 28px; line-height: 36px; margin: 0 0 24px 0;">Payment Details</h1>

    @if (Model.GuestPaymentDetails != null && Model.GuestPaymentDetails.Count > 0)
    {
    @foreach(var guestPaymentDetail in Model.GuestPaymentDetails)
    {
    <div style="background-color: #ffffff; border: 2px solid #EBF0F5; border-radius: 12px; padding: 24px;">
        <div style="display: flex; align-items: center; margin-bottom: 24px;">
            <div style="margin-right: 16px;">
                <img src="https://s3-freebird-bucket.s3.eu-west-1.amazonaws.com/mail-template-images/avatar.png" alt="User Icon" style="width: 48px; height: 48px;" />
            </div>
            <div>
                <h2 style="color: #4A5568; font-weight: 600; font-size: 20px; line-height: 28px; margin: 0;">@(guestPaymentDetail.CardHolderName ?? "Card Holder")</h2>
            </div>
        </div>

        <div>
            <!-- Table Header -->
            <div style="display: flex; border-bottom: 1px solid #EBF0F5; padding-bottom: 8px; margin-bottom: 16px;">
                <div style="width: 30%; padding-right: 16px;">
                    <p style="color: #718096; font-weight: 400; font-size: 16px; line-height: 24px; margin: 0;">Payment Method</p>
                </div>
                <div style="width: 15%; padding-right: 16px;">
                    <p style="color: #718096; font-weight: 400; font-size: 16px; line-height: 24px; margin: 0;">Installment</p>
                </div>
                <div style="width: 20%; padding-right: 16px;">
                    <p style="color: #718096; font-weight: 400; font-size: 16px; line-height: 24px; margin: 0;">Date</p>
                </div>
                <div style="width: 15%; padding-right: 16px;">
                    <p style="color: #718096; font-weight: 400; font-size: 16px; line-height: 24px; margin: 0;">Amount</p>
                </div>
                <div style="width: 20%;">
                    <p style="color: #718096; font-weight: 400; font-size: 16px; line-height: 24px; margin: 0;">Status</p>
                </div>
            </div>

            <!-- Payment Row Template - This will be repeated for each payment -->
            @if (guestPaymentDetail.PaymentRows != null && guestPaymentDetail.PaymentRows.Count > 0)
            {
            @foreach(var paymentRow in guestPaymentDetail.PaymentRows)
            {
            <div style="display: flex; align-items: center; margin-bottom: 24px;">
                <div style="width: 30%; padding-right: 16px;">
                    <p style="color: #4A5568; font-weight: 600; font-size: 16px; line-height: 24px; margin: 0 0 4px 0;">@(paymentRow.PaymentMethod ?? "Credit Card")</p>
                    <p style="color: #718096; font-weight: 400; font-size: 14px; line-height: 20px; margin: 0;">@(paymentRow.CardInfo?.CardMaskedNumber ?? "****")</p>
                </div>
                <div style="width: 15%; padding-right: 16px;">
                    <p style="color: #4A5568; font-weight: 600; font-size: 16px; line-height: 24px; margin: 0;">@(Model.Installment ?? "1")</p>
                </div>
                <div style="width: 20%; padding-right: 16px;">
                    <p style="color: #4A5568; font-weight: 600; font-size: 16px; line-height: 24px; margin: 0;">@(paymentRow.PaymentDate ?? "-")</p>
                </div>
                <div style="width: 15%; padding-right: 16px;">
                    <p style="color: #1a4480; font-weight: 600; font-size: 16px; line-height: 24px; margin: 0;">@(paymentRow.PaymentAmount ?? currencySymbol + "0.00")</p>
                </div>
                <div style="width: 20%;">
                    <p style="color: #4A5568; font-weight: 600; font-size: 16px; line-height: 24px; margin: 0;">@(paymentRow.PaymentStatus ?? "Pending")</p>
                </div>
            </div>
            }
            }

        </div>
    </div>
    }
    }
    else
    {
    <div style="background-color: #ffffff; border: 2px solid #EBF0F5; border-radius: 12px; padding: 24px;">
        <p style="color: #4A5568; font-weight: 400; font-size: 16px; line-height: 24px; margin: 0;">No payment information available.</p>
    </div>
    }
</div>


<div style="font-family: Arial, sans-serif; padding: 24px;">
    <h2 style="color: #718096; font-weight: 400; font-size: 18px; line-height: 26px; margin: 0 0 8px 0;">Customize your trip</h2>
    <h1 style="color: #1a4480; font-weight: 600; font-size: 28px; line-height: 36px; margin: 0 0 24px 0;">Additional extras</h1>

    <div style="background-color: #ffffff; border: 2px solid #EBF0F5; border-radius: 12px; padding: 24px;">
        <div style="display: flex; margin-bottom: 40px;">
            <div style="width: 50%;">
                <h3 style="color: #1a4480; font-weight: 600; font-size: 20px; line-height: 28px; margin: 0 0 20px 0;">Seat reservation</h3>

                @foreach (var feature in Model.SeatFeatures)
                {
                <div style="margin-bottom: 12px; display: flex; align-items: center;">
                    <img src="https://s3-freebird-bucket.s3.eu-west-1.amazonaws.com/mail-template-images/vector.png" alt="Checkmark" style="width: 20px; height: 20px; margin-right: 8px;" />
                    <span style="color: #4A5568; font-size: 16px; line-height: 24px;">@feature.FeatureText</span>
                </div>
                }

                <a href="@Model.SeatReservationUrl" style="display: inline-block; color: #E67E22; font-weight: 500; font-size: 16px; line-height: 24px; text-decoration: none; padding: 12px 24px; border: 1px solid #E4E7EB; border-radius: 8px;">
                    @Model.SeatButtonText <img src="https://s3-freebird-bucket.s3.eu-west-1.amazonaws.com/mail-template-images/arrow.png" alt="→" style="width: 16px; height: 16px; margin-left: 8px; vertical-align: middle;" />
                </a>
            </div>

            <div style="width: 50%;">
                <img src="https://s3-freebird-bucket.s3.eu-west-1.amazonaws.com/mail-template-images/seat.png" alt="Seat Map" style="width: 100%; height: auto;" />
            </div>
        </div>

        <div style="display: flex; margin-bottom: 40px;">
            <div style="width: 50%;">
                <h3 style="color: #1a4480; font-weight: 600; font-size: 20px; line-height: 28px; margin: 0 0 20px 0;">Luggage</h3>

                @foreach (var feature in Model.LuggageFeatures)
                {
                <div style="margin-bottom: 12px; display: flex; align-items: center;">
                    <img src="https://s3-freebird-bucket.s3.eu-west-1.amazonaws.com/mail-template-images/vector.png" alt="Checkmark" style="width: 20px; height: 20px; margin-right: 8px;" />
                    <span style="color: #4A5568; font-size: 16px; line-height: 24px;">@feature.FeatureText</span>
                </div>
                }

                <a href="@Model.LuggageUrl" style="display: inline-block; color: #E67E22; font-weight: 500; font-size: 16px; line-height: 24px; text-decoration: none; padding: 12px 24px; border: 1px solid #E4E7EB; border-radius: 8px;">
                    @Model.LuggageButtonText <img src="https://s3-freebird-bucket.s3.eu-west-1.amazonaws.com/mail-template-images/arrow.png" alt="→" style="width: 16px; height: 16px; margin-left: 8px; vertical-align: middle;" />
                </a>
            </div>

            <div style="width: 50%;">
                <img src="https://s3-freebird-bucket.s3.eu-west-1.amazonaws.com/mail-template-images/luggage.png" alt="Luggage" style="width: 100%; height: auto;" />
            </div>
        </div>

        <div style="display: flex;">
            <div style="width: 50%;">
                <h3 style="color: #1a4480; font-weight: 600; font-size: 20px; line-height: 28px; margin: 0 0 20px 0;">Meals</h3>

                @foreach (var feature in Model.MealFeatures)
                {
                <div style="margin-bottom: 12px; display: flex; align-items: center;">
                    <img src="https://s3-freebird-bucket.s3.eu-west-1.amazonaws.com/mail-template-images/vector.png" alt="Checkmark" style="width: 20px; height: 20px; margin-right: 8px;" />
                    <span style="color: #4A5568; font-size: 16px; line-height: 24px;">@feature.FeatureText</span>
                </div>
                }

                <a href="@Model.MealUrl" style="display: inline-block; color: #E67E22; font-weight: 500; font-size: 16px; line-height: 24px; text-decoration: none; padding: 12px 24px; border: 1px solid #E4E7EB; border-radius: 8px;">
                    @Model.MealButtonText <img src="https://s3-freebird-bucket.s3.eu-west-1.amazonaws.com/mail-template-images/arrow.png" alt="→" style="width: 16px; height: 16px; margin-left: 8px; vertical-align: middle;" />
                </a>
            </div>

            <div style="width: 50%;">
                <img src="https://s3-freebird-bucket.s3.eu-west-1.amazonaws.com/mail-template-images/meal.png" alt="Meal" style="width: 100%; height: auto;" />
            </div>
        </div>
    </div>
</div>


<div style="font-family: Arial, sans-serif; background-color: #f0f7ff; border-radius: 12px; padding: 24px; margin-top: 24px;">
    <p style="color: #4A5568; font-weight: 500; font-size: 18px; line-height: 26px; margin: 0 0 16px 0;">Dear Passenger,</p>

    <p style="color: #4A5568; font-weight: 400; font-size: 16px; line-height: 24px; margin: 0 0 24px 0;">Thank you for choosing @Model.AirlineName.</p>

    <p style="color: #4A5568; font-weight: 400; font-size: 16px; line-height: 24px; margin: 0;">
        Please be at the airport at least @Model.HoursBeforeTime prior to departure for @(Model.isInternational == true ? "international flight" : "domestic flight").
    </p>
</div>
