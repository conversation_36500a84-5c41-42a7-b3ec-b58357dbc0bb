using System.Configuration;
using Ubimecs.Domain.Entities.UbimecsEntities;
using Ubimecs.Domain.Entities.UserEntities;
using Ubimecs.Domain.Entities.TorcEntities.ProcedureEntities;
using Ubimecs.Infrastructure.Caching.Models;
using Ubimecs.Infrastructure.Models.Common;
using Ubimecs.Infrastructure.Models.DTO;
using Ubimecs.Infrastructure.Models.Response;
using Ubimecs.Infrastructure.Models.Request;

namespace Ubimecs.Infrastructure.Caching
{
    public static class CachedData
    {
        public static Dictionary<string, string> Configs
        {
            get
            {
                return CacheManager.Instance.Get<Dictionary<string, string>>(CacheDataKeys.Configs);
            }
        }
        public static List<Country> Country
        {
            get
            {
                return CacheManager.Instance.Get<List<Country>>(CacheDataKeys.Countries);
            }
        }
        public static Dictionary<string, ServiceCategoryEnum> ServiceCodeCategories
        {
            get
            {
                return CacheManager.Instance.Get<Dictionary<string, ServiceCategoryEnum>>(CacheDataKeys.ServiceCodeCategories);
            }
        }

        public static List<LanguageDTO> LanguageList
        {
            get
            {
                return CacheManager.Instance.Get<List<LanguageDTO>>(CacheDataKeys.LanguageList);
            }
        }
        public static Dictionary<string, string> CurrencyCodes
        {
            get
            {
                return CacheManager.Instance.Get<Dictionary<string, string>>(CacheDataKeys.CurrencyCodes);
            }
        }
        public static Dictionary<string, object> SecurePayment
        {
            get
            {
                return CacheManager.Instance.Get<Dictionary<string, object>>(CacheDataKeys.CurrencyCodes);
            }
        }
        public static List<PageSetting> PageSettings
        {
            get
            {
                return CacheManager.Instance.Get<List<PageSetting>>(CacheDataKeys.PageSettings);
            }
        }
        public static AirportListResponse AirPortList
        {
            get
            {
                return CacheManager.Instance.Get<AirportListResponse>(CacheDataKeys.AirPorts);
            }
        }
        public static List<string> CheckInIFlyResControl
        {
            get
            {
                return CacheManager.Instance.Get<List<string>>(CacheDataKeys.CheckInIFlyResControl);
            }
        }
        public static List<string> RestrictedAirportControl
        {
            get
            {
                return CacheManager.Instance.Get<List<string>>(CacheDataKeys.RestrictedAirportsControl);
            }
        }
        public static Dictionary<string,string> CheckInTimeResControl
        {
            get
            {
                return CacheManager.Instance.Get<Dictionary<string,string>>(CacheDataKeys.CheckInTimeResControl);
            }
        }
        public static List<CheckInTimeResControlDTO> CheckInTimeResControl_V2
        {
            get
            {
                return CacheManager.Instance.Get<List<CheckInTimeResControlDTO>>(CacheDataKeys.CheckInTimeResControl_V2);
            }
        }
        public static List<OrderChannel> OrderChannel
        {
            get
            {
                return CacheManager.Instance.Get<List<OrderChannel>>(CacheDataKeys.OrderChannel);
            }
        }
        public static List<PassengerType> PassengerType
        {
            get
            {
                return CacheManager.Instance.Get<List<PassengerType>>(CacheDataKeys.PassengerType);
            }
        }
        public static List<PlcCountry> PlcCountries
        {
            get
            {
                return CacheManager.Instance.Get<List<PlcCountry>>(CacheDataKeys.PlcCountries);
            }
        }
        public static List<AppText> AppText
        {
            get
            {
                return CacheManager.Instance.Get<List<AppText>>(CacheDataKeys.AppText);
            }
        }
        public static Dictionary<int, List<ThirdPartyErrorCode>> ThirdPartyErrors
        {
            get
            {
                return CacheManager.Instance.Get<Dictionary<int, List<ThirdPartyErrorCode>>>(CacheDataKeys.ThirdPartyErrors);
            }
        }
        public static List<ErrorCode> ErrorCodes
        {
            get
            {
                return CacheManager.Instance.Get<List<ErrorCode>>(CacheDataKeys.ErrorCodes);
            }
        }
        public static List<Treshold> Tresholds
        {
            get
            {
                return CacheManager.Instance.Get<List<Treshold>>(CacheDataKeys.Tresholds);
            }
        }
        
        public static List<ChannelConfiguration> ChannelConfigurations
        {
            get
            {
                return CacheManager.Instance.Get<List<ChannelConfiguration>>(CacheDataKeys.ChannelConfigurations);
            }
        }
        public static int LastCMSVersion { get { return CacheManager.Instance.Get<int>(CacheDataKeys.LastCMSVersion); } }


        public static BINCheckResponse BINCheck(BINCheckRequest req)
        {
            BINCheckResponse resp;
            var binList = CacheManager.Instance.Get<List<BINList>>(CacheDataKeys.BINList);
            var bin = binList?.FirstOrDefault(a => a.BIN.Contains(req.CardBIN));
            var binShort = binList?.FirstOrDefault(x => x.BIN.Contains(req.CardBIN.Substring(0, 6)));
            if (bin == null && binShort == null)
            {
                if (Convert.ToInt32(req.CardBIN.Substring(0, 2)) >= 51 && Convert.ToInt32(req.CardBIN.Substring(0, 2)) <= 55)
                {
                    resp = new BINCheckResponse() { IsSupportInstallment = false, Organization = "Master", IsDomectic = false, CardImageUrl = ConfigurationManager.AppSettings["MasterCardPath"] };
                    return resp;
                }
                else if (Convert.ToInt32(req.CardBIN.Substring(0, 1)) == 4)
                {
                    resp = new BINCheckResponse() { IsSupportInstallment = false, Organization = "Visa", IsDomectic = false, CardImageUrl = ConfigurationManager.AppSettings["VisaCardPath"] };
                    return resp;
                }
                else
                {
                    resp = new BINCheckResponse() { IsSupportInstallment = false, IsDomectic = false};
                    return resp;
                }
            }
            else if (bin != null)
            {
                resp = new BINCheckResponse()
                {
                    IsSupportInstallment = bin.IsInstallmentSupported, 
                    CardType = bin.CardType, 
                    BankName = bin.BankName, 
                    Organization = bin.Organization,
                    IsDomectic = bin.IsDomesticCard
                };
                return resp;
            }
            else
            {
                resp = new BINCheckResponse()
                {
                    IsSupportInstallment = binShort.IsInstallmentSupported, 
                    CardType = binShort.CardType,
                    BankName = binShort.BankName,
                    Organization = binShort.Organization,
                    IsDomectic = bin.IsDomesticCard
                };
                return resp;
            }
        }

        public static bool CheckNationalIdentityNumber(long number)
        {
            if (number > *********** || number < ***********)
                return false;
            long Tmp1, Tmp;
            Tmp = number / 100;
            Tmp1 = number / 100;
            int odd_sum, even_sum, total, ChkDigit2, ChkDigit1;
            int[] NumArray = new int[9];
            for (int i = 8; i >= 0; i--)
            {
                NumArray[i] = (int)(Tmp1 % 10);
                Tmp1 = Tmp1 / 10;
            }
            odd_sum = NumArray[8] + NumArray[6] + NumArray[4] + NumArray[2] + NumArray[0];
            even_sum = +NumArray[7] + NumArray[5] + NumArray[3] + NumArray[1];
            total = odd_sum * 3 + even_sum;
            ChkDigit1 = (10 - (total % 10)) % 10;
            odd_sum = ChkDigit1 + NumArray[7] + NumArray[5] + NumArray[3] + NumArray[1];
            even_sum = NumArray[8] + NumArray[6] + NumArray[4] + NumArray[2] + NumArray[0];
            total = odd_sum * 3 + even_sum;
            ChkDigit2 = (10 - (total % 10)) % 10;
            Tmp = Tmp * 100 + ChkDigit1 * 10 + ChkDigit2;
            if (Tmp != number)
            {
                return false;
            }
            return true;
        }

        public static Dictionary<int, Dictionary<string, uSP_SelectAllClientTexts_Result>> CmsKeys
        {
            get
            {
                return CacheManager.Instance.Get<Dictionary<int, Dictionary<string, uSP_SelectAllClientTexts_Result>>>(CacheDataKeys.CmsKeys);
            }
        }
        public static string GetCMS(int languageId, string key)
        {
            var text = CacheManager.Instance.Get<uSP_SelectAllClientTexts_Result>(CacheDataKeys.GetCMsKeyName(key, languageId));
            return text?.Value ?? "NO_CMS";
        }
        public static string GetCMS(LanguageEnum languageId, string key)
        {
            return GetCMS((int)languageId, key);
        }

        public static List<BestOfferDTO> BestOffers
        {
            get
            {
                return CacheManager.Instance.Get<List<BestOfferDTO>>(CacheDataKeys.BestOffers)
                    .Where(w => AirPortList.Airports.Any(y => y.AirportCode == w.DepartureAirport) && AirPortList.Airports.Any(y => y.AirportCode == w.DepartureAirport)).ToList();
            }
        }


        #region Utility Methods

        public static Dictionary<string, string> GetCountries(int languageId)
        {
            return AppText
                .Where(w => w.LanguageId == languageId && w.GroupName.Equals("Country"))
                .GroupBy(g => g.Key)
                .ToDictionary(k => k.Key, v => v.FirstOrDefault()?.Value);
        }

        public static Dictionary<string, string> GetCities(int languageId)
        {
            return AppText
                .Where(w => w.LanguageId == languageId && w.GroupName.Equals("City"))
                .GroupBy(g => g.Key)
                .ToDictionary(k => k.Key, v => v.FirstOrDefault()?.Value);
        }

        public static string GetCountryCodeByAirportCode(string airportCode)
        {
            if (string.IsNullOrEmpty(airportCode))
            {
                return "";
            }
            else
            {
                var airports = AirPortList.Airports;
                return airports.FirstOrDefault(f => f.AirportCode == airportCode)?.CountryCode ?? "";
            }
        }

        public static string GetCountryCode(string CurrencyCode)
        {
            if (string.IsNullOrEmpty(CurrencyCode)) return "TR";
            return CurrencyCodes.ContainsKey(CurrencyCode) ? CurrencyCodes[CurrencyCode] : "TR";
        }

        public static string GetCurrency(string DepartureCode)
        {
            var CountryCode = AirPortList?.Airports.Count > 0 ? AirPortList?.Airports?.Where(x => x.AirportCode == DepartureCode).FirstOrDefault().CountryCode : "TR";
            return CurrencyCodes.ContainsValue(CountryCode) ? CurrencyCodes.FirstOrDefault(x => x.Value.Equals(CountryCode)).Key : "TRY";
        }

        public static Dictionary<string, string> GetCurrencyListByLanguage(int languageId)
        {
            Dictionary<string, string> CurrencyList = new Dictionary<string, string>();
            var AppTextList = GetAppTextByLanguage(languageId) ?? null;

            if (AppTextList == null || AppText.Count == 0)
                return CurrencyCodes;

            foreach (var item in CurrencyCodes)
            {
                var translate = AppTextList.FirstOrDefault(x => x.Key.Equals(item.Key)).Value;
                CurrencyList.Add(item.Key, translate ?? item.Key);
            }

            return CurrencyList;
        }

        public static List<AppText> GetAppTextByLanguage(int languageID)
        {
            var ErrorCodes = new Dictionary<string, string>();

            return AppText.Where(x => x.LanguageId == languageID).ToList();
        }

        public static SortedDictionary<string, string> GetCalendarListByLanguage(int languageId)
        {
            SortedDictionary<string, string> CalendarList = new SortedDictionary<string, string>();
            var AppTextList = AppText.Where(x => x.LanguageId == languageId && x.GroupName.Equals("Calendar")).ToList();

            if (AppTextList == null || AppTextList.Count == 0)
                return CalendarList;

            foreach (var item in AppTextList)
            {
                if (!CalendarList.ContainsKey(item.Key))
                    CalendarList.Add(item.Key, item.Value);
            }

            return CalendarList;
        }

        public static Dictionary<string, string> GetRelationType()
        {
            Dictionary<string, string> RelationTypes = new Dictionary<string, string>();
            var relationTypeList = PageSettings.Where(x => x.page.Equals("relation_type")).ToList();

            if (relationTypeList != null && relationTypeList.Count > 0)
            {
                relationTypeList.Sort((x, y) => x.value.CompareTo(y.value));
                foreach (var item in relationTypeList)
                {
                    if (!RelationTypes.ContainsKey(item.key))
                        RelationTypes.Add(item.key, item.value);
                }
            }
            return RelationTypes;
        }

        public static List<PageSetting> PhoneCountryCodeList(int lang)
        {
            List<PageSetting> phoneCountryList = new List<PageSetting>();
            try
            {
                phoneCountryList = PageSettings?.Where(x => x.page.Equals("phone_country_code")).ToList();

            }
            catch (Exception)
            {
                throw;
            }
            return phoneCountryList;
        }

        public static string GetCountrThreLetterName(string countryCode)
        {
            if (string.IsNullOrEmpty(countryCode)) return string.Empty;

            return Country.FirstOrDefault(f => f.TwoLetterIsoCode == countryCode)?.ThreeLetterIsoCode;
        }

        public static string GetCountrThreLetterFromName(string countryName)
        {
            if (string.IsNullOrEmpty(countryName)) return string.Empty;

            return Country.FirstOrDefault(f => f.Name == countryName)?.ThreeLetterIsoCode;
        }

        public static string GetCountrCode(string countryThreeLetterName)
        {
            if (string.IsNullOrEmpty(countryThreeLetterName)) return null;

            return Country.FirstOrDefault(f => f.ThreeLetterIsoCode == countryThreeLetterName)?.TwoLetterIsoCode;
        }
        
        public static string GetCountryName(string countryCode)
        {
            if (string.IsNullOrEmpty(countryCode)) return null;

            return Country.FirstOrDefault(f => f.TwoLetterIsoCode == countryCode)?.Name;
        }

        public static string GetErrorMessage(string errorCode, int language)
        {
            if (string.IsNullOrEmpty(errorCode)) return null;

            var errors = ThirdPartyErrors;
            if (errors == null) return null;
            var error = errors[language].FirstOrDefault(f => f.Code == errorCode);
            if (error?.ShowEndUser == true)
            {
                return error?.Message;
            }
            else
            {
                return errors[language].FirstOrDefault(x => x.Code.Equals("tmob.error")).Message;

            }
        }

        public static string GetLanguageCode(LanguageEnum language)
        {
            return LanguageList.FirstOrDefault(f => f.Id == (int)language).CultureName;
        }

        public static SortedDictionary<string, string> GetHotLineNumbers()
        {
            SortedDictionary<string, string> List = new SortedDictionary<string, string>();
            var AppTextList = PageSettings.Where(x => x.page.Equals("call_our_hotlines_number")).ToList();

            if (AppTextList == null || AppTextList.Count == 0)
                return List;

            foreach (var item in AppTextList)
            {
                if (!List.ContainsKey(item.key))
                    List.Add(item.key, item.value);
            }

            return List;
        }

        public static string GetCountryName(string CountryCode, int languageId)
        {
            var CountryName = AppText.FirstOrDefault(x => x.LanguageId == languageId && x.GroupName.Equals("Country") && x.Key == CountryCode)?.Value;
            return CountryName ?? CountryCode;
        }

        public static ChannelConfiguration GetChannelConfiguration(EnvironmentTypeEnum environment, ChannelTypeEnum channel)
        {
            string environmentStr = environment.ToString();
            string channelStr = channel.ToString();

            return ChannelConfigurations.FirstOrDefault(c => 
                c.EnvironmentType.Equals(environmentStr, StringComparison.OrdinalIgnoreCase) && 
                c.ChannelType.Equals(channelStr, StringComparison.OrdinalIgnoreCase));
        }
        
        #endregion
    }
}
