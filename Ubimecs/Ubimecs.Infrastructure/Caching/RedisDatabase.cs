using Microsoft.Extensions.Options;
using StackExchange.Redis;
using System.Configuration;
using System.Diagnostics;
using Ubimecs.Infrastructure.Models.Common;
using Ubimecs.Infrastructure.Settings;

namespace Ubimecs.Infrastructure.Caching
{

    public sealed class RedisDatabase
    {
        private static ServiceEnvironmentTypeEnum _serviceEnvironment = (ServiceEnvironmentTypeEnum)Enum.Parse(typeof(ServiceEnvironmentTypeEnum), ConfigurationManager.AppSettings["SYSTEM_ENVIRONMENT"] ?? ServiceEnvironmentTypeEnum.Test.ToString(), true);
        private static string redisIpAddress;
        private static int redisPort;
        public IDatabase Database { get; set; }
        public IServer Server { get; set; }
        public RedisDatabase()
        {
            var option = new ConfigurationOptions
            {
                Ssl = false,
                ConnectTimeout = 500
            };

            Debug.WriteLine("redis_connect");


            option.EndPoints.Add(redisIpAddress, redisPort);


            option.AbortOnConnectFail = false;
            var connect = ConnectionMultiplexer.Connect(option);




            Database = connect.GetDatabase(0);

            Server = connect.GetServer(redisIpAddress, redisPort);

        }

        private static readonly object lockObj = new object();

        private static RedisDatabase instance = null;

        public static RedisDatabase Instance
        {
            get
            {
                if (instance == null)
                {
                    lock (lockObj)
                    {
                        if (instance == null)
                        {
                            instance = new RedisDatabase();
                        }
                    }
                }
                return instance;
            }
        }
    }
}
