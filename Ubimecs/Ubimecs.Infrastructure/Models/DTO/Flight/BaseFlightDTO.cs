using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Ubimecs.Infrastructure.Models.DTO.Flight
{
    public class BaseFlightDTO
    {
        public string Id { get; set; }
        public string DepartureCode
        {
            get
            {
                return Segments?.FirstOrDefault()?.DepartureCode;
            }
        }
        public string ArrivalCode
        {
            get
            {
                return Segments?.LastOrDefault()?.ArrivalCode;
            }
        }
        public TimeSpan JourneyTime { get; set; }
        public DateTime? FlightDate
        {
            get
            {
                if (Segments?.Count > 0)
                {
                    return Segments.Min(m => m.DepartureDate);
                }
                else
                {
                    return null;
                }
            }
        }

        //public bool IsFlown
        //{
        //    get
        //    {
        //        if (Segments?.Count > 0)
        //        {
        //            return Segments.Min(m => m.CompleteDepartureTime) <= DateTime.Now;
        //        }
        //        else
        //        {
        //            return false;
        //        }
        //    }
        //}
        public bool IsFlown { get; set; }
        public string CabinClass { get; set; }
        public List<FlightSegmentDTO> Segments { get; set; } = new List<FlightSegmentDTO>();
        public bool IsConnectedFlight
        {
            get
            {
                return Segments?.Count > 1;
            }
        }
        public string TmobId
        {
            get
            {
                return
                    DepartureCode + "|" +
                    ArrivalCode + "|" +
                    Segments?.FirstOrDefault()?.FlightNumber + "|" +
                    (Segments?.Count>1 ? Segments.LastOrDefault()?.FlightNumber+"|":"" ) +
                    Segments?.FirstOrDefault()?.AirlineId + "|" +
                    Segments?.FirstOrDefault()?.DepartureDate.ToString("dd.MM.yyyy") + "|" +
                    Segments?.FirstOrDefault()?.DepartureTime;
            }
        }

        public string SegmentRefs
        {
            get
            {
                return string.Join(" ", Segments.Select(s => s.Id));
            }
        }

        public decimal BasePrice { get; set; }
        public string BasePriceDescription { get; set; }
        public string TotalPriceDescription { get; set; }
        public string Title { get; set; }






    }
}
