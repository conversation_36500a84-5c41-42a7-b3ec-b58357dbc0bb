using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Ubimecs.Infrastructure.Models.Flow
{
    public enum RebookingTypeEnum
    {
        ChangeFlight,
        AddFlight,
        UpgradeBundle,
        CancelFlight,
        CancelPNR,        
        AddExtras,
        NameChange,
        TravelDocumentChange,
        ContantInfoChange,
        OptionalPNR,
        TourOperator,
        ChangeFlightNative
    }
}
