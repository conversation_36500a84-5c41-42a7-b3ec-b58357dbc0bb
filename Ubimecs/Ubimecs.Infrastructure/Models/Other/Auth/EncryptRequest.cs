using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Ubimecs.Infrastructure.Models.Other.Auth
{
    public class EncryptRequest
    {
        public string DeviceId { get; set; }
        public string DeviceIp { get; set; }
        public string SslThumbprint { get; set; }
        public string SslPin1 { get; set; }
        public string SslPin2 { get; set; }
    }
}
