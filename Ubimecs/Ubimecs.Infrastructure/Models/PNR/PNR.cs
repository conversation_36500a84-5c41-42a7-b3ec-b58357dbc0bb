using Ubimecs.Infrastructure.Models.DTO.Baggage;
using Ubimecs.Infrastructure.Models.DTO.Corona;
using Ubimecs.Infrastructure.Models.DTO.Flight;
using Ubimecs.Infrastructure.Models.DTO.Ife;
using Ubimecs.Infrastructure.Models.DTO.Meal;
using Ubimecs.Infrastructure.Models.DTO.Seat;
using Ubimecs.Infrastructure.Models.DTO.Golf;
using Ubimecs.Infrastructure.Models.Request;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Ubimecs.Infrastructure.Models.DTO.Bundle;
using Ubimecs.Infrastructure.Models.DTO.ExtraServices;
using Ubimecs.Infrastructure.Models.DTO.Flex;
using Ubimecs.Infrastructure.Models.DTO.GroundTransfer;
using Ubimecs.Infrastructure.Models.DTO.HolidayExtras;
using Ubimecs.Infrastructure.Models.DTO.Payment;
using Ubimecs.Infrastructure.Models.DTO.Passenger;
using Ubimecs.Infrastructure.Models.DTO.Service;

namespace Ubimecs.Infrastructure.Models.PNR
{
    public class PNR
    {
        public bool IsBooking
        {
            get
            {
                return string.IsNullOrEmpty(Number);
            }
        }
        public bool IsTourOperatorOperation { get; set; }
        public string Number { get; set; }
        public string Currency { get; set; }
        public decimal GiftVoucherAmount { get; set; } = decimal.Zero;
        public string? IbsSessionId { get; set; }
        public string? PromoCode { get; set; }
        public bool IsOptionalPNR { get; set; }
        public string PassengerRefs
        {
            get
            {
                if (Passengers?.Count > 0)
                {
                    return string.Join(" ", Passengers.Select(s => s.LocalId ?? s.GlobalId).ToArray());
                }
                else
                {
                    return "";
                }
            }
        }
        public PaymentInformation PaymentInfo { get; set; }
        public Dictionary<string,string> PassengerTypeDictionary
        {
            get
            {
                if (Passengers?.Count > 0)
                {
                    return Passengers.GroupBy(g => g.PassengerType).ToDictionary(k => k.Key.ToString(), v => string.Join(" ", v.Select(s => s.LocalId ?? s.GlobalId)));
                }
                else
                {
                    return new Dictionary<string, string>();
                }
            }
        }

        public List<Passenger> Passengers { get; set; } = new List<Passenger>();
        public List<SessionPnrFlightDTO> Flights { get; set; } = new List<SessionPnrFlightDTO>();
        public List<SegmentPassengerSeat> Seats { get; set; } = new List<SegmentPassengerSeat>();
        public List<PassengerSegmentBaggage> ExtraBaggages { get; set; } = new List<PassengerSegmentBaggage>();
        public List<PassengerFlightSportEquipment> SportEquipments { get; set; } = new List<PassengerFlightSportEquipment>();
        public List<PassengerFlightCorona> Coronas { get; set; } = new List<PassengerFlightCorona>();
        public List<PassengerFlightExtraService> ExtraServices { get; set; } = new List<PassengerFlightExtraService>();
        public List<PassengerFlightMeal> Meals { get; set; } = new List<PassengerFlightMeal>();
        public List<PassengerFlightIfe> IfeServices { get; set; } = new List<PassengerFlightIfe>();
        public List<PassengerFlightGolfBundle> GolfBundles { get; set; } = new List<PassengerFlightGolfBundle>();
        public List<RebookingTravelDocumentChangeRequest> TravelDocumentChangedList { get; set; } = new List<RebookingTravelDocumentChangeRequest>();
        public List<PassengerInformationDTO> GroupPassengers { get; set; } = new List<PassengerInformationDTO>();
        public List<PassengerFlexService> FlexServices { get; set; } = new List<PassengerFlexService>();
        public List<SessionBundleDTO> Bundles { get; set; } = new List<SessionBundleDTO>();
        public GroupInformation GroupInformation { get; set; } = new GroupInformation();
        public List<SessionCarParkingDTO> CarParkings { get; set; } = new List<SessionCarParkingDTO>();
        public List<SessionGroundTransferDTO> GroundTransfers { get; set; } = new List<SessionGroundTransferDTO>();
        public void ClearServices()
        {
            SportEquipments.Clear();
            Seats.Clear();
            ExtraBaggages.Clear();
            Coronas.Clear();
            Meals.Clear();
            IfeServices.Clear();
            GolfBundles.Clear();
            ExtraServices.Clear();
            FlexServices.Clear();
            Bundles.Clear();
            CarParkings.Clear();
            GroundTransfers.Clear();
        }
    }
}
