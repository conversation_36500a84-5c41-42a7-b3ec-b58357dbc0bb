using Ubimecs.Infrastructure.Models.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Ubimecs.Infrastructure.Models.Request
{
    public class RebookingNameChangeRequest
    {
        public List<PassengerInfo> Passengers { get; set; } = new List<PassengerInfo>();

        public class PassengerInfo
        {
            public string PassengerID { get; set; }
            public string PTC { get; set; }
            public DateTime? Birthdate { get; set; }
            public GenderTypeEnum Gender { get; set; }
            public string NameTitle { get; set; }
            public string GivenName { get; set; }
            public string Surname { get; set; }
            public string CountryCode { get; set; }
            public string IdentityNumber { get; set; }
            public string MemberShipNumber { get; set; }
            public string HesCode { get; set; }
        }
    }


}
