using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Ubimecs.Infrastructure.Models.Request
{
    public class RebookingReSelectSeatRequest
    {
        public List<SegmentSeat> SegmentSeats { get; set; }
        public class SegmentSeat
        {
            public string SegmentTmobId { get; set; }
            public List<PassengerSeatInfo> Seats { get; set; }

            public class PassengerSeatInfo
            {
                public string PassengerId { get; set; }
                public string Column { get; set; }
                public string Number { get; set; }
            }
        }             
    }
}
