using Ubimecs.Infrastructure.Models.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Ubimecs.Infrastructure.Models.Request
{
    public class RebookingTravelDocumentChangeRequest
    {
        public string TmobId { get; set; }
        public string PassengerID { get; set; }
        public GenderTypeEnum? Gender { get; set; }
        public string Title { get; set; }
        public string GivenName { get; set; }
        public string Surname { get; set; }
        public string DocumentNumber { get; set; }
        public string DocumentType { get; set; }
        public DateTime? DateOfExpiry { get; set; }
        public DateTime? BirthDate { get; set; }
        public string IssuingCountry { get; set; }
        public string CitizenshipCountryCode { get; set; }
        public string VisaNo { get; set; }
        public string VisaIssuedBy { get; set; }
        public string MemberShipNumber { get; set; }
        public string HesCode { get; set; }
        public DateTime? HesCodeDateTime { get; set; }
        public string BirthPlace { get; set; }
        public bool IsRemoved { get; set; }
        public string ResidenceCountryCode { get; set; }
        public bool GenderSpecified { get; set; }
        public string TravelDocumentId { get; set; }
    }


}
