using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Ubimecs.Infrastructure.Models.Request
{
    public class SetExtraBaggageRequest
    {
        public List<ExtraBaggageRequest> Baggages { get; set; } = new List<ExtraBaggageRequest>();
    }

    public class ExtraBaggageRequest
    {
        public string PassengerId { get; set; }
        public string TmobId { get; set; }
        public decimal ExtraWeight { get; set; }
    }
}
