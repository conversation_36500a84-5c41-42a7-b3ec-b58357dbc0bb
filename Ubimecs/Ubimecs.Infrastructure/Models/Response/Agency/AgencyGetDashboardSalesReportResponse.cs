namespace Ubimecs.Infrastructure.Models.Response.Agency;

public class AgencyGetDashboardSalesReportResponse
{
    public SalesReport WeeklySales { get; set; }
    public SalesReport MonthlySales { get; set; }
    public SalesReport YearlySales { get; set; }
    public int TotalSales { get; set; }
}

public class SalesReport
{
    public int Total { get; set; }
    public int PreviousTotal { get; set; }
    public string ChangePercentage { get; set; }
    public TrendDirection Trend => Total > PreviousTotal ? TrendDirection.Increase : Total < PreviousTotal ? TrendDirection.Decrease : TrendDirection.Stable;
    public List<ReportData> Data { get; set; } = new List<ReportData>();
}
public class ReportData
{
    public string PeriodTitle { get; set; }
    public string PeriodStartDate { get; set; }
    public string PeriodEndDate { get; set; }
    public List<TransactionDetail> TransactionDetails { get; set; } = new List<TransactionDetail>();
}
public class TransactionDetail
{
    public string PnrNumber { get; set; }
    public string PnrCreationDate { get; set; }
    public string TransactionDate { get; set; }
    public string PassengerName { get; set; }
    public int Age { get; set; }
    public string DepartureDate { get; set; }
    public string FlightNumber { get; set; }
    public string Origin { get; set; }
    public string Destination { get; set; }
    public string FareBasisCode { get; set; }
    public decimal BillAmount { get; set; }
    public decimal CancellationFee { get; set; }
    public string AgentId { get; set; }
}

public enum TrendDirection
{
    Increase,  // Artış
    Decrease,  // Azalış
    Stable     // Sabit
}