using Ubimecs.Infrastructure.Models.DTO.Flight;
using Ubimecs.Infrastructure.Models.DTO.Service;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Ubimecs.Infrastructure.Models.Response
{
    public class BasePriceResponse
    {
        public List<PriceFlightDTO> Flights { get; set; } = new List<PriceFlightDTO>();
        public string Currency { get; set; }
        private decimal _totalPrice;

        public decimal TotalPrice
        {
            get => _totalPrice;
            set { _totalPrice = value; }
        }

        public decimal InstallmentFeePrice { get; set; }
        public Prices PriceInfo { get; set; } = new Prices();


        public AllServices Services { get; set; } = new AllServices();

        public void SetTotalPriceManually(decimal totalPrice)
        {
            _totalPrice = totalPrice;
        }

        public class Prices
        {
            public decimal TotalAmountToBePaid { get; set; }
            public decimal TotalAmountPaid { get; set; }
            public decimal NonRefundableComponents { get; set; }
            
            public decimal CancellationFee { get; set; }
            public decimal TotalRefund { get; set; }
            public decimal TotalRebookFeeAmount { get; set; }
            public decimal ShownGiftVoucherAmount { get; set; }
            public string Currency { get; set; }
            public List<Price> PricesByCurrency { get; set; } = new();
            public List<FlightTaxDTO> TaxesAndSurcharges { get; set; } = new();
        }
        
        public class Price
        {
            public decimal Amount { get; set; }
            public string Currency { get; set; }
        }
    }
}