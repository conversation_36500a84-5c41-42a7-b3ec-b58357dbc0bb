using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Ubimecs.Infrastructure.Models.Response
{
    public class BundleResponse
    {
        public List<AncillaryServiceDTO> AncillaryServices { get; set; }        
    }

    public class AncillaryServiceDTO
    {
        public string ServiceCode { get; set; }
        public string ServiceName { get; set; }
        public string Category { get; set; }
        public bool IsBundle { get; set; }
        public string Priority { get; set; }
        public bool IsEnabled { get; set; } = false;
        public List<IncludedAncillaryDTO> IncludedAncillaries { get; set; }
        public FeeInformationDTO FeeInformation { get; set; }
    }

    public class IncludedAncillaryDTO
    {
        public string ServiceCode { get; set; }
        public string ServiceName { get; set; }
        public string Category { get; set; }
    }

    public class FeeInformationDTO
    {
        public double Price { get; set; }
        public string Currency {  get; set; }
    }
}
