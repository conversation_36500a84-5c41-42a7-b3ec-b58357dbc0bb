using Ubimecs.Infrastructure.Models.DTO.Loyalty;

namespace Ubimecs.Infrastructure.Models.Response
{
    public class GetBaggageAllowanceResponse
    {
        public Dictionary<string, decimal> PassengersFreeBagAllowances { get; set; } = new Dictionary<string, decimal>();
        public Dictionary<string, decimal> PassengersMaxBagAllowances { get; set; } = new Dictionary<string, decimal>();
       
        public Dictionary<string, decimal> PassengersExtraBagAllowances { get; set; } = new Dictionary<string, decimal>();
        public int? FirstStepValue {get; set;}
        public int? StepCounter {get; set;}
        public List<RedeemBonusPoint> BonusPoints { get; set; } = new List<RedeemBonusPoint>();
    }
}
