using Ubimecs.Infrastructure.Models.DTO.Payment;

namespace Ubimecs.Infrastructure.Models.Response
{
    public class GetInvoiceFieldMetadataResponse
    {
        public List<TaxInvoiceDetail> TaxInvoiceDetail { get; set; }
    }

    public class TaxInvoiceDetail
    {
        public string CountryCode { get; set; }
        public string DefaultInvoiceType { get; set; }
        public List<TaxInvoiceDTO> TaxInvoice { get; set; }
    }

    public class TaxInvoiceDTO
    {
        public string TaxInvoiceType { get; set; }
        public InvoiceTypeEnum InvoiceType { get; set; }
        public List<TaxInvoiceFieldDTO> TaxInvoiceFields { get; set; }
    }

    public class TaxInvoiceFieldDTO
    {
        public string FieldName { get; set; }
    }
}