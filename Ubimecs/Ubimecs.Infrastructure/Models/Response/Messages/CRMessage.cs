using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Ubimecs.Infrastructure.Models.Response.Messages
{
    public class CRMessage : FMessageBase
    {
        public Dictionary<string, object> Params { get; private set; }

        public CRMessage(PriorityLevel priority)
        {
            this.Params = new Dictionary<string, object>();
            this.Priority = priority;
            this.SetDirty(false);
        }

        public void SetCryptoKey(string cryptoKey)
        {
            Params[Keys.CryptoKey] = cryptoKey;
            this.SetDirty(true);
        }

        public void SetCallInit(bool callInit)
        {
            Params[Keys.CallInit] = callInit;
            this.SetDirty(true);
        }

        public void SetTimeout(int timeOutValue)
        {
            Params[Keys.TimeoutValue] = timeOutValue.ToString();
            this.SetDirty(true);
        }

        public void NavigateToPage(int pageIdentifier)
        {
            Params[Keys.NavigateToPage] = pageIdentifier;
            this.SetDirty(true);
        }

        class Keys
        {
            public const string CallInit = "CallInit";
            public const string TimeoutValue = "TimeoutValue";
            public const string CryptoKey = "CryptoKey";
            public const string NavigateToPage = "NavigateToPage";
        }


    }
}
