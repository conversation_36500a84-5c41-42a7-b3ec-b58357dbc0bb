using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Ubimecs.Infrastructure.Models.Response
{
    public class PrintBoardingPassResponse
    {
        public string SegmentTmobId { get; set; }
        public List<BoardingPassInfoDTO> BoardingPasses { get; set; } = new List<BoardingPassInfoDTO>();

        public class BoardingPassInfoDTO
        {
            public string FirstName { get; set; }
            public string LastName { get; set; }
            public string GuestName { get; set; }
            public string PnrNumber { get; set; }
            public string PaxKey { get; set; }
            public BoardingPassFlightDTO FlightInformation { get; set; }
            public BoardingPassSeatDTO SeatInformation { get; set; }
            public string GateNumber { get; set; }
            public string Terminal { get; set; }
            public DateTime BoardingTime { get; set; }
            public DateTime ArrivalTime { get; set; }
            public DateTime DepartureTime { get; set; }
            public string BoardingHour { get; set; }
            public string ArrivalHour { get; set; }
            public string DepartureHour { get; set; }
            public string Barcode { get; set; }
            public string IFEAccessCode{ get; set; }
            public BoardingPassAirportDTO BoardpointInformation { get; set; }
            public BoardingPassAirportDTO OffpointInformation { get; set; }
        }


        public class BoardingPassAirportDTO
        {
            public string AirportCode { get; set; }
            public string AirportName { get; set; }
            public string CityName { get; set; }
        }

        public class BoardingPassFlightDTO
        {
            public string FlightNumber { get; set; }
            public string FlightSuffix { get; set; }
            public string AirlineCode { get; set; }
            public string CarrierCode { get; set; }
            public DateTime FlightDate { get; set; }
        }

        public class BoardingPassSeatDTO
        {
            public string OldSeatNumber { get; set; }
            public string NewSeatNumber { get; set; }
            public string FlightKey { get; set; }
        }
    }
}
