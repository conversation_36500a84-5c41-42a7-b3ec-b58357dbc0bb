using Ubimecs.Infrastructure.Models.Common;
using Ubimecs.Infrastructure.Models.DTO.Flight;
using Ubimecs.Infrastructure.Models.DTO.Service;
using Ubimecs.Infrastructure.Models.DTO.Service.ServiceList;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Ubimecs.Infrastructure.Models.Response
{
    public class ServicesResponse
    {
        public Dictionary<ServiceCategoryEnum, List<object>> Services { get; set; } = Enum.GetValues(typeof(ServiceCategoryEnum)).Cast<ServiceCategoryEnum>().ToDictionary(k => k, v => new List<object>());
        public bool IsValid { get; set; }
    }
}
