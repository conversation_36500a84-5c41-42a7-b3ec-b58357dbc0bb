using Ubimecs.Infrastructure.Contracts.ChannelConfig;
using Ubimecs.Domain.Entities.UbimecsEntities;
using Ubimecs.Infrastructure.Caching;
using Ubimecs.Infrastructure.Models.Common;

namespace Ubimecs.Infrastructure.Providers;

public class ChannelConfigurationProvider : IChannelConfigurationProvider
{
    private readonly SessionCache _session;

    public ChannelConfigurationProvider(SessionCache session)
    {
        _session = session;
    }

    public ChannelConfiguration Get(ChannelTypeEnum channelType)
    {
        return CachedData.GetChannelConfiguration(_session.EnvironmentType, channelType);
    }
}