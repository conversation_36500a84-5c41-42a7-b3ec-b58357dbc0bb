using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Ubimecs.Infrastructure.Settings
{
    public class AuthSettings
    {
        public string SecretKey { get; set; }
        public string Issuer { get; set; }
        public string Audience { get; set; }
        public int TokenExpiryInMinutes { get; set; }
        public int RefreshTokenExpireInMinutes { get; set; }
        public string JwtSecretKey { get; set; }
        public bool UseJwtAuthentication { get; set; }
        public bool UseRateLimiting { get; set; }
        public int RateLimitTimeWindow { get; set; } //1 (1 dk içerisinde gelen request)
        public int MaxRateLimitRequestCount { get; set; } // 100 (izin verilen request sayısı)
        public int BlockingRateLimitTime { get; set; } // 5 (Bloklanma süresi (dk))
    }
}
