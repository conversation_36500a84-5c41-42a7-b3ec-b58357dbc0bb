<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="ClosedXML" Version="0.105.0-rc" />
    <PackageReference Include="log4net" Version="3.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.2.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.8" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.8" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="8.0.2" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="NodaTime" Version="3.1.12" />
    <PackageReference Include="RestSharp" Version="112.0.0" />
    <PackageReference Include="StackExchange.Redis" Version="2.8.16" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Ubimecs.Domain\Ubimecs.Domain.csproj" />
    <ProjectReference Include="..\Ubimecs.Infrastructure.CMS\Ubimecs.Infrastructure.CMS.csproj" />
    <ProjectReference Include="..\Ubimecs.Infrastructure.CRM\Ubimecs.Infrastructure.CRM.csproj" />
    <ProjectReference Include="..\Ubimecs.Infrastructure.CSM\Ubimecs.Infrastructure.CSM.csproj" />
    <ProjectReference Include="..\Ubimecs.Infrastructure.FileService\Ubimecs.Infrastructure.FileService.csproj" />
    <ProjectReference Include="..\Ubimecs.Infrastructure.Ftp\Ubimecs.Infrastructure.Ftp.csproj" />
    <ProjectReference Include="..\Ubimecs.Infrastructure.HolidayExtras\Ubimecs.Infrastructure.HolidayExtras.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="Models\Configuration\ChannelConfiguration.cs" />
  </ItemGroup>

</Project>
